from sqlalchemy.orm import (
    Mapped,
    mapped_column,
)

from sqlalchemy import Foreign<PERSON>ey
from .base import SQLModel
import datetime


class SensorModuleState(SQLModel):
    __tablename__ = "sensor_module_states"
    __table_args__ = {"schema": "dbo"}

    id: Mapped[int] = mapped_column("id", primary_key=True)
    value: Mapped[int] = mapped_column("value")
    description: Mapped[str] = mapped_column("description")
    display_name: Mapped[str] = mapped_column("display_name")
    created_at: Mapped[datetime.datetime] = mapped_column("created_at")
    updated_at: Mapped[datetime.datetime] = mapped_column("updated_at")
    is_active: Mapped[bool] = mapped_column("is_active")


class SensorCategory(SQLModel):
    __tablename__ = "sensor_category"
    __table_args__ = {"schema": "dbo"}

    id: Mapped[int] = mapped_column("id", primary_key=True)
    name: Mapped[str] = mapped_column("name")
    created_at: Mapped[datetime.datetime] = mapped_column("created_at")
    updated_at: Mapped[datetime.datetime] = mapped_column("updated_at")
    is_active: Mapped[bool] = mapped_column("is_active")

class DeviceTypes(SQLModel):
    __tablename__ = "device_types"
    __table_args__ = {"schema": "dbo"}

    id: Mapped[int] = mapped_column("id", primary_key=True)
    name: Mapped[str] = mapped_column("name")
    value: Mapped[str] = mapped_column("value")
    created_at: Mapped[datetime.datetime] = mapped_column("created_at")
    updated_at: Mapped[datetime.datetime] = mapped_column("updated_at")
    is_active: Mapped[bool] = mapped_column("is_active")


class SensorModuleType(SQLModel):
    __tablename__ = "sensor_module_types"
    __table_args__ = {"schema": "dbo"}

    id: Mapped[int] = mapped_column("id", primary_key=True)
    value: Mapped[int] = mapped_column("value")
    description: Mapped[str] = mapped_column("description")
    unit: Mapped[str] = mapped_column("unit")
    category_id: Mapped[int] = mapped_column(ForeignKey("dbo.sensor_category.id"))
    display_name: Mapped[str] = mapped_column("display_name")
    alert_min: Mapped[float] = mapped_column("alert_min",nullable=True)
    alert_max: Mapped[float] = mapped_column("alert_max",nullable=True)
    alarm_min: Mapped[float] = mapped_column("alarm_min",nullable=True)
    alarm_max: Mapped[float] = mapped_column("alarm_max",nullable=True)
    created_at: Mapped[datetime.datetime] = mapped_column("created_at")
    updated_at: Mapped[datetime.datetime] = mapped_column("updated_at")
    is_active: Mapped[bool] = mapped_column("is_active")


class SensorDevice(SQLModel):
    __tablename__ = "sensor_devices"
    __table_args__ = {"schema": "dbo"}

    id: Mapped[int] = mapped_column("id", primary_key=True)
    nid: Mapped[int] = mapped_column("nid")
    label: Mapped[str] = mapped_column("label")
    mine_id: Mapped[int] = mapped_column(ForeignKey("dbo.mines.id"))
    section_id: Mapped[int] = mapped_column(ForeignKey("dbo.sections.id"))
    device_type_id: Mapped[int] = mapped_column(ForeignKey("dbo.device_types.id"))
    ground_status: Mapped[int] = mapped_column("ground_status")
    comm_status: Mapped[int] = mapped_column("comm_status")
    last_heard: Mapped[int] = mapped_column("last_heard")
    battery_status: Mapped[int] = mapped_column("battery_status")
    lh_datetime: Mapped[datetime.datetime] = mapped_column("lh_datetime")
    created_at: Mapped[datetime.datetime] = mapped_column("created_at")
    updated_at: Mapped[datetime.datetime] = mapped_column("updated_at")
    is_active: Mapped[bool] = mapped_column("is_active")


class SensorRawData(SQLModel):
    __tablename__ = "sensor_raw_data"
    __table_args__ = {"schema": "dbo"}

    id: Mapped[int] = mapped_column("id", primary_key=True)
    sensor_device_id: Mapped[int] = mapped_column(ForeignKey("dbo.sensor_devices.id"))
    timestamp: Mapped[int] = mapped_column("timestamp")
    index: Mapped[int] = mapped_column("index")
    module_type_id: Mapped[int] = mapped_column(ForeignKey("dbo.sensor_module_types.id"))
    value: Mapped[float] = mapped_column("value")
    module_state_id: Mapped[int] = mapped_column(ForeignKey("dbo.sensor_module_states.id"))
    module_fault: Mapped[str] = mapped_column("module_fault")
    category_id: Mapped[int] = mapped_column(ForeignKey("dbo.sensor_category.id"))
    ts_day: Mapped[int] = mapped_column("ts_day")
    ts_month: Mapped[int] = mapped_column("ts_month")
    ts_year: Mapped[int] = mapped_column("ts_year")
    ts_week: Mapped[int] = mapped_column("ts_week")
    ts_datetime: Mapped[datetime.datetime] = mapped_column("ts_datetime")
    created_at: Mapped[datetime.datetime] = mapped_column("created_at")
    updated_at: Mapped[datetime.datetime] = mapped_column("updated_at")
    is_active: Mapped[bool] = mapped_column("is_active")


class SensorDataAggrDaily(SQLModel):
    __tablename__ = "sensor_data_aggr_daily"
    __table_args__ = {"schema": "dbo"}

    id: Mapped[int] = mapped_column("id", primary_key=True)
    sensor_device_id: Mapped[int] = mapped_column(ForeignKey("dbo.sensor_devices.id"))
    module_type_id: Mapped[int] = mapped_column(ForeignKey("dbo.sensor_module_types.id"))
    mine_date: Mapped[datetime.date] = mapped_column("mine_date")
    utc_date: Mapped[datetime.date] = mapped_column("utc_date")
    min_value: Mapped[float] = mapped_column("min_value")
    max_value: Mapped[float] = mapped_column("max_value")
    avg_value: Mapped[float] = mapped_column("avg_value")
    category_id: Mapped[int] = mapped_column(ForeignKey("dbo.sensor_category.id"))
    created_at: Mapped[datetime.datetime] = mapped_column("created_at")
    updated_at: Mapped[datetime.datetime] = mapped_column("updated_at")
    is_active: Mapped[bool] = mapped_column("is_active")


class SensorDataAggrHourly(SQLModel):
    __tablename__ = "sensor_data_aggr_hourly"
    __table_args__ = {"schema": "dbo"}

    id: Mapped[int] = mapped_column("id", primary_key=True)
    sensor_device_id: Mapped[int] = mapped_column(ForeignKey("dbo.sensor_devices.id"))
    module_type_id: Mapped[int] = mapped_column(ForeignKey("dbo.sensor_module_types.id"))
    mine_date: Mapped[datetime.date] = mapped_column("mine_date")
    utc_date: Mapped[datetime.date] = mapped_column("utc_date")
    hour: Mapped[int] = mapped_column("hour")
    min_value: Mapped[float] = mapped_column("min_value")
    max_value: Mapped[float] = mapped_column("max_value")
    avg_value: Mapped[float] = mapped_column("avg_value")
    category_id: Mapped[int] = mapped_column(ForeignKey("dbo.sensor_category.id"))
    created_at: Mapped[datetime.datetime] = mapped_column("created_at")
    updated_at: Mapped[datetime.datetime] = mapped_column("updated_at")
    is_active: Mapped[bool] = mapped_column("is_active")