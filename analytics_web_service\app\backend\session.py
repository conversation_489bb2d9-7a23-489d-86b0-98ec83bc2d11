from contextlib import contextmanager
from typing import Iterator

from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker

from backend.config import config

from app_loggers.loggers_config import get_logger, env_settings

logger= get_logger(__name__)


def mask_dsn(dsn: str) -> str:
    """Regular expression to mask password in the DSN"""
    try:
        start = dsn.index("://") + 3
        end = dsn.index("@")
        user_info = dsn[start:end]
        username, password = user_info.split(":", 1)
        masked_user_info = f"{username}:****"
        return dsn[:start] + masked_user_info + dsn[end:]
    except (ValueError, IndexError):
        return None



# create session factory to generate new database sessions
logger.info("=========================================")
logger.info(mask_dsn(config.database.dsn))
logger.info("=========================================")

pool_size = env_settings.SQL_ALC_POOL_SIZE            # Increase this based on your needs
max_overflow = env_settings.SQL_ALC_MAX_OVERFLOW         # Increase overflow to allow more temporary connections
pool_timeout = env_settings.SQL_ALC_POOL_TIMEOUT         # Timeout to wait for a connection before throwing an error
isolation_level = env_settings.SQL_ALC_ISOLATION_LEVEL

try:
    engine_args= {
        "fast_executemany": True,
        **({"pool_size" : pool_size} if pool_size > 0 else {}),
        **({"max_overflow" : max_overflow} if max_overflow > 0 else {}),
        **({"pool_timeout" : pool_timeout} if pool_timeout > 0 else {}),
        **({"isolation_level": isolation_level} if isolation_level != "" else {})
    }
    logger.debug("engine_args : %s",engine_args)
    engine = create_engine(config.database.dsn, **engine_args)

    logger.info(
        f"SQL_ALC_POOL_SIZE: {pool_size}, "
        f"SQL_ALC_MAX_OVERFLOW: {max_overflow}, "
        f"SQL_ALC_POOL_TIMEOUT: {pool_timeout}, "
        f"ISOLATION_LEVEL: {isolation_level}"
    )
    engine.connect()
    logger.info("Database connected successfully!")
except Exception as e:
    logger.error(f"Failed to connect to the database. Details: {e}", exc_info=True)
    raise

SessionFactory = sessionmaker(
    bind=engine,
    autocommit=False,
    autoflush=False,
    expire_on_commit=False,
)

def create_session() -> Iterator[Session]:
    """Create a new database session.

    Yields:
        Database session.
    """

    session = SessionFactory()

    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        error_message = "Error occurred during a database operation."
        logger.error(f"{error_message} Details: {e}", exc_info=True)
        raise
    finally:
        session.close()


@contextmanager
def open_session() -> Iterator[Session]:
    """Create new database session with context manager.

    Yields:
        Database session.
    """

    return create_session()
