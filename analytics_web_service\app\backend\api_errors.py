from datetime import datetime
from dataclasses import dataclass
from enum import Enum
from typing import Any
from fastapi.responses import JSONResponse
import pytz


class APIErrorType(str, Enum):
    """
        Types of errors that can exist within Superset.
    """
    
    # Mine Errors
    MINE_NOT_FOUND = "MINE_NOT_FOUND"
    MINER_NOT_FOUND = "MINER_NOT_FOUND"
    DATABASE_ERROR = "DATABASE_ERROR"
    INVALID_DATE = "INVALID_DATE"
    USER_NOT_FOUND = "USER_NOT_FOUND"
    FUTURE_DATE = "FUTURE_DATE"
    UNAUTHORIZED = "UNAUTHORIZED"
    API_ERROR = "API_ERROR"
 


@dataclass
class APIError():
    """API error that is returned to a client
    """
    status: int
    error_type: APIErrorType
    message: str
    timestamp: datetime = datetime.now()

    def to_dict(self) -> dict[str, Any]:
        """Returns APIError as a dictionary"""
        error = {
            "status": self.status,
            "errorType": self.error_type.value,
            "message": self.message,
            "timestamp": self.timestamp.astimezone(pytz.UTC).isoformat(timespec='milliseconds')
        }
        return error
    
# def generate_error_response(error_type: APIErrorType, message: str, status: int) -> JSONResponse:
#     """Handles API errors and returns JSONResponse"""
#     error_response = APIError(status=status, error_type=error_type, message=message).to_dict()
#     return JSONResponse(status_code=status, content=error_response)

# Usage example
# error_response = generate_error_response(APIErrorType.MINER_NOT_FOUND, "Miner not found")