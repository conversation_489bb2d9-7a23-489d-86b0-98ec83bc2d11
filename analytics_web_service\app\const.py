from enum import Enum
from typing import (
    Final,
    List,
)
from app_loggers.loggers_config import env_settings

# Open API parameters
OPEN_API_TITLE: Final = "IWT Analytics Engine Web Service"
API_KEY = env_settings.API_KEY
AUTH_ENABLE = env_settings.AUTH_ENABLE
OPEN_API_DESCRIPTION: Final = "IWT Analytics Engine Web Service APIs"
API_BASE_URL = "/analytics-service/api/v1"
LOC_TAGS : Final[List[str | Enum] | None] = ["Locations"]
BASE_TAGS: Final[List[str | Enum] | None] = ["Base"]
PROD_TAGS: Final[List[str | Enum] | None] = ["Production"]
SHIFT_TYPE_PRODUCTION: Final = "Production"
SHIFT_TYPE_MAINTENANCE: Final = "Maintenance"
SECONDS_IN_HOUR : Final = 3600
MINUTES_IN_HOUR : Final = 60
SECONDS_IN_MINUTE : Final = 60
ATM_TAGS = ["atmosphere"]
