from fastapi import HTT<PERSON>Ex<PERSON>, status, Security
from fastapi.security import <PERSON><PERSON><PERSON>Head<PERSON>, HTTPBasic
from fastapi.responses import JSONResponse
from backend.api_errors import APIErrorType, APIError
from const import API_KEY, AUTH_ENABLE
from app_loggers.loggers_config import get_logger 

logger= get_logger(__name__)

api_key_header = APIKeyHeader(name="X-API-KEY", auto_error=False)

async def validate_api_key(api_key_header: str = Security(api_key_header)) -> str:
    logger.info(f"AUTH_ENABLE: {AUTH_ENABLE}")
    if AUTH_ENABLE != 1:
        return api_key_header

    if api_key_header != API_KEY:
        api_error = APIError(401, APIErrorType.UNAUTHORIZED, "Invalid API Key")
        response = api_error.to_dict()
        logger.info("Invalid API Key")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=response)

    return api_key_header