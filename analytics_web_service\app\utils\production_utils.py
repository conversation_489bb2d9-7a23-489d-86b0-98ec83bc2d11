from typing import List, Optional, <PERSON><PERSON>
import pytz
from sqlalchemy import (
    TIME,
    Date,
    asc,
    cast,
    desc,
    distinct,
    false,
    null,
    or_,
    select,
    func,
    and_,
    true,
    case,
    literal_column,
)

from datetime import date, datetime, timedelta, timezone, time

from models.location import (
    Mine,
    Miner,
    MinerActivity,
    MinerStatus,
    Watchlist,
    Section,
    Shift,
)

from backend.api_errors import APIErrorType, APIError

from models.production import Cut, DownEvent
from app_loggers.loggers_config import get_logger, env_settings

from services.base import (
    BaseDataManager,
)
from sqlalchemy.orm import aliased
from schemas.location import ShiftSchema
from const import SECONDS_IN_HOUR ,MINUTES_IN_HOUR ,SHIFT_TYPE_PRODUCTION, SECONDS_IN_MINUTE

logger = get_logger(__name__)


def getFeetsMinedAverage(feets_mined, hours):
    return feets_mined / hours


def get_start_date_time(dt):
    # Getting the date part of the datetime object
    date_part = dt.date()

    # Creating a time object representing midnight (start of the day)
    start_of_day = time(0, 0, 0)  # Initializing midnight

    # Combining date_part and start_of_day to form the start datetime of the day
    start_date_time = datetime.combine(date_part, start_of_day)

    # Formatting the datetime to ensure milliseconds are '.000'
    start_date_time_with_ms = start_date_time.replace(microsecond=0)

    return start_date_time_with_ms


def get_end_date_time(dt):
    # Getting the date part of the datetime object
    date_part = dt.date()

    # Creating a time object representing just before midnight (end of the day)
    end_of_day = time(23, 59, 59, 999999)  # Initializing just before midnight

    # Combining date_part and end_of_day to form the end datetime of the day
    end_date_time = datetime.combine(date_part, end_of_day)

    return end_date_time


def time_to_decimal(time_obj: time):
    # Parsing the time string to a datetime object
    # time_obj = datetime.strptime(time, "%I:%M %p")

    # Calculating the decimal time representation
    decimal_time = time_obj.hour + time_obj.minute / 60.0

    return float(decimal_time)


def get_mine_goal_from_portal(
    data_manager: BaseDataManager, mine_id, date, section_id=None
):

    return 1600.50


def get_feet_mined_by_interval(
    data_manager: BaseDataManager,
    mine_id: int,
    start_time: datetime,
    end_time: datetime,
    section_id: int = None,
):

    try:
        stmt = (
            select(Cut.dt_end_time, Cut.mine_id, Cut.total_feet)
            .select_from(Cut)
            .filter(
                Cut.dt_end_time >= start_time,
                Cut.dt_end_time <= end_time,
                Cut.mine_id == mine_id,
            )
        )
        if section_id is not None:
            # print("section_id from get_feet_mined_by_interval", section_id)
            stmt = stmt.where(Cut.section_id == section_id)

        result = data_manager.get_execute_all(stmt)
        total_feet_mined = 0
        if result:
            for row in result:
                # print(row)
                total_feet_mined += row.total_feet
            return total_feet_mined

    except Exception as e:
        logger.error(f"Error in get_feet_mined_by_interval: {e}", exc_info=True)


def get_total_downtime_by_interval(
    data_manager: BaseDataManager, mine_id, start_time, end_time, section_id=None
):

    try:
        stmt = (
            select(
                DownEvent.dt_end_time, DownEvent.mine_id, DownEvent.duration_of_delay
            )
            .select_from(DownEvent)
            .filter(
                DownEvent.dt_end_time >= start_time,
                DownEvent.dt_end_time <= end_time,
                DownEvent.mine_id == mine_id,
            )
        )

        # section_id=None
        if section_id is not None:
            # print("section_id from get_total_downtime_by_interval", section_id)
            stmt = stmt.where(DownEvent.section_id == section_id)

        result = data_manager.get_execute_all(stmt)
        total_seconds = 0
        if result:
            for row in result:
                total_seconds += (
                    row.duration_of_delay.hour * SECONDS_IN_HOUR
                    + row.duration_of_delay.minute * 60
                    + row.duration_of_delay.second
                )

        return total_seconds

    except Exception as e:
        logger.error(f"Error in get_total_downtime_by_interval: {e}", exc_info=True)


def get_last_down_time_by_interval(
    data_manager: BaseDataManager, mine_id, start_time, end_time, section_id=None
):
    try:
        stmt = (
            select(func.max(DownEvent.time_down))
            .select_from(DownEvent)
            .filter(
                DownEvent.dt_end_time >= start_time,
                DownEvent.dt_end_time <= end_time,
                DownEvent.mine_id == mine_id,
            )
            # .order_by(desc(DownEvent.last_down_time))
            .limit(1)
        )

        # section_id=None
        if section_id is not None:
            # print("section_id from get_last_down_time_by_interval", section_id)
            stmt = stmt.where(DownEvent.section_id == section_id)

        last_down_time = data_manager.get_one(stmt)
        if last_down_time:
            return last_down_time

    except Exception as e:
        logger.error(f"Error in get_last_down_time_by_interval: {e}", exc_info=True)
        # raise e


def is_spanning_shift(start_time, end_time):
    # Handling the case where end_time is exactly midnight
    if end_time == time(0, 0, 0):
        return False

    # # Checking if end time is earlier than start time (spanning across midnight)
    if end_time < start_time:
        return True
    else:
        return False


def extract_shift_patterns(shifts_ids):
    """Function to extract the pattern from shift"""
    if not shifts_ids:
        return []

    shifts = [shifts_ids[0]]

    for i in range(1, len(shifts_ids)):
        if shifts_ids[i] != shifts_ids[i - 1]:
            # print(i, i-1,  shifts_ids[i] ,shifts_ids[i - 1])
            shifts.append(shifts_ids[i])
    return shifts


def get_shifts_in_day(data_manager, start_time, current_time, mine_id):
    """Get unique shifts in a day, including those spanning across multiple days."""
    try:
        # print("from get_shifts_in_day", start_time)

        # Query to get distinct shift IDs within the time frame
        cuts_query = (
            select(Cut.shift_id)
            .select_from(Cut)
            .filter(
                Cut.dt_end_time >= start_time,
                Cut.dt_end_time <= current_time,
                Cut.mine_id == mine_id,
            )
            .order_by(Cut.dt_end_time)
        )

        # Execute the query to get the shifts
        shifts_from_cuts = data_manager.get_all(cuts_query)
        unique_shifts_ids = shifts_from_cuts

        # print("unique_shifts_ids", unique_shifts_ids)

        return extract_shift_patterns(unique_shifts_ids)

    except Exception as e:
        logger.error(f"Error in get_shifts_in_day: {e}", exc_info=True)
        return []

def get_shifts_in_particular_day(
    data_manager: BaseDataManager, mine_id: int, start_time, end_time
) -> List[dict]:
    """Retrieve all shifts for a given mine within a specified day."""
    try:

        # print("from get_shifts_in_particular_day", start_time, end_time)
        # Retrieving unique shifts within the specified day
        shifts_list = get_shifts_in_day(
            data_manager, start_time, end_time, mine_id
        )

        # print("shifts_list from cuts", shifts_list)

        if shifts_list:
            if len(shifts_list) != 0:
                # Handling shifts that span across two days
                non_unique_shifts = []

                for shift_id in shifts_list:
                    query = (
                        select(Shift.id, Shift.shift_name, Shift.start_time, Shift.end_time)
                        .where(
                            Shift.mine_id == mine_id,
                            # Shift.is_active == 1,
                            Shift.id == shift_id  # Directly check for the shift_id
                        )
                    )

                    # Executing the query and retrieving all matching shifts
                    shifts = data_manager.get_execute_all(query)

                    # Check if any shifts were returned and append the first one
                    for shift in shifts:
                        non_unique_shifts.append(shift)
                        break  #

                # print("non_unique_shifts",non_unique_shifts)

                shift_list = []
                shift_num = 1
                for shift in non_unique_shifts:
                    # print("shiftobj", shift)
                    shift_dict = {
                        "id": shift_num,
                        "shiftId": shift.id,
                        "shiftName": shift.shift_name,
                        "startTime": shift.start_time.strftime("%H:%M:%S"),
                        "endTime": shift.end_time.strftime("%H:%M:%S"),
                    }

                    # Adding the shift dictionary to the list
                    shift_list.append(shift_dict)
                    shift_num+=1

                # print("shift_list :---",shift_list)

                # # Checking if first shift ends exactly at midnight and adjusting it
                # if len(shift_list) == 1:

                if shift_list is not None and len(shift_list) > 0:
                    if shift_list[0]["endTime"] == "00:00:00":
                        shift_list[0]["endTime"] = "23:59:59"

                    elif is_spanning_shift(shift_list[0]["startTime"], shift_list[0]["endTime"]):
                        # print(
                        #     "is_spanning_shift - single shift \t",
                        #     is_spanning_shift(shift_list[0]["startTime"], shift_list[0]["endTime"]),
                        #     shift_list[0]["startTime"],
                        #     shift_list[0]["endTime"],
                        # )

                        # Adjusting the start time of the first spanning shift to midnight
                        shift_list[0]["startTime"] = "00:00:00"


                # Checking if there are multiple shifts and the last one spans across two days
                if len(shift_list) > 1 :

                    if shift_list[len(shift_list) - 1]["endTime"] == "00:00:00":
                        shift_list[len(shift_list) - 1]["endTime"] = "23:59:59"

                    elif is_spanning_shift(
                        shift_list[len(shift_list) - 1]["startTime"],
                        shift_list[len(shift_list) - 1]["endTime"],
                    ):
                        # print(
                        #     "is_spanning_shift - multiple shifts \t",
                        #     is_spanning_shift(
                        #         shift_list[len(shift_list) - 1]["startTime"],
                        #         shift_list[len(shift_list) - 1]["endTime"],
                        #     ),
                        #     shift_list[len(shift_list) - 1]["startTime"],
                        #     shift_list[len(shift_list) - 1]["endTime"],
                        # )

                        # Adjusting the end time of the last spanning shift to just before midnight
                        shift_list[len(shift_list) - 1]["endTime"] = "23:59:59"
                        # shift_list[len(shift_list) - 1]["endTime"] = "24:00:00"


                # Returning the updated list of shifts
                return shift_list

        # Returning an empty list if no shifts are found
        return []

    except Exception as e:
        logger.error(f"Error in get_shifts_in_particular_day: {e}", exc_info=True)


def extract_shift_hours(shifts):
    # Initializing an empty list to store all the hours
    hours = []

    if shifts :
        # Iterating over each shift
        for shift in shifts:
            start_hour = int(
                shift["startTime"][:2]
            )  # Extracting the starting hour (converting to int)
            end_hour = int(
                shift["endTime"][:2]
            )  # Extracting the ending hour (converting to int)

            # Adding all hours within the shift range to the list
            for hour in range(start_hour, end_hour):
                hours.append(hour)

            # Including the ending hour if it's not midnight (24:00)
            if end_hour <= 24:
                hours.append(end_hour)

        # Sorting and removing duplicates (although there should be none due to range creation)

        hours_set = set(hours)
        # length_of_hours_set = len(hours_set)

        if hours_set and max(hours_set) < 24:
            for hour in range(
                max(hours_set) + 1, 25
            ):  # Filling from the next hour after the max to 24
                hours_set.add(hour)

        final_hours = sorted(hours_set)

        return final_hours
    else :
        return hours


def get_last_saved_or_submitted_ts(data_manager: BaseDataManager, mine_id, section_id : int = None, desired_date: date = None):
    """Function to get the last saved or submitted ts from either cuts or downevents table"""
    try:
        cut_stmt = (
            select(
                func.max(Cut.form_saved_at).label("max_saved_at"),
                func.max(Cut.form_submitted_at).label("max_submitted_at"),
            )
            .select_from(Cut)
            .filter(
                # cast(Cut.dt_end_time, Date) == given_date,
                Cut.mine_id == mine_id
            )
        )

        if section_id is not None:
            cut_stmt = cut_stmt.where(Cut.section_id == section_id)

        if desired_date is not None:
            cut_stmt = cut_stmt.where(Cut.dt_end_time.cast(Date) == desired_date)

        cuts_result = data_manager.get_execute_all(cut_stmt)
        # Processing the result to determine which timestamp to return
        if cuts_result is not None:
            # Extracting the last_saved and last_submitted timestamps from the result
            for row in cuts_result:
                cuts_last_saved = row.max_saved_at
                cuts_last_submitted = row.max_submitted_at

                if cuts_last_saved is not None and cuts_last_submitted is not None:
                    return max(cuts_last_saved, cuts_last_submitted)
                elif cuts_last_saved is not None:
                    return cuts_last_saved

        # print("cuts_result", *cuts_result, sep="\n")
        if cuts_result is None:
            downevent_stmt = (
                select(
                    func.max(DownEvent.form_saved_at).label("max_saved_at"),
                    func.max(DownEvent.form_submitted_at).label("max_submitted_at"),
                )
                .select_from(DownEvent)
                .filter(
                    # cast(DownEvent.dt_end_time, Date) == given_date,
                    DownEvent.mine_id
                    == mine_id,
                )
            )

            if section_id is not None:
                downevent_stmt = downevent_stmt.where(DownEvent.section_id == section_id)

            if desired_date is not None:
                cut_stmt = cut_stmt.where(Cut.dt_end_time.cast(Date) == desired_date)

            downevent_result = data_manager.get_execute_all(downevent_stmt)

            if downevent_result is not None:
                # print("downevent_result", downevent_result)
                # Extracting the last_saved and last_submitted timestamps from the result
                for row in downevent_result:
                    downevent_last_saved = row.max_saved_at
                    downevent_last_submitted = row.max_submitted_at

                    if (
                        downevent_last_saved is not None
                        and downevent_last_submitted is not None
                    ):
                        return max(downevent_last_saved, downevent_last_submitted)
                    elif downevent_last_saved is not None:
                        return downevent_last_saved

        # print("cuts_result", cuts_result)
        return None
    except Exception as e:
        logger.error(f"Error in get_last_saved_or_submitted_ts: {e}", exc_info=True)


def generate_trendline_cuts_data(mined_average, hours, shifts:list):
    """Function to get trendline cuts points list"""
    if not hours or len(hours) < 2:
        # If hours list is empty or has less than two elements, returning []
        return {}

    if mined_average is None:
        return {}

    if shifts:
        last_shift_end_time = datetime.strptime(shifts[-1]['endTime'], '%H:%M:%S').time()
        last_shift_end_decimal = time_to_decimal(last_shift_end_time)

    logger.debug("mined_average : %s  last_shift_end_decimal : %s starthour: %s (last_shift_end_decimal - hours[0]) %s"
                 , mined_average, last_shift_end_decimal, hours[0], (last_shift_end_decimal - hours[0]))

    # Defining the coordinates list containing dictionaries
    coordinates = [
        {"feetMined": 0, "cutEndTime": hours[0]},
        {
            "feetMined": round(mined_average * (last_shift_end_decimal - hours[0]), 2),
            "cutEndTime":round(last_shift_end_decimal, 2),
        },
    ]
    # format(mined_average * (hours[len(hours) - 1] - hours[0]), ".2f"),

    # Constructing the trendlineCutsData dictionary
    trendline_cuts_data = {"coordinates": coordinates}

    return trendline_cuts_data



def generate_trendline_cuts_data_v2(feet_mined_per_hour, shifts, mine_goal):
    """Function to get trendline cuts points list"""

    # Helper to get the last shift end time in decimal hours
    last_shift_start_time = datetime.strptime(shifts[0]['startTime'], '%H:%M:%S').time()
    last_shift_start_decimal = time_to_decimal(last_shift_start_time)
    last_shift_end_time = datetime.strptime(shifts[-1]['endTime'], '%H:%M:%S').time()
    last_shift_end_decimal = time_to_decimal(last_shift_end_time)

    coordinates = []

    # First scenario - When there is no cut data
    if feet_mined_per_hour is None :

        coordinates = [
            {"feetMined": 0, "cutEndTime": last_shift_start_decimal},
            {"feetMined": mine_goal, "cutEndTime": round(last_shift_end_decimal, 2)}
        ]
        return {"coordinates": coordinates}

    # Calculating expected goal achieve time
    try :
        expected_goal_time = mine_goal / feet_mined_per_hour
    except Exception as e:
        logger.debug(f"Exception while calculating feet_mined_per_hour : {e}")
        expected_goal_time = 0

    real_expected_goal_time = expected_goal_time + last_shift_start_decimal
    production_hours = 0

    # Second scenario - When feetmined per hour is higher than expected run rate.
    # The goal will be achieved in less than expected hours.
    if real_expected_goal_time <= last_shift_end_decimal:
        coordinates = [
            {
                "feetMined": 0,
                "cutEndTime": last_shift_start_decimal
            },
            {
                "feetMined": mine_goal, 
                "cutEndTime": round(real_expected_goal_time, 2)
            }
        ]

    # Third scenario - When feetmined per hour is less than the expected run rate.
    # goal will not be achieved by the production shift's end time.
    else:
        production_hours = last_shift_end_decimal - last_shift_start_decimal

        estimated_total_feet_mined = feet_mined_per_hour * production_hours
        coordinates = [
            {
                "feetMined": 0,
                "cutEndTime":last_shift_start_decimal},
            {
                "feetMined": round(estimated_total_feet_mined, 2), 
                "cutEndTime": round(last_shift_end_decimal, 2)
            }
        ]

    logger.debug(
        f"expected_goal_time: {expected_goal_time}\n"
        f"last_shift_end_decimal: {last_shift_end_decimal}\n"
        f"last_shift_start_decimal: {last_shift_start_decimal}\n"
        f"real_expected_goal_time: {real_expected_goal_time}\n"
        f"production_hours: {production_hours}\n"
        f"mine_goal: {mine_goal}\n"
    )
    trendline_cuts_data = {"coordinates": coordinates}

    return trendline_cuts_data


def get_cuts_by_interval(
    data_manager: BaseDataManager,
    mine_id: int,
    start_time: time,
    end_time: time,
    section_id=None,
):
    try:
        query = (
            select(
                Cut.id,
                Cut.section_id,
                Cut.shift_id,
                Cut.cumulative_sum,
                Cut.end_hour,
                Cut.dt_start_time,
                Cut.dt_end_time,
                Cut.total_feet,
                # max_times.c.max_end_time
            )
            .filter(
                Cut.dt_end_time >= start_time,
                Cut.dt_end_time <= end_time,
                Cut.mine_id == mine_id,
            )
            .order_by(Cut.dt_end_time)
        )

        # section_id=None
        if section_id is not None:
            # print("section_id from get_cuts_by_interval", section_id, "\n")
            query = query.where(Cut.section_id == section_id)

        cuts = data_manager.get_execute_all(query)

        # print("cuts from", *cuts, sep="\n")
        return list(cuts)

    except Exception as e:
        logger.error(f"Error in get_cut_ids_by_interval: {e}", exc_info=True)


def get_cuts_by_cut_ids(data_manager: BaseDataManager, cut_ids: set):
    try:
        # print("cut_id from get_cuts_by_cut_ids", cut_ids, list(cut_ids))
        # Creating an alias for the Cut table to perform the join
        max_times = aliased(
            select(func.max(Cut.dt_end_time).label("max_end_time"), Cut.end_hour)
            .group_by(Cut.end_hour)
            .subquery()
        )

        # Joining cuts with max_times subquery to filter based on max dt_end_time for each end_hour
        query = (
            select(
                Cut.id,
                Cut.section_id,
                Cut.shift_id,
                Cut.cumulative_sum,
                Cut.end_hour,
                Cut.dt_start_time,
                Cut.dt_end_time,
                Cut.total_feet,
            )
            .join(
                max_times,
                and_(
                    Cut.end_hour == max_times.c.end_hour,
                    Cut.dt_end_time == max_times.c.max_end_time,
                ),
            )
            .filter(Cut.id.in_(list(cut_ids)))
            .order_by(Cut.dt_end_time)
        )
        joined_cuts = data_manager.get_execute_all(query)

        return list(joined_cuts)

    except Exception as e:
        logger.error(f"Error in get_cuts_by_cut_ids: {e}", exc_info=True)


def calculate_cumulative_sums(end_hour):
    cumulative_sums = {}

    previous_cum_sum = 0  # Initialize previous cumulative sum to 0

    # Iterate through each hour in end_hour dictionary
    for hour, entries in end_hour.items():
        # print("hour", hour, "entries", entries)
        total_feet_sum = 0  # To accumulate total_feet within this hour

        # Getting cumulative sum from previous hour
        cum_sum = previous_cum_sum

        # Calculating total_feet sum and cum_sum for the current hour's entries
        for entry in entries:
            total_feet_sum += entry["total_feet"]
            entry["total_feet_sum"] = total_feet_sum
            entry["cum_sum"] = cum_sum + total_feet_sum

        # Update cumulative sum for the current hour
        cumulative_sums[hour] = cum_sum + total_feet_sum

        # Update previous cumulative sum for the next iteration
        previous_cum_sum = cumulative_sums[hour]

    return end_hour

def get_start_point_of_cuts_graph(shifts):
    """This functions returns a starting point for the cuts graph """
    try:
        if shifts :

            shift_id = shifts[0]["shiftId"]
            start_time =datetime.strptime(shifts[0]["startTime"], '%H:%M:%S').time()
            shift_start_time =time_to_decimal(start_time)

            start_point={
                "shiftId": shift_id,
                "feetMined": 0,
                "cutEndTime": round(shift_start_time, 2)
            }
            return start_point

    except Exception as e:
        logger.error(
            f"Error in get_start_point_of_cuts_graph: {e}",
            exc_info=True,
        )
        return []

def get_coordinates_for_cuts_graph(end_hour_dict, shifts):
    """This functions returns a list of final cuts coordinates"""
    summary_data = []

    # Iterating over end_hour_dict keys in the desired order
    for end_hour in end_hour_dict:
        cuts_in_end_hour = end_hour_dict[end_hour]

        # Grouping cuts by section_id to find latest cuts per section
        latest_cuts_by_section = {}
        for cut in cuts_in_end_hour:

            section_id = cut["section_id"]

            if section_id not in latest_cuts_by_section:
                latest_cuts_by_section[section_id] = cut
            else:
                # Replacing if the current cut has a later end time
                if (
                    cut["dt_end_time"]
                    > latest_cuts_by_section[section_id]["dt_end_time"]
                ):
                    latest_cuts_by_section[section_id] = cut

        latest_cut = max(cuts_in_end_hour, key=lambda x: (x["dt_end_time"], x["cum_sum"]))

        # Calculating feetMined and cutEndTime for this end_hour
        feet_mined = latest_cut["cum_sum"]
        # sum(cut["cum_sum"] for cut in latest_cuts_by_section.values())

        # print("feet_mined :--", latest_cut, latest_cut["cum_sum"], feet_mined)

        cut_end_time = time_to_decimal((latest_cut["dt_end_time"]).time())

        # Preparing the summary data entry for this end_hour
        summary_data.append(
            {
                "shiftId": latest_cut["shift_id"],
                "feetMined": feet_mined,
                "cutEndTime": round(cut_end_time, 2),  # format(cut_end_time, ".2f"),
            }
        )

    starting_point = get_start_point_of_cuts_graph(shifts=shifts)
    if starting_point:
        return [starting_point] + summary_data

def get_cuts_line_data_for_graph(
    data_manager: BaseDataManager,
    mine_id: int,
    start_datetime: datetime,
    current_datetime: datetime,
    shifts: list[dict],
    section_id=None,
    # sections: list[dict],
):
    """This functions returns a list of final cuts coordinates for cuts line"""
    parameter_info = [
        f"data_manager: {data_manager}",
        f"mine_id: {mine_id}",
        f"start_datetime: {start_datetime}",
        f"current_datetime: {current_datetime}",
        # f"sections: {sections}",
    ]

    # print("\nFunction Parameters:")
    # print("\n".join(parameter_info), "\n")

    cuts = get_cuts_by_interval(
        data_manager, mine_id, start_datetime, current_datetime, section_id
    )

    if cuts:
        # retrieving cuts based on the cuts obtained
        # cuts = get_cuts_by_cut_ids(data_manager, cut_ids)
        # print("get_cuts_by_cut_ids response", *cuts, sep="\n")

        # Creating a dictionary to group elements by end_hour
        end_hour_dict = {}

        for cut in cuts:
            end_hour = cut.end_hour
            if end_hour not in end_hour_dict:
                end_hour_dict[end_hour] = []
            end_hour_dict[end_hour].append(
                {
                    "id": cut.id,
                    "section_id": cut.section_id,
                    "shift_id": cut.shift_id,
                    "cumulative_sum": cut.cumulative_sum,
                    "dt_start_time": cut.dt_start_time,
                    "dt_end_time": cut.dt_end_time,
                    "total_feet": cut.total_feet,
                }
            )
        # print("\n end_hour_dict", end_hour_dict, "\n")

        end_hour_dict_with_cum_sum = calculate_cumulative_sums(end_hour_dict)
        # print("\n end_hour_dict_with_cum_sum", end_hour_dict_with_cum_sum, "\n")

        # for k, v in end_hour_dict_with_cum_sum.items():
        #     print(k, v)
        cuts_coordinates = get_coordinates_for_cuts_graph(
            end_hour_dict_with_cum_sum, shifts
        )

        cuts_line_data = {"coordinates": cuts_coordinates}
        return cuts_line_data
    else :
        return {"coordinates": []}


# sectionwise graph related functions


def get_unique_sections_in_day(
    data_manager: BaseDataManager, start_time, current_time, mine_id
):
    try:
        cuts_query = (
            select(distinct(Cut.section_id))
            .select_from(Cut)
            .filter(
                Cut.dt_end_time >= start_time,
                Cut.dt_end_time <= current_time,
                Cut.mine_id == mine_id,
            )
            .order_by(asc(Cut.section_id))
        )

        sections_from_cuts = data_manager.get_all(cuts_query)
        # print("set(sections_from_cuts)", set(sections_from_cuts))
        return set(sections_from_cuts)

    except Exception as e:
        logger.error(f"Error in get_unique_sections_in_day: {e}", exc_info=True)


def get_sections_from_cuts(
    data_manager: BaseDataManager, mine_id: int, start_time, current_time
) -> List[dict]:
    """Retrieve all sections for a given mine within a specified day."""

    # print("times", start_time, current_time)
    try:
        sections_set = get_unique_sections_in_day(
            data_manager, start_time, current_time, mine_id
        )

        # print("sections_set", sections_set)

        if len(sections_set) != 0:
            query = (
                select(Section.id, Section.ext_section_name)
                .where(
                    Section.mine_id == mine_id,
                    Section.is_active == 1,
                    # Section.id.in_([3,4]),  # Filter by section IDs present in sections_set
                    Section.id.in_(
                        list(sections_set)
                    ),  # Filter by shift IDs present in shifts_set
                )
                .order_by(asc(Section.id))
            )
            sections = data_manager.get_execute_all(query)

            sections_list = []
            for section in sections:
                # print("sectionObj", section)
                section_dict = {
                    "sectionId": section.id,
                    "sectionName": section.ext_section_name,
                }

                sections_list.append(section_dict)

            # print("sections_list", sections_list)

            if len(sections_list) > 0:
                return sections_list

        return []

    except Exception as e:
        logger.error(f"Error in get_sections_from_cuts: {e}", exc_info=True)


def get_feet_mined_in_shift_by_section_id(
    data_manager: BaseDataManager, mine_id, section_id, shift: dict, current_date,current_datetime =None
) -> float:

    try:
        keys_of_interest = ("shiftId", "shiftName", "startTime", "endTime")
        shift_id, shift_name, start_time, end_time = (
            shift[key] for key in keys_of_interest
        )
        # print("get_feet_mined_in_shift_by_section_id", shift)
        end_time_obj = datetime.strptime(end_time, '%H:%M:%S').time()
        # print("before",end_time_obj)

        if current_datetime:
            if end_time_obj > current_datetime.time():
                end_time_obj = current_datetime.time()
                # print("after",end_time_obj)

        stmt = (
            select(Cut.dt_end_time, Cut.mine_id, Cut.total_feet)
            .select_from(Cut)
            .where(
                func.cast(Cut.dt_end_time, TIME) >= start_time,
                func.cast(Cut.dt_end_time, TIME) <= end_time_obj ,
                cast(Cut.dt_end_time, Date) == current_date,
                # Cut.shift_id == shift_id,
                Cut.mine_id == mine_id,
                Cut.section_id == section_id,
            )
        )

        result = data_manager.get_execute_all(stmt)
        if result:
            total_feet_mined = 0
            for row in result:
                # print("row", row)
                total_feet_mined += row.total_feet

        return float(total_feet_mined)

    except Exception as e:
        logger.error(
            f"Error in get_feet_mined_in_shift_by_section_id: {e}", exc_info=True
        )

def find_matching_shifts(live_shifts, shifts_list):

    if not shifts_list:
        return live_shifts

    # Creating an empty list to hold the matching records
    matching_shifts = []

    # Looping through each live shift
    for live_shift in live_shifts:
        # Looping through each historical shift
        for shift in shifts_list:
            # Checking if both startTime and endTime match
            if (live_shift['startTime'] == shift['startTime'] and
                live_shift['endTime'] == shift['endTime']):
                # If they match, appending the shift to the matching_shifts list
                matching_shifts.append(shift)

    return matching_shifts

def get_section_coordinates(
    data_manager: BaseDataManager,
    mine_id: int,
    current_datetime: datetime,
    sections: list[dict],
    live_shifts: list[dict],
    shifts_list: list[dict]
):
    if not sections or not live_shifts:
        return []

    section_data = []

    # Creating a set to collect all unique shift names
    # shift_names = list(shift["shiftName"] for shift in live_shifts)
    # print("shift_names",shift_names)

    matching_shifts = find_matching_shifts(live_shifts, shifts_list)

    # print("matching_shifts :-",matching_shifts)
    for section in sections:
        section_id = section["sectionId"]
        section_name = section["sectionName"]

        # Creating section_info dictionary with shift wise data and section data
        section_info = {
            "sectionID": section_id,
            "sectionName": section_name,
            "totalMined": 0,
            "shiftData": []
        }

        # Iterating over matching shifts and creating shiftData skeleton
        for shift in matching_shifts:
            shift_info = {
                "id": shift["id"],  # Assuming 'id' is a key in shift
                "value": 0  # Default value as zero
            }
            section_info["shiftData"].append(shift_info)

        total_shifts = len(matching_shifts)

        # Calculating mining data for each shift
        for i, shift in enumerate(matching_shifts):
            # print("from get_section_coordinates function",i , shift)
            if i == total_shifts - 1:
                current_time_to_pass = current_datetime
            else:
                current_time_to_pass = None

            # Calculating mined feet for the current section in this shift
            mined_feet = get_feet_mined_in_shift_by_section_id(
                data_manager, mine_id, section_id, shift,
                current_datetime.date(), current_time_to_pass
            )

            if mined_feet is not None:
                # Assigning mined_feet value to the corresponding shift in shiftData
                for shift_data in section_info["shiftData"]:
                    if shift_data["id"] == shift["id"]:
                        shift_data["value"] = mined_feet
                        section_info["totalMined"] += mined_feet

        if section_info["shiftData"]:
            # Appending the section data to the result list
            section_data.append(section_info)

    return section_data

def renumber_ids(shifts:list):
    """
    Renumbers the 'id' fields in a list of shift dictionaries sequentially starting from 1.

    Args:
    shifts (list): A list of dictionaries, each containing shift details including 'id'.

    Returns:
    list: The list of dictionaries with 'id' fields renumbered.
    """
    for i, shift in enumerate(shifts):
        shift['id'] = i + 1
    return shifts


def get_empty_schema_prod(
    api_name: str, formatted_current_datetime, last_saved_or_submitted_ts
):
    """Function to get empty schema by passing api_name"""
    if api_name == "live/overview":
        return {
            "lastUpdatedTs": formatted_current_datetime,
            "lastSavedOrSubmittedTs": last_saved_or_submitted_ts,
            "mined": None,
            "minedUnit": None,
            "feetMinedPerHour": None,
            "feetMinedPerHourUnit": None,
            "totalDowntime": None,
            "totalDowntimeUnit": None,
            "lastDowntimeStarted": None,
            "lastDowntimeStartedPostfix": None,
            "shifts": [],
            "cutsGraphData": {},
            "sectionwiseGraphData": {},
            "averageGoal" : None,
            "activityMetrics": {}
            
        }, None

    if api_name == "live/sections":
        return {
            "lastUpdatedTs": formatted_current_datetime,
            "lastSavedOrSubmittedTs": last_saved_or_submitted_ts,
            "shifts": [],
            "sections": [],
            "sectionsData": [],
        }, None


def get_feet_mined_in_day_from_section(
    data_manager: BaseDataManager, mine_id, date_obj, section_id=None
) -> tuple:

    stmt = (
        select(Cut.dt_end_time, Cut.mine_id, Cut.total_feet)
        .select_from(Cut)
        .filter(cast(Cut.dt_end_time, Date) == date_obj, Cut.mine_id == mine_id)
    )

    # section_id=None
    if section_id is not None:
        stmt = stmt.where(Cut.section_id == section_id)

    result = data_manager.get_execute_all(stmt)
    if result:
        total_feet_mined = 0
        for row in result:
            total_feet_mined += row.total_feet

    return date_obj, total_feet_mined


def get_best_day_and_feet_mined(
    data_manager: BaseDataManager,
    mine_id: int,
    current_date: date,
    current_datetime: datetime,
    section_id=None,
) -> Tuple[datetime, int]:
    """
    Function to find the best day and its feet mined within the past year for a specific mine.
    """
    # Calculating the start date based on the lookback days
    lookback_days = env_settings.BEST_DAY_LOOKBACK_DAYS
    start_date = current_date - timedelta(days=lookback_days)
    # print("BEST_DAY_LOOKBACK_DAYS" , lookback_days)

    # Constructing the SQL query to aggregate feet mined by date within the date range
    stmt = (
        select(
            func.cast(Cut.dt_end_time, Date).label("feet_mined_date"),
            func.sum(Cut.total_feet).label("total_feet_mined"),
        )
        .select_from(Cut)
        .filter(
            Cut.mine_id == mine_id,
            func.cast(Cut.dt_end_time, Date) >= start_date,
            Cut.dt_end_time <= current_datetime,
        )
        .group_by(func.cast(Cut.dt_end_time, Date))
        .order_by(func.sum(Cut.total_feet).desc())
        # .limit(1)
    )

    if section_id is not None:
        # print("sections filter from get_best_day_and_feet_mined :- ",section_id)
        stmt = stmt.filter(Cut.section_id == section_id)

    # Executing the query and retrieve the result
    result = data_manager.get_execute_all(stmt)

    best_day = None
    max_feet_mined = 0
    
    # print("bestday_feetmined :- ", *result, sep="\n")
    
    # Iterating over the result (ChunkedIteratorResult) to extract data
    for row in result:
        # print("row", row.feet_mined_date, row.total_feet_mined)
        best_day = row.feet_mined_date
        max_feet_mined = row.total_feet_mined
        break  # Since we are using ORDER BY and LIMIT 1, we only need the first row

    return best_day, max_feet_mined


def get_on_section_by_interval(
    data_manager: BaseDataManager,
    mine_id: int,
    start_time: datetime,
    end_time: datetime,
    section_id: int,
):

    try:
        if section_id is not None:
            stmt = (
                select(Cut.no_of_crew)
                .select_from(Cut)
                .filter(
                    Cut.dt_end_time >= start_time,
                    Cut.dt_end_time <= end_time,
                    Cut.mine_id == mine_id,
                    Cut.section_id == section_id,
                )
            )

            on_section = data_manager.get_one(stmt)
            # print("from get_on_section_by_interval: ", on_section)

            return on_section
        else:
            return None

    except Exception as e:
        logger.error(f"Error in get_on_section_by_interval: {e}", exc_info=True)


def get_no_of_cuts_by_interval(
    data_manager: BaseDataManager,
    mine_id: int,
    start_time: datetime,
    end_time: datetime,
    section_id: int,
):

    try:
        if section_id is not None:
            stmt = (
                select(Cut.total_cuts)
                .select_from(Cut)
                .filter(
                    Cut.dt_end_time >= start_time,
                    Cut.dt_end_time <= end_time,
                    Cut.mine_id == mine_id,
                    Cut.section_id == section_id,
                )
            )
            # section_id=None

            cuts = data_manager.get_one(stmt)
            # print("from get_no_of_cuts_by_interval: ", cuts)

            return cuts
        else:
            return None

    except Exception as e:
        logger.error(f"Error in get_no_of_cuts_by_interval: {e}", exc_info=True)


def hours_between_intervals(from_date, current_datetime):
    """ Function to calculate hours between time intervals"""
    from_date = datetime.combine(from_date, datetime.min.time())
    current_datetime = current_datetime.replace(tzinfo=None)
    time_difference = current_datetime - from_date
    total_hours = time_difference.total_seconds() / 3600
    return total_hours


def add_shift_durations_in_shift(shift_list):
    """Calculate the duration of each shift in hours and add it to the shift dictionary."""
    shift_id = 1
    for shift in shift_list:
        start_time = datetime.strptime(shift.get("startTime"), "%H:%M:%S")
        end_time = datetime.strptime(shift.get("endTime"), "%H:%M:%S")

        # Adjust for shifts that span midnight
        if end_time < start_time:
            end_time += timedelta(days=1)

        duration = end_time - start_time
        shift["shift_duration"] = duration.total_seconds() / 3600
        shift["id"] = shift_id
        shift_id += 1

    return shift_list

def get_raw_shifts(
    data_manager: BaseDataManager,
    mine_id: int,
    shift_type = None
):
    """Retrieving all shifts from shifts table by mine id"""
    try:
        # Query to get all active shifts for the specified mine within the specified day
        query = select(
            Shift.id,
            Shift.shift_name,
            Shift.start_time,
            Shift.end_time,
            Shift.shift_type
        ).where(
            Shift.mine_id == mine_id,
            Shift.is_active == 1,
        )

        if shift_type is not None:
            query = query.filter(Shift.shift_type == shift_type)

        # Executing the query and retrieving the shifts
        shifts = data_manager.get_execute_all(query)

        # Converting the shifts to a list of dictionaries
        shifts_list = [
            {
                "shiftId": shift.id,
                "shiftName": shift.shift_name,
                "startTime": shift.start_time,
                "endTime": shift.end_time,
                "shiftType": shift.shift_type
            }
            for shift in shifts
        ]

        return shifts_list

    except Exception as e:
        logger.error(
            f"Error in get_raw_shifts: {e}",
            exc_info=True,
        )
        return []

def get_shifts_in_day_from_shifts(
    raw_shifts: list[dict], for_graph : bool=None
):
    """Retrieving all shifts for a given mine, adjusting for
    spanning shifts to cover a full 24-hour period."""
    try:

        # Executing the query and retrieving the shifts
        shifts = raw_shifts

        shift_list = []

        for i, shift in enumerate(shifts):
            start_time = shift.get("startTime")
            end_time = shift.get("endTime")

            # Adding original shift
            shift_list.append(
                {
                    "shiftId": shift.get("shiftId"),
                    "shiftName": shift.get("shiftName"),
                    "startTime": start_time.strftime("%H:%M:%S"),
                    "endTime": end_time.strftime("%H:%M:%S"),
                    "shiftType": shift.get("shiftType"),
                }
            )

            # Handling spanning shifts by splitting into two parts if necessary
            if is_spanning_shift(start_time, end_time):
                shift_list.append(
                    {
                        "shiftId": shift.get("shiftId"),
                        "shiftName": shift.get("shiftName"),
                        "startTime": "00:00:00",
                        "endTime": end_time.strftime("%H:%M:%S"),
                        "shiftType": shift.get("shiftType"),
                    }
                )
                shift_list[i]["endTime"] = "00:00:00"

        # Sorting the shifts by start time to handle spanning shifts correctly
        shift_list.sort(
            key=lambda x: datetime.strptime(x["startTime"], "%H:%M:%S").time()
        )

        # logger.debug("shift_list from  get_shifts_in_day_from_shifts :-\n %s ",shift_list)
        final_shifts = add_shift_durations_in_shift(shift_list)

        if for_graph is not None and for_graph is True:
            if final_shifts:
                length_of_shift_list = len(final_shifts) -1
                last_end_time = final_shifts[length_of_shift_list].get("endTime")


                if last_end_time == "00:00:00":
                    final_shifts[length_of_shift_list]["endTime"] = "23:59:59"

                final_shifts = [
                    {k: v for k, v in shift.items() if k != "shift_duration"}
                    for shift in final_shifts
                ]

        return final_shifts

    except Exception as e:
        logger.error(
            f"Error in get_shifts_in_day_from_shifts: {e}",
            exc_info=True,
        )
        return []


def get_total_hours_by_ts(
    data_manager: BaseDataManager,
    mine_id: int,
    last_saved_or_submitted_ts : datetime,

):
    """ Function to get total hours between last_saved_or_submitted_ts 
    and first production shift start_time """
    try:
        # getting all production processed shifts
        raw_shifts = get_raw_shifts(data_manager, mine_id, SHIFT_TYPE_PRODUCTION)

        all_shifts_in_day = get_shifts_in_day_from_shifts(raw_shifts)


        last_ts = last_saved_or_submitted_ts

        if last_ts:
            total_hours = 0
            current_shift_hours = 0
            completed_shifts = 0

            for shift in all_shifts_in_day:
                shift_start_str = shift.get("startTime")
                shift_end_str = shift.get("endTime")

                # Extracting time components
                shift_start_time = datetime.strptime(shift_start_str, "%H:%M:%S").time()
                shift_end_time = datetime.strptime(shift_end_str, "%H:%M:%S").time()

                # Combining with date component from last_ts
                shift_start = datetime.combine(last_ts.date(), shift_start_time)
                shift_end = datetime.combine(last_ts.date(), shift_end_time)


                if shift_end < shift_start:
                    shift_end += timedelta(days=1)

                if last_ts >= shift_end:
                    completed_shifts += 1
                    shift_hours = (shift_end - shift_start).total_seconds() / 3600
                    total_hours += shift_hours
                elif shift_start <= last_ts < shift_end:
                    current_shift_hours = (last_ts - shift_start).total_seconds() / 3600
                    total_hours += current_shift_hours
                    break
                elif last_ts < shift_start:
                    break

            return round(total_hours, 2)


    except Exception as e:
        logger.error(f"Error in get_total_hours_by_ts: {e}", exc_info=True)



def convert_to_utc(dt):
    """
    Converts a datetime object to UTC timezone.
    
    Parameters:
        dt (datetime): Input datetime object.
        
    Returns:
        datetime: Datetime object with timezone replaced by UTC.
    """
    if dt:
        return dt.replace(tzinfo=pytz.utc)


def get_feet_mined_in_day(data_manager: BaseDataManager,mine_id,current_date) -> tuple:
    """_summary_: Accepts single date and return total feet mined in that day

    Args:
        mine_id (_int_): id of the mine
        current_date (_datetime.date_): selected_date

    Returns:
        tuple: selected date and total feet mined in the day
    """
    try:
        stmt = (
                    select(
                        Cut.dt_end_time,
                        Cut.mine_id,
                        Cut.total_feet

                    )
                    .select_from(Cut)
                    .filter(
                        cast(Cut.dt_end_time, Date) == current_date,
                        Cut.mine_id == mine_id,
                        Cut.is_active == 1
                    )
                )

        result = data_manager.get_execute_all(stmt)
        if result:
            total_feet_mined = 0
            for row in result:
                total_feet_mined += row.total_feet

        return current_date, total_feet_mined

    except Exception as e:
        logger.error(f"Error in get_feet_mined_in_day: {e}", exc_info=True)
        return None, APIError(500, APIErrorType.API_ERROR, f"Error {e} in get_feet_mined_in_day")

def get_downtime_in_single_day(data_manager: BaseDataManager,mine_id,current_date) -> int:
    """_summary_ : Accepts single date and return total downtime (in seconds) in that day

    Args:
        mine_id (_int_): id of the mine
        current_date (_datetime.date_): se

    Returns:
        tuple: total seconds in a day
    """
    try:
        stmt = (
                    select(
                        DownEvent.dt_end_time,
                        DownEvent.mine_id,
                        DownEvent.duration_of_delay
                    )
                    .select_from(DownEvent)
                    .filter(
                        cast(DownEvent.dt_end_time, Date) == current_date,
                        DownEvent.mine_id == mine_id,
                        DownEvent.is_active == 1
                    )
                )

        result = data_manager.get_execute_all(stmt)
        total_seconds = 0
        if result:
            for row in result:
                total_seconds += row.duration_of_delay.hour * SECONDS_IN_HOUR + row.duration_of_delay.minute * MINUTES_IN_HOUR + row.duration_of_delay.second

        return total_seconds

    except Exception as e:
        logger.error(f"Error in get_downtime_in_single_day: {e}", exc_info=True)
        return None, APIError(500, APIErrorType.API_ERROR, f"Error {e} in get_downtime_in_single_day")


def hours_since_given_date(from_date):
    """_summary_: current date

    Args:
        from_date (_date_): date

    Returns:
        _type_: returns no of hours in the day
    """
    try:
        current_datetime = datetime.now()
        from_date = datetime.combine(from_date, datetime.min.time())
        time_difference = current_datetime - from_date
        total_hours = time_difference.total_seconds() / SECONDS_IN_HOUR
        return total_hours

    except Exception as e:
        logger.error(f"Error in hours_since_given_date: {e}", exc_info=True)
        return None, APIError(500, APIErrorType.API_ERROR, f"Error {e} in hours_since_given_date") 


def working_hours_in_a_day(shifts):

    working_hours = 0

    for shift in shifts:
        if shift.get("is_active") == 1 :
            common_date = datetime.today().date()
            start_time = shift.get("startTime")
            end_time = shift.get("endTime")
            start_datetime = datetime.combine(common_date, start_time)
            end_datetime = datetime.combine(common_date, end_time)
            if end_datetime <= start_datetime:
                end_datetime += timedelta(days=1)
            duration = end_datetime - start_datetime
            total_minutes = duration.total_seconds()/ MINUTES_IN_HOUR
            hours = total_minutes/ MINUTES_IN_HOUR
            working_hours += hours

    logger.debug("Working hours in a day: %s", working_hours)

    return working_hours


def get_section_graph_points(data_manager: BaseDataManager, mine_id,start_date,end_date) -> list:
    """_summary_: Gives section wise feet mined in given date range (start_date to to_date)

    Args:
        mine_id (_int_): id of the mine
        start_date (_datetime.date_): start date of the date range
        end_date (_type_): end date of the date range

    Returns:
        list: list of objects containing feet mined in each section
    """
    try:
        stmt = (
        select(
            Cut.section_id,
            Section.ext_section_name,
            func.sum(Cut.total_feet).label('total_feet_sum')
        )
        .where(
            and_(
                Cut.mine_id == mine_id,
                cast(Cut.dt_end_time, Date) >= start_date,
                cast(Cut.dt_end_time, Date) <= end_date,
                Cut.section_id == Section.id,
                Cut.is_active == 1
            )
        )
        .group_by(Cut.section_id, Section.ext_section_name)
    )
        result = data_manager.get_execute_all(stmt)
        output_array = []
        for row in result:
            output = {}
            output["section_id"] = row[0]
            output["section_name"] = row[1]
            output["total_feet_mined"] = row[2]
            output_array.append(output)
        return output_array

    except Exception as e:
        logger.error(f"Error in get_section_graph_points: {e}", exc_info=True)
        return None, APIError(500, APIErrorType.API_ERROR, f"Error {e} in get_section_graph_points")


def get_sections(data_manager: BaseDataManager,mine_id) -> tuple:
    """_summary_ : Gives sections and shift details for the mine id

    Args:
        mine_id (_int_): id of the mine

    Returns:
        _tuple_: tuple of section and shift object
    """
    try:
        stmt = select(
                Section.ext_section_name,
                Section.id
                ).where(
            and_(
                Section.mine_id == mine_id,
            )
        ).distinct()

        stmt2 = select(
                Shift.id,
                Shift.shift_name,
                Shift.start_time,
                Shift.shift_type,
                Shift.is_active,
                Shift.end_time).where(
            and_(
            Shift.mine_id == mine_id ,
            Shift.shift_type == SHIFT_TYPE_PRODUCTION
            )
        ).distinct()

        section_result = data_manager.get_execute_all(stmt)
        sections = []
        for section in section_result:
            sections.append({"sectionName":section.ext_section_name, "sectionId":section.id,})

        shift_result = data_manager.get_execute_all(stmt2)
        shifts = []
        for shift in shift_result:
            end_time =shift.end_time
            start_time = shift.start_time
            shift_hours, hour = generate_time_list_and_duration(start_time, end_time)

            shifts.append({"shiftName":shift.shift_name, "shiftId":shift.id, "startTime":shift.start_time, "is_active":shift.is_active,
                        "endTime":shift.end_time, "shiftDuration": hour, "shiftHours": shift_hours,"shift_type" :shift.shift_type })

        return sections, shifts

    except Exception as e:
        logger.error(f"Error in get_sections: {e}", exc_info=True)
        return None, APIError(500, APIErrorType.API_ERROR, f"Error {e} in get_sections")


def get_shifts_include_maintenance(data_manager: BaseDataManager,mine_id) -> tuple:
    """_summary_ : Gives shift details for the mine id

    Args:
        mine_id (_int_): id of the mine

    Returns:
        _tuple_: tuple of section and shift object
    """
    try:

        stmt2 = select(
                Shift.id,
                Shift.shift_name,
                Shift.start_time,
                Shift.end_time,
                Shift.shift_type
                ).where(
                and_(
                Shift.mine_id == mine_id ,
                )
            ).distinct()

        shift_result = data_manager.get_execute_all(stmt2)
        shifts = []
        for shift in shift_result:
            end_time =shift.end_time
            start_time = shift.start_time
            shift_hours, hour = generate_time_list_and_duration(start_time, end_time)

            shifts.append({"shiftName":shift.shift_name, "shiftId":shift.id, "startTime":shift.start_time,
                        "endTime":shift.end_time, "shiftDuration": hour, "shiftHours": shift_hours, "shift_type" :shift.shift_type })

        return shifts

    except Exception as e:
        logger.error(f"Error in get_sections: {e}", exc_info=True)
        return None, APIError(500, APIErrorType.API_ERROR, f"Error {e} in get_sections")








def generate_time_list_and_duration(start_time_obj, end_time_obj):
    try:
        # Validate input types
        if not isinstance(start_time_obj, time) or not isinstance(end_time_obj, time):
            raise ValueError("start_time_obj and end_time_obj must be time objects")

        # Convert the time objects to datetime objects with a common date
        common_date = datetime.today().date()
        start_datetime = datetime.combine(common_date, start_time_obj)
        end_datetime = datetime.combine(common_date, end_time_obj)
        
        # Handle the case where end time is on the next day
        if end_datetime <= start_datetime:
            end_datetime += timedelta(days=1)

        # Initialize the list of times
        time_list = []
        current_time = start_datetime

        # Loop to generate times
        while current_time < end_datetime:
            time_list.append(current_time.hour)
            next_time = current_time + timedelta(hours=1)
            # Avoid overflow by breaking if next_time exceeds datetime max
            if next_time > datetime.max:
                break
            current_time = next_time

        # Add the end time hour to the list if not already added
        if end_datetime.hour not in time_list:
            time_list.append(end_datetime.hour)

        # Calculate the duration
        duration = end_datetime - start_datetime
        total_minutes = duration.total_seconds() // MINUTES_IN_HOUR
        hours = total_minutes // MINUTES_IN_HOUR
        if end_time_obj.minute >= 30:
            time_list.append(end_time_obj.hour + 1)
            hours += 1

        return time_list, hours

    except Exception as e:
        logger.error(f"Error in generate_time_list_and_duration: {e}", exc_info=True)
        return None, APIError(500, APIErrorType.API_ERROR, f"Error {e} in generate_time_list_and_duration")




def get_report_shift_metrics(data_manager: BaseDataManager,mine_id, section_id,shift_id,selected_date) -> tuple:
    """_summary_: calculates the metrics for the shift in the section and graph points

    Args:
        mine_id (_int_): id of the mine
        section_id (_int_): id of the section
        shift_id (_int_): id of the shift
        selected_date (_datetime.date_): _description_

    Returns:
        _tuple_: tuple of shift metrics and graph data 
    """
    try:
        stmt = (
        select(
            Cut.no_of_crew,
            Cut.total_cuts,
            Cut.total_feet,
            Cut.total_down_events,
            Cut.total_feet_mined,
            Cut.total_down_time,
            Cut.cumulative_sum,
            Cut.end_hour,
            Cut.end_time

        )
        .where(
            and_(
                Cut.mine_id == mine_id,
                Cut.form_date == selected_date,
                Cut.section_id == section_id,
                Cut.shift_id == shift_id,
                Cut.is_active == 1
            )
        ).order_by(asc(Cut.dt_end_time))
    )

        result = data_manager.get_execute_all(stmt)
        shift_metrics = {}
        graph_data = []
        for row in result:
            if shift_metrics.get("mined") and shift_metrics.get("mined")< row.total_feet_mined:
                shift_metrics["mined"] = row.total_feet_mined
            elif not shift_metrics.get("mined"):
                shift_metrics["mined"] = row.total_feet_mined
            shift_metrics["total_cuts"] = row.total_cuts
            shift_metrics["total_down_events"] = row.total_down_events
            shift_metrics["on_section"] = row.no_of_crew
            shift_metrics["total_down_time"] = row.total_down_time
            end_time = row.end_time
            seconds = end_time.hour * SECONDS_IN_HOUR + end_time.minute * SECONDS_IN_MINUTE + end_time.second
            decimal_time = seconds / SECONDS_IN_HOUR
            if decimal_time:
                decimal_time = round(decimal_time,2)
            graph_data.append({"hour": decimal_time, "feetMined":row.cumulative_sum})

        return shift_metrics, graph_data

    except Exception as e:
        logger.error(f"Error in get_report_shift_metrics: {e}", exc_info=True)
        return None, APIError(500, APIErrorType.API_ERROR, f"Error {e} in get_report_shift_metrics")


def get_most_downtime(data_manager: BaseDataManager,mine_id, current_date) -> dict:
    """_summary_: Returns dictionary of window and corresponding downtime

    Args:
        mine_id (_int_): id of the mine
        current_date (_datetime.date_): date
    """

    try:
        stmt = (
        select(
            DownEvent.time_down,
            DownEvent.time_repaired
        )
        .where(
            and_(
                DownEvent.mine_id == mine_id,
                cast(DownEvent.dt_end_time, Date) == current_date,
                DownEvent.is_active == 1
            )
        )
    )
        result = data_manager.get_execute_all(stmt)

        window_dict_array = []
        day_downtime_window = {}

        for row in result:
            row_dict = {}
            down_time_start = row.time_down
            down_time_end = row.time_repaired
            window_minutes = calculate_window_minutes (down_time_start, down_time_end)
            for window_index, minutes in enumerate(window_minutes):
                row_dict[f"Window_{window_index + 1}"] = minutes
            window_dict_array.append(row_dict)

        for d in window_dict_array:
            for key, value in d.items():
                day_downtime_window[key] = day_downtime_window.get(key, 0) + value

        return day_downtime_window

    except Exception as e:
        logger.error(f"Error in get_most_downtime: {e}", exc_info=True)
        return None, APIError(500, APIErrorType.API_ERROR, f"Error {e} in get_most_downtime")
        

def calculate_window_minutes(start_time, end_time) -> list:
    """_summary_: Returns minutes in each two hour window

    Args:
        start_time (_datetime.time_): start time
        end_time (_datetime.time_): end time

    Returns:
        list: window minutes
    """
    try:

        # Define the window size in seconds (2 hours)
        window_size_seconds = 2 * SECONDS_IN_HOUR

        # Convert start_time and end_time to total seconds elapsed since midnight
        start_seconds = start_time.hour * SECONDS_IN_HOUR + start_time.minute * MINUTES_IN_HOUR + start_time.second
        end_seconds = end_time.hour * SECONDS_IN_HOUR + end_time.minute * MINUTES_IN_HOUR + end_time.second
        window_minutes = [0] * 12

        # Iterate over each window
        for window_index in range(12):
            window_start_seconds = window_index * window_size_seconds
            window_end_seconds = (window_index + 1) * window_size_seconds
            overlap_start = max(start_seconds, window_start_seconds)
            overlap_end = min(end_seconds, window_end_seconds)
            overlap_duration_minutes = max(0, (overlap_end - overlap_start) // MINUTES_IN_HOUR)
            window_minutes[window_index] = overlap_duration_minutes
        return window_minutes

    except Exception as e:
        logger.error(f"Error in calculate_window_minutes: {e}", exc_info=True)
        return None, APIError(500, APIErrorType.API_ERROR, f"Error {e} in calculate_window_minutes")


def get_section_wise_feetmined(data_manager: BaseDataManager,mine_id, section_id,shift_id,from_date,to_date):
    """_summary_: Feet mined in the shift of the section in given date range

    Args:
        mine_id (_int_): id of the mine
        section_id (_int_): id of the section
        shift_id (_int_): id of the shift
        from_date (_datetime.date_): start date 
        to_date (_datetime.date_): end date

    Returns:
        int: Feet mined in the shift of the section in given date range
    """
    try:
        stmt = (
        select(
            Cut.cumulative_sum,
            Cut.form_date,
            Cut.total_feet
        )
        .where(
            and_(
                Cut.mine_id == mine_id,
                cast(Cut.dt_end_time, Date) >= from_date,
                cast(Cut.dt_end_time, Date) <= to_date,
                Cut.section_id == section_id,
                Cut.shift_id == shift_id,
                Cut.is_active == 1
            )
        ).order_by(desc(Cut.cumulative_sum))
    ).distinct(Cut.form_date)

        result = data_manager.get_execute_all(stmt)
        feet_mined = 0
        for row in result:
            feet_mined +=row.total_feet
        return feet_mined

    except Exception as e:
        logger.error(f"Error in get_section_wise_feetmined: {e}", exc_info=True)
        return None, APIError(500, APIErrorType.API_ERROR, f"Error {e} in get_section_wise_feetmined")

def check_data_present_in_shift(shift_data: dict) -> bool:
    """
    Check if data is present in the shift.
    Args:
        shift_data (dict): A dictionary containing the shift data.
    Returns:
        bool: True if data is present in the shift, False otherwise.
    """
    try :
        if shift_data.get("mined") is None and shift_data.get("totalDowntime") is None:
            return False
        return True

    except Exception as e:
        logger.error(f"Error in check_data_present_in_shift: {e}", exc_info=True)
        return False