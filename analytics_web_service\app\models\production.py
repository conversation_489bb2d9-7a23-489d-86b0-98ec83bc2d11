from sqlalchemy.orm import (
    Mapped,
    mapped_column,
)

from sqlalchemy import Foreign<PERSON>ey
# from models.base import SQLModel
from .base import SQLModel
import datetime


class Cut(SQLModel):
    __tablename__ = "cuts"
    __table_args__ = {"schema": "dbo"}
    
    id: Mapped[int] = mapped_column("id", primary_key=True)
    form_id: Mapped[int] = mapped_column("form_id")
    mine_id: Mapped[int] = mapped_column(ForeignKey("dbo.mines.id"))
    section_id: Mapped[int] = mapped_column(ForeignKey("dbo.sections.id"))
    shift_id: Mapped[int] = mapped_column(ForeignKey("dbo.shifts.id"))
    version_stamp: Mapped[int] = mapped_column("version_stamp")
    form_date: Mapped[datetime.date] = mapped_column("form_date")
    timezone_offset: Mapped[int] = mapped_column("timezone_offset")
    no_of_crew: Mapped[int] = mapped_column("no_of_crew")
    total_feet_mined: Mapped[float] = mapped_column("total_feet_mined")
    total_cuts: Mapped[int] = mapped_column("total_cuts")
    total_down_time: Mapped[datetime.time] = mapped_column("total_down_time")
    last_down_time: Mapped[datetime.datetime] = mapped_column("last_down_time")
    total_down_events: Mapped[int] = mapped_column("total_down_events")
    foreman: Mapped[str] = mapped_column("foreman")
    time_in: Mapped[datetime.time] = mapped_column("time_in")
    arrive_time: Mapped[datetime.time] = mapped_column("arrive_time")
    on_coal_time: Mapped[datetime.time] = mapped_column("on_coal_time")
    quit_time: Mapped[datetime.time] = mapped_column("quit_time")
    incidents: Mapped[str] = mapped_column("incidents")
    no_of_crew_off: Mapped[int] = mapped_column("no_of_crew_off")
    end_of_track_x_cut: Mapped[str] = mapped_column("end_of_track_x_cut")
    total_feet_bolted: Mapped[float] = mapped_column("total_feet_bolted")
    total_no_of_cars: Mapped[int] = mapped_column("total_no_of_cars")
    cm_bits_used: Mapped[int] = mapped_column("cm_bits_used")
    end_hour: Mapped[int] = mapped_column("end_hour")
    cumulative_sum: Mapped[float] = mapped_column("cumulative_sum")
    cut_id: Mapped[int] = mapped_column("cut_id")
    place: Mapped[str] = mapped_column("place")
    height_of_cut: Mapped[float] = mapped_column("height_of_cut")
    start_time: Mapped[datetime.time] = mapped_column("start_time")
    end_time: Mapped[datetime.time] = mapped_column("end_time")
    dt_start_time: Mapped[datetime.datetime] = mapped_column("dt_start_time")
    dt_end_time: Mapped[datetime.datetime] = mapped_column("dt_end_time")
    start_depth: Mapped[float] = mapped_column("start_depth")
    end_depth: Mapped[float] = mapped_column("end_depth")
    total_feet: Mapped[float] = mapped_column("total_feet")
    no_of_cars: Mapped[int] = mapped_column("no_of_cars")
    cut_status: Mapped[str] = mapped_column("cut_status")
    form_created_at: Mapped[datetime.datetime] = mapped_column("form_created_at")
    form_saved_at: Mapped[datetime.datetime] = mapped_column("form_saved_at")
    form_submitted_at: Mapped[datetime.datetime] = mapped_column("form_submitted_at")
    ss_end_hour: Mapped[int] = mapped_column("ss_end_hour")
    created_at: Mapped[datetime.datetime] = mapped_column("created_at")
    updated_at: Mapped[datetime.datetime] = mapped_column("updated_at")
    is_active: Mapped[int] = mapped_column("is_active")

class DownEvent(SQLModel):
    __tablename__ = "down_events"
    __table_args__ = {"schema": "dbo"}
    
    id: Mapped[int] = mapped_column("id", primary_key=True)
    form_id: Mapped[int] = mapped_column("form_id")
    mine_id: Mapped[int] = mapped_column(ForeignKey("dbo.mines.id"))
    section_id: Mapped[int] = mapped_column(ForeignKey("dbo.sections.id"))
    shift_id: Mapped[int] = mapped_column(ForeignKey("dbo.shifts.id"))
    version_stamp: Mapped[int] = mapped_column("version_stamp")
    form_date: Mapped[datetime.date] = mapped_column("form_date")
    timezone_offset: Mapped[int] = mapped_column("timezone_offset")
    no_of_crew: Mapped[int] = mapped_column("no_of_crew")
    total_feet_mined: Mapped[float] = mapped_column("total_feet_mined")
    total_cuts: Mapped[int] = mapped_column("total_cuts")
    total_down_time: Mapped[datetime.time] = mapped_column("total_down_time")
    last_down_time: Mapped[datetime.datetime] = mapped_column("last_down_time")
    total_down_events: Mapped[int] = mapped_column("total_down_events")
    foreman: Mapped[str] = mapped_column("foreman")
    time_in: Mapped[datetime.time] = mapped_column("time_in")
    arrive_time: Mapped[datetime.time] = mapped_column("arrive_time")
    on_coal_time: Mapped[datetime.time] = mapped_column("on_coal_time")
    quit_time: Mapped[datetime.time] = mapped_column("quit_time")
    incidents: Mapped[str] = mapped_column("incidents")
    no_of_crew_off: Mapped[int] = mapped_column("no_of_crew_off")
    end_of_track_x_cut: Mapped[str] = mapped_column("end_of_track_x_cut")
    total_feet_bolted: Mapped[float] = mapped_column("total_feet_bolted")
    total_no_of_cars: Mapped[int] = mapped_column("total_no_of_cars")
    cm_bits_used: Mapped[int] = mapped_column("cm_bits_used")
    down_event_id: Mapped[int] = mapped_column("down_event_id")
    down_event_type: Mapped[str] = mapped_column("down_event_type")
    time_down: Mapped[datetime.time] = mapped_column("time_down")
    time_repaired: Mapped[datetime.time] = mapped_column("time_repaired")
    dt_start_time: Mapped[datetime.datetime] =mapped_column("dt_start_time")
    dt_end_time: Mapped[datetime.datetime] =mapped_column("dt_end_time")
    duration_of_delay: Mapped[datetime.time] = mapped_column("duration_of_delay")
    actual_production_lost_time: Mapped[datetime.time] = mapped_column("actual_production_lost_time")
    equipment_identification: Mapped[str] = mapped_column("equipment_identification")
    reason_for_downtime: Mapped[str] = mapped_column("reason_for_downtime")
    down_event_status: Mapped[str] = mapped_column("down_event_status")
    form_created_at: Mapped[datetime.datetime] = mapped_column("form_created_at")
    form_saved_at: Mapped[datetime.datetime] = mapped_column("form_saved_at")
    form_submitted_at: Mapped[datetime.datetime] = mapped_column("form_submitted_at")
    created_at: Mapped[datetime.datetime] = mapped_column("created_at")
    updated_at: Mapped[datetime.datetime] = mapped_column("updated_at")
    is_active: Mapped[int] = mapped_column("is_active")
    dt_start_time: Mapped[datetime.datetime] = mapped_column("dt_start_time")
    dt_end_time: Mapped[datetime.datetime] = mapped_column("dt_end_time")


class ProductionGoals(SQLModel):
    __tablename__ = "production_goals"
    __table_args__ = {"schema": "dbo"}
    
    id: Mapped[int] = mapped_column("id", primary_key=True)
    section_id: Mapped[int] = mapped_column(ForeignKey("dbo.sections.id"))
    shift_id: Mapped[int] = mapped_column(ForeignKey("dbo.shifts.id"))
    goal: Mapped[int] = mapped_column("goal")
    effective_date: Mapped[datetime.date] = mapped_column("effective_date")
    end_date: Mapped[datetime.date] = mapped_column("end_date")
    created_at: Mapped[datetime.datetime] = mapped_column("created_at")
    created_by: Mapped[int] = mapped_column("created_by")
    updated_at: Mapped[datetime.datetime] = mapped_column("updated_at")
    updated_by: Mapped[int] = mapped_column("updated_by")
