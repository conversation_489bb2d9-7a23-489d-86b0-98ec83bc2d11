from typing import (
    Any,
    List,
    Sequence,
    Type,
)

from sqlalchemy import (
    func,
    select,
)
from sqlalchemy.orm import Session
from sqlalchemy.sql.expression import Executable

from models.base import SQLModel
import os

from app_loggers.loggers_config import get_logger 
logger= get_logger(__name__)

class SessionMixin:
    """Provides instance of database session."""

    def __init__(self, session: Session) -> None:
        self.session = session


class BaseService(SessionMixin):
    """Base class for application services."""


class BaseDataManager(SessionMixin):
    """Base data manager class responsible for operations over database."""

    def add_one(self, model: Any) -> None:
        self.session.add(model)

    def add_all(self, models: Sequence[Any]) -> None:
        self.session.add_all(models)

    def get_one(self, select_stmt: Executable) -> Any:
        return self.session.scalar(select_stmt)

    def get_all(self, select_stmt: Executable) -> List[Any]:
        return list(self.session.scalars(select_stmt).all())
    
    def get_execute_all(self, select_stmt: Executable) -> List[Any]:
        result = self.session.execute(select_stmt)
        return result

    def fetch_all(self, select_stmt):
        result = self.session.execute(select_stmt)
        # Fetching all rows to ensure the connection isn't left busy
        rows = result.fetchall()
        return rows

    def get_execute_one(self, stmt) -> dict:
        """Executes the given SQL statement and returns a single result."""
        try:
            # Executing the statement and fetching one result
            result = self.session.execute(stmt).fetchone()

            if result is None:
                return None

            # Converting result to a dictionary based on its type
            if hasattr(result, '_asdict'):
                result_dict = result._asdict()
            else:
                result_dict = {
                    key: value
                    for key, value in zip(result.keys(), result)
                }
            return result_dict

        except Exception as e:
            logger.error(f"Exception in get_execute_one: {e}", exc_info=True)
            return None

    def get_from_tvf(self, model: Type[SQLModel], *args: Any) -> List[Any]:
        """Query from table valued function.

        This is a wrapper function that can be used to retrieve data from
        table valued functions.

        Examples:
            from app.models.base import SQLModel

            class MyModel(SQLModel):
                __tablename__ = "function"
                __table_args__ = {"schema": "schema"}

                x: Mapped[int] = mapped_column("x", primary_key=True)
                y: Mapped[str] = mapped_column("y")
                z: Mapped[float] = mapped_column("z")

            # equivalent to "SELECT x, y, z FROM schema.function(1, 'AAA')"
            BaseDataManager(session).get_from_tvf(MyModel, 1, "AAA")
        """

        return self.get_all(self.select_from_tvf(model, *args))

    @staticmethod
    def select_from_tvf(model: Type[SQLModel], *args: Any) -> Executable:
        fn = getattr(getattr(func, model.schema()), model.table_name())
        stmt = select(fn(*args).table_valued(*model.fields()))
        return select(model).from_statement(stmt)

class VersionService(BaseService):
    def get_repo_version(self):
        """Get repo."""
        return GetVersion(self.session).get_repo_version()


class GetVersion(BaseDataManager):
    def get_repo_version(self) -> str:
        """Get git repository version.""" 
        # current_directory = os.path.dirname(os.path.realpath(__file__))
        # engine_directory = os.path.dirname(os.path.dirname(os.path.dirname(current_directory)))
        # version_file_path = os.path.join(engine_directory, 'version.txt')

        try:
            #with open(version_file_path, "r") as file:
            with open("../version.txt", "r", encoding='utf-8') as file:
                data = file.read()
                return data.strip("\n"), None
        except FileNotFoundError:
            return "version.txt file not found", None