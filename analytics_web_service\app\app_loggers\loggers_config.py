from pydantic import model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict
import logging

class Settings(BaseSettings):
    LOG_LEVEL : str
    LOG_FORMAT : str
    SQL_SERVER_ADDR : str
    SQL_SERVER_DB_NAME : str
    SQL_SERVER_USER : str
    SQL_SERVER_PWD : str
    SQL_ODBC : str
    SQL_DRIVER : str
    API_KEY : str
    AUTH_ENABLE : int
    BEST_DAY_LOOKBACK_DAYS : int = 365
    SQL_ALC_POOL_SIZE: int = 10
    SQL_ALC_MAX_OVERFLOW: int = 10
    SQL_ALC_POOL_TIMEOUT: int = 30
    SQL_ALC_ISOLATION_LEVEL: str = "READ UNCOMMITTED"
    model_config = SettingsConfigDict(env_file=".env")

    @model_validator(mode='before')
    def check_sqlalchemy_settings(cls, values):
        """
            Validates and updates SQLAlchemy connection settings before the model is initialized.

            This method checks specific SQLAlchemy connection-related 
            fields in the `values` dictionary:
            - `SQL_ALC_POOL_SIZE`
            - `SQL_ALC_MAX_OVERFLOW`
            - `SQL_ALC_POOL_TIMEOUT`

            For each of these fields, it ensures that if the field is 
            present but set to an empty string (`""`), 
            it will be replaced with a default value of `0`.

            Parameters:
                cls: The class to which the validator is applied.
                values (dict): The dictionary containing the values of the fields to be validated.

            Returns:
                dict: The updated `values` dictionary where empty strings for certain fields 
                    are replaced with the default values.
        """

        fields_to_check = {
            'SQL_ALC_POOL_SIZE': 0,
            'SQL_ALC_MAX_OVERFLOW': 0,
            'SQL_ALC_POOL_TIMEOUT': 0
        }

        for field_name, default_value in fields_to_check.items():
            # Checking if the field is present in the values
            if field_name in values:
                value = values.get(field_name, '')
                if isinstance(value, str) and value == "":
                    # Setting to default if empty string
                    values[field_name] = default_value
        return values

env_settings = Settings()

def get_logger(logger_name:str)->logging.Logger:
    logging.basicConfig(level=env_settings.LOG_LEVEL, format=env_settings.LOG_FORMAT)
    return logging.getLogger(logger_name)
