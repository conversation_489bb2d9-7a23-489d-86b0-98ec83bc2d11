from pydantic import BaseModel,validator
from datetime import datetime
from typing import Optional,List,Dict, Union


class Shift(BaseModel):
    shiftId: int
    shiftName: str
    startTime: str
    endTime: str

class OverviewSchema(BaseModel):
    lastUpdatedTs: Optional[datetime]
    lastSavedOrSubmittedTs: Optional[datetime]
    tzCode: Optional[str]
    tzAbbreviation: Optional[str]
    mined: Optional[float]
    minedUnit: Optional[str]
    feetMinedPerHour: Optional[float]
    feetMinedPerHourUnit: Optional[str]
    totalDowntime: Optional[int]
    totalDowntimeUnit: Optional[str]
    lastDowntimeStarted: Optional[str]
    lastDowntimeStartedPostfix: Optional[str]
    shifts: Optional[list]
    cutsGraphData: Optional[dict]
    sectionwiseGraphData: Optional[dict]
    averageGoal : Optional[float]
    activityMetrics : Optional[dict]


class Coordinates(BaseModel):
    feetMined: int
    cutEndTime: float


class SectionDataSchema(BaseModel):
    sectionId: int
    mined: Optional[float]
    minedUnit: Optional[str]
    feetMinedPerHour: Optional[float]
    feetMinedPerHourUnit: Optional[str]
    totalDowntime: Optional[int]
    totalDowntimeUnit: Optional[str]
    lastDowntimeStarted: Optional[str]
    lastDowntimeStartedPostfix: Optional[str]
    cutsGraphData: Optional[dict]
    onSection: Optional[int]
    cuts: Optional[int]

class LiveSectionsSchema(BaseModel):
    lastUpdatedTs: datetime
    lastSavedOrSubmittedTs: Optional[datetime]
    tzCode: Optional[str]
    tzAbbreviation: Optional[str]
    shifts: Optional[list]
    sections: Optional[list]
    sectionsData : Optional[list[SectionDataSchema]]

class sectionwiseGraphDataSchema(BaseModel):
    coordinates: Optional[list]
    shiftNameList : Optional[list]
    xAxisPoints: Optional[list]


class activityMetricsSchema(BaseModel):
    lastReport: Optional[datetime]
    tzCode: Optional[str]
    tzAbbreviation: Optional[str]
    inspectionDowntime: Optional[dict]
    plannedDowntime: Optional[dict]
    unplannedDowntime: Optional[dict]
    onTime:Optional[dict]



class ReportMineSchema(BaseModel):
    totalFeetMined : Optional[float]
    feetMinedGraphData : Optional[dict]
    totalDowntime : Optional[float]
    totalDowntimeUnit : Optional[str]
    feetMinedPerHour : Optional[float]
    feetMinedPerHourUnit : Optional[str]
    totalFeetMinedUnit : Optional[str]
    mostDowntime : Optional[str]
    mostDowntimeMeridiem :Optional[str]
    sectionwiseGraphData : Optional[sectionwiseGraphDataSchema]
    averageGoal: Optional[float]
    activityMetrics : Optional[activityMetricsSchema]


class ReportMineCompareSchema(BaseModel):
    mined : Optional[dict]
    feetMinedPerHour : Optional[dict]
    totalDowntime : Optional[dict]
    mostDowntime : Optional[dict]
    feetMinedGraphData : Optional[dict]
    sectionMinedGraphData : Optional[list]
    averageGoal1 : Optional[float]
    averageGoal2 : Optional[float]
    activityMetrics1 : Optional[activityMetricsSchema]
    activityMetrics2 : Optional[activityMetricsSchema]
    

class ReportShiftSchema(BaseModel):
    sections : Optional[list]
    shifts : Optional[list]
    shiftsMetrics : Optional[list]
 