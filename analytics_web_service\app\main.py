from fastapi import FastAPI

from const import (
    OPEN_API_DESCRIPTION,
    OPEN_API_TITLE,
)
from routers import (
    location, production, base, atmosphere
)
from version import __version__
# import pyodbc

# print("PYODBC",pyodbc.drivers())

app = FastAPI(
    title=OPEN_API_TITLE,
    description=OPEN_API_DESCRIPTION,
    version=__version__,
    swagger_ui_parameters={"defaultModelsExpandDepth": -1},
)

# app.add_middleware(APIErrorHandlerMiddleware)

routers = [base.router, location.router, production.router, atmosphere.router]

for router in routers:
    app.include_router(router)
