from datetime import date
from datetime import date
from typing import Optional
from datetime import date

from fastapi import APIRout<PERSON>, Depends, Path
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from backend.session import create_session
from schemas.location import (
    MinerSchema,
     DashboardSchema,
    ReportCheckinsSchema,
     ReportSectionsResponseSchema, ReportPersonnelSchema,SectionsResponseSchema,
     WatchlistResponseSchema,
     LiveCheckinsSchema,
    # ReportPersonnelSchema
)
from services.location import LocationService
from const import (
    LOC_TAGS,
)
from app_loggers.loggers_config import get_logger

from middleware import validate_api_key
from fastapi import Security
from const import API_BASE_URL, AUTH_ENABLE


logger = get_logger(__name__)

router = APIRouter(prefix=API_BASE_URL, tags=LOC_TAGS, dependencies = [Security(validate_api_key)])



@router.get("/locations/id")
async def get_miner_by_id(
    miner_id: int,
    session: Session = Depends(create_session),
) -> MinerSchema:
    """Get miner by ID."""

    result, api_error = LocationService(session).get_miner_by_id(miner_id)
    if api_error is not None:
        return JSONResponse(status_code=api_error.status, content=api_error.to_dict())
    return result


# @router.get("/locations/status")
# async def get_miner_by_status(
#     status: str,
#     session: Session = Depends(create_session),
# ) -> List[MinerSchema]:
#     """Get miner by ``status``."""
#     result = LocationService(session).get_miners_by_status(status)
#     return result


@router.get("/locations/live/dashboard/{mine_id}")
async def get_dashboard_data(
    mine_id: int = Path(..., title="The ID of the mine"),
    session: Session = Depends(create_session),
) -> DashboardSchema:
    """Get Dashboard Data."""

    result, api_error = LocationService(session).get_dashboard_data(mine_id)
    if api_error is not None:
        return JSONResponse(status_code=api_error.status, content=api_error.to_dict())
    return result


@router.get("/locations/live/sections/{mine_id}/{user_id}")
async def get_sections_data(
    mine_id: int = Path(..., title="The ID of the mine"),
    user_id: int = Path(..., title="The ID of the user"),
    section_id: Optional[int] = None,
    sort_by: str = "first_arrival",
    session: Session = Depends(create_session),
) -> SectionsResponseSchema:
    """Get Section Data."""
    result, api_error = LocationService(session).get_sections_data(
        mine_id, user_id, section_id, sort_by
    )
    if api_error is not None:
        return JSONResponse(status_code=api_error.status, content=api_error.to_dict())
    return result


@router.get("/locations/live/checkins/{mine_id}")
async def get_live_checkins_data(
    session: Session = Depends(create_session),
    mine_id: int = Path(..., title="The ID of the mine"),
) -> LiveCheckinsSchema:
    """Get checkins data."""

    result, api_error = LocationService(session).get_live_checkins_data(mine_id)
    if api_error is not None:
        return JSONResponse(status_code=api_error.status, content=api_error.to_dict())
    return result


@router.get("/locations/live/watchlist/{mine_id}/{user_id}")
async def get_watchlist_data(
    mine_id: int = Path(..., title="The ID of the mine"),
    user_id: int = Path(..., title="The ID of the user"),
    session: Session = Depends(create_session),
) -> WatchlistResponseSchema:
    """Get Watchlist Data."""
    result, api_error = LocationService(session).get_watchlist_data(mine_id, user_id)
    if api_error is not None:
        return JSONResponse(status_code=api_error.status, content=api_error.to_dict())
    return result



@router.get("/locations/reports/sections/{mine_id}/{user_id}/{date}")
async def get_sections_report(
    mine_id: int = Path(..., title="The ID of the mine"),
    user_id: int = Path(..., title="The ID of the user"),
    date: date = Path(..., title="The date in format YYYY-MM-DD"),
    section_id: Optional[int] = None,
    session: Session = Depends(create_session),
) -> ReportSectionsResponseSchema:
    """Get Section Report."""
    result, api_error = LocationService(session).get_sections_report(mine_id, user_id, date, section_id)
    if api_error is not None:
        return JSONResponse(status_code=api_error.status, content=api_error.to_dict())
    return result


@router.get("/locations/reports/personnel/{mine_id}/{miner_id}/{from_date}/{to_date}")
# , response_model=ReportPersonnelSchema)
async def get_personnel_report_data(
    session: Session = Depends(create_session),
    mine_id: int = Path(..., title="Mine ID", description="Unique identifier for the mine"),
    miner_id: int = Path(..., title="Miner ID", description="Unique identifier for the miner"),
    from_date: date = Path(..., title="From Date", description="Start date for the report (YYYY-MM-DD)"),
    to_date: date = Path(..., title="To Date", description="End date for the report (YYYY-MM-DD)"),
) -> ReportPersonnelSchema:
    """Get personnel report data."""
    
    result, api_error = LocationService(session).get_personnel_report_data(mine_id, miner_id, from_date, to_date)
    if api_error is not None:
        return JSONResponse(status_code=api_error.status, content=api_error.to_dict())
    return result

@router.get("/locations/reports/checkins/{mine_id}/{date}")
async def get_report_checkins_data(
    session: Session = Depends(create_session),
    mine_id: int = Path(..., title="The ID of the mine"),
    date: date = Path(..., title="Selected Date"),
) -> ReportCheckinsSchema:
    """Get checkins data."""
    result, api_error = LocationService(session).get_report_checkins_data(mine_id, date)
    if api_error is not None:
        return JSONResponse(status_code=api_error.status, content=api_error.to_dict())
    return result