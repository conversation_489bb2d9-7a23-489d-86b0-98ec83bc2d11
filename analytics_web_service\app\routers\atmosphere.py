from typing import  Dict
from fastapi import APIRouter, Depends, Path, Query, Security
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from datetime import date

from backend.session import create_session
from services.atmosphere import AtmosphereService
from const import (
    ATM_TAGS,
    API_BASE_URL
)
from app_loggers.loggers_config import get_logger
from middleware import validate_api_key

logger = get_logger(__name__)

router = APIRouter(prefix=API_BASE_URL, tags=ATM_TAGS, dependencies=[Security(validate_api_key)])


@router.get("/atmosphere/dashboard/{mine_id}")
async def get_atmosphere_dashboard(
    mine_id: int = Path(..., title="The ID of the mine"),
    session: Session = Depends(create_session),
) -> Dict:
    """
    Get comprehensive dashboard data for atmospheric monitoring.
    
    This endpoint combines overview statistics, active alerts, system stats,
    maintenance stats, and latest sensor readings into a single response.
    """
    service = AtmosphereService(session)
    logger.debug("mine id: %s", mine_id)
    
    # Get system stats
    system_stats, system_error = service.get_system_stats(mine_id)
    if system_error is not None:
        return JSONResponse(status_code=system_error.status, content=system_error.to_dict())
    
    # Get maintenance stats
    maintenance_stats, maintenance_error = service.get_maintenance_stats(mine_id)
    if maintenance_error is not None:
        return JSONResponse(status_code=maintenance_error.status, content=maintenance_error.to_dict())
    
    # Get latest sensor readings
    sensor_data, sensor_error = service.get_sensor_time_series(mine_id)
    if sensor_error is not None:
        return JSONResponse(status_code=sensor_error.status, content=sensor_error.to_dict())
    
    # Combine all data into a single response
    dashboard_data = {       
        "systemStats": system_stats,
        "maintenanceStats": maintenance_stats,
        "sensorData": sensor_data
    }
    
    return dashboard_data

@router.get("/atmosphere/live/notifications/{mine_id}")
async def get_todays_notifications(
    mine_id: int = Path(..., title="The ID of the mine"),
    page_number: int = Query(default=0, title="Page_Number", description="Page Number which will start from 0 andthen next like 1 , 2,3 onwards"),
    page_size: int = Query(default=1000, title="Page_Size", description="Number of records to return per page"),
    session: Session = Depends(create_session),
) -> Dict:
    """
    Get all notifications for today based on mine's timezone.
    
    This endpoint returns a list of all alerts, alarms, and faults that occurred today
    in the mine's local timezone, sorted by timestamp (most recent first).
    """
    service = AtmosphereService(session)
    logger.debug("Getting today's notifications for mine id: %s", mine_id)
    
    notifications_data, notifications_error = service.get_todays_notifications(mine_id,page_number,page_size)
    if notifications_error is not None:
        return JSONResponse(status_code=notifications_error.status, content=notifications_error.to_dict())
    
    return notifications_data

@router.get("/atmosphere/reports/notifications/{mine_id}/{from_date}/{to_date}")
async def get_notifications_by_date_range(
    mine_id: int = Path(..., title="The ID of the mine"),
    from_date: date = Path(..., title="From Date", description="Start date for the report (YYYY-MM-DD)"),
    to_date: date = Path(..., title="To Date", description="End date for the report (YYYY-MM-DD)"),
    page_number: int  = Query(default=0, title="Page_Number", description="Page Number which will start from 0 and then next like 1, 2, 3 onwards"),
    page_size: int = Query(default=1000, title="Page_Size", description="Number of records to return per page"),
    session: Session = Depends(create_session),
) -> Dict:
    """
    Get all notifications for a specific date range based on mine's timezone.
    
    This endpoint returns a list of all alerts, alarms, and faults that occurred within the specified date range
    in the mine's local timezone, sorted by timestamp (most recent first).
    """
    service = AtmosphereService(session)
    logger.debug("Getting notifications for mine id: %s from %s to %s", mine_id, from_date, to_date)
    
    notifications_data, notifications_error = service.get_notifications_by_date_range(mine_id, from_date, to_date,page_number,page_size)
    if notifications_error is not None:
        return JSONResponse(status_code=notifications_error.status, content=notifications_error.to_dict())
    
    return notifications_data

@router.get("/atmosphere/live/gas/{mine_id}")
async def get_live_gas_data(
    mine_id: int = Path(..., title="The ID of the mine"),
    session: Session = Depends(create_session),
) -> Dict:
    """
    Get live gas data for all sections in a mine.
    
    This endpoint returns a list of all sections with their sensors and the latest gas readings,
    including status information and timestamps.
    """
    service = AtmosphereService(session)
    logger.debug("Getting live gas data for mine id: %s", mine_id)
    
    gas_data, gas_error = service.get_live_gas_data(mine_id)
    if gas_error is not None:
        return JSONResponse(status_code=gas_error.status, content=gas_error.to_dict())
    
    return gas_data

@router.get("/atmosphere/live/environmental/{mine_id}")
async def get_live_environmental_data(
    mine_id: int = Path(..., title="The ID of the mine"),
    session: Session = Depends(create_session),
) -> Dict:
    """
    Get live environmental data for all sections in a mine.
    
    This endpoint returns environmental readings (pressure, temperature, humidity)
    for all sensors grouped by section, including timestamps and status information.
    """
    service = AtmosphereService(session)
    logger.debug("Getting live environmental data for mine id: %s", mine_id)
    
    environmental_data, environmental_error = service.get_live_environmental_data(mine_id)
    if environmental_error is not None:
        return JSONResponse(status_code=environmental_error.status, content=environmental_error.to_dict())
    
    return environmental_data

@router.get("/atmosphere/live/ventilation/{mine_id}")
async def get_live_ventilation_data(
    mine_id: int = Path(..., title="The ID of the mine"),
    session: Session = Depends(create_session),
) -> Dict:
    """
    Get live ventilation data for all sections in a mine.

    This endpoint returns ventilation readings from SensorRawData for device type 72 (ventilation sensors)
    with specific module types: Differential Pressure (17, 18), Compensated Pressure (5001),
    Air Velocity (5002), and Air Quantity (5003), grouped by section.
    """
    service = AtmosphereService(session)
    logger.debug("Getting live ventilation data for mine id: %s", mine_id)

    ventilation_data, ventilation_error = service.get_live_ventilation_data(mine_id)
    if ventilation_error is not None:
        return JSONResponse(status_code=ventilation_error.status, content=ventilation_error.to_dict())

    return ventilation_data

@router.get("/atmosphere/reports/gas/{mine_id}")
async def get_gas_report(
    mine_id: int = Path(..., title="The ID of the mine"),
    start_date: date = Query(..., title="Start date for the report (YYYY-MM-DD)"),
    end_date: date = Query(..., title="End date for the report (YYYY-MM-DD)"),
    session: Session = Depends(create_session),
) -> Dict:
    """
    Get gas report data for a specific date range organized by sections.

    This endpoint returns gas data organized by sections, with each section containing devices
    and their gas modules. Gas modules are dynamically named as gas1, gas2, gas3, etc.
    Data includes max and avg values calculated from sensor raw data only (no aggregation tables used).
    Each device also includes an alarm count showing the total number of alarm states recorded
    during the specified date range. Devices without sections are grouped under "No Section".
    """
    service = AtmosphereService(session)
    logger.debug("Getting gas report for mine id: %s from %s to %s", mine_id, start_date, end_date)

    gas_report_data, gas_report_error = service.get_gas_report(mine_id, start_date, end_date)
    if gas_report_error is not None:
        return JSONResponse(status_code=gas_report_error.status, content=gas_report_error.to_dict())

    return gas_report_data

@router.get("/atmosphere/reports/environmental/{mine_id}")
async def get_environmental_report(
    mine_id: int = Path(..., title="The ID of the mine"),
    start_date: date = Query(..., title="Start date for the report (YYYY-MM-DD)"),
    end_date: date = Query(..., title="End date for the report (YYYY-MM-DD)"),
    session: Session = Depends(create_session),
) -> Dict:
    """
    Get environmental report data for a specific date range organized by sections.

    This endpoint returns environmental data organized by sections, with each section containing devices
    and their environmental modules (Pressure, Temperature, Humidity). Data includes average values
    calculated from sensor raw data only (no aggregation tables used). Each device also includes an
    alarm count showing the total number of alarm states recorded during the specified date range.
    Devices without sections are grouped under "No Section".
    """
    service = AtmosphereService(session)
    logger.debug("Getting environmental report for mine id: %s from %s to %s", mine_id, start_date, end_date)

    environmental_report_data, environmental_report_error = service.get_environmental_report(mine_id, start_date, end_date)
    if environmental_report_error is not None:
        return JSONResponse(status_code=environmental_report_error.status, content=environmental_report_error.to_dict())

    return environmental_report_data

@router.get("/atmosphere/reports/ventilation/{mine_id}")
async def get_ventilation_report(
    mine_id: int = Path(..., title="The ID of the mine"),
    start_date: date = Query(..., title="Start date for the report (YYYY-MM-DD)"),
    end_date: date = Query(..., title="End date for the report (YYYY-MM-DD)"),
    session: Session = Depends(create_session),
) -> Dict:
    """
    Get ventilation report data for a specific date range organized by sections.

    This endpoint returns ventilation data organized by sections, with each section containing devices
    and their ventilation modules (Differential Pressure, Compensated Pressure, Air Velocity, Air Quantity).
    Data includes average values calculated from sensor raw data only (no aggregation tables used).
    Each device also includes an alarm count showing the total number of alarm states recorded during
    the specified date range. Devices without sections are grouped under "No Section".

    Note: Module types 17 and 18 are both treated as Differential Pressure and combined if both exist
    for the same device.
    """
    service = AtmosphereService(session)
    logger.debug("Getting ventilation report for mine id: %s from %s to %s", mine_id, start_date, end_date)

    ventilation_report_data, ventilation_report_error = service.get_ventilation_report(mine_id, start_date, end_date)
    if ventilation_report_error is not None:
        return JSONResponse(status_code=ventilation_report_error.status, content=ventilation_report_error.to_dict())

    return ventilation_report_data

@router.get("/atmosphere/reports/gasgraph/{mine_id}")
async def get_gas_graph_data(
    mine_id: int = Path(..., title="The ID of the mine"),
    device_ids: str = Query(..., title="Comma-separated device IDs"),
    start_date: date = Query(..., title="Start date for the data (YYYY-MM-DD)"),
    end_date: date = Query(..., title="End date for the data (YYYY-MM-DD)"),
    session: Session = Depends(create_session),
) -> Dict:
    """
    Get gas graph data for specific devices and date range.

    This endpoint returns gas module data for specified devices within a date range.
    - For multi-day ranges (start_date != end_date): Returns daily aggregated data with max values per day
    - For same-day ranges (start_date == end_date): Returns hourly aggregated data with max values per hour

    The data is sourced from pre-aggregated tables (sensor_data_aggr_daily and sensor_data_aggr_hourly)
    and includes gas module information with sequential naming (gas1, gas2, gas3, etc.) per device.

    The response includes xAxisData and xAxisLabels for easy chart plotting:
    - For hourly data: xAxisData contains hours [0-23], xAxisLabels contains formatted times ["00:00", "01:00", ...]
    - For daily data: xAxisData contains date strings, xAxisLabels contains formatted dates

    Parameters:
    - device_ids: Comma-separated list of device IDs (e.g., "1,2,3")
    - start_date: Start date in YYYY-MM-DD format
    - end_date: End date in YYYY-MM-DD format
    """
    service = AtmosphereService(session)
    logger.debug("Getting gas graph data for mine id: %s, devices: %s from %s to %s", mine_id, device_ids, start_date, end_date)

    gas_graph_data, gas_graph_error = service.get_gas_graph_data(mine_id, device_ids, start_date, end_date)
    if gas_graph_error is not None:
        return JSONResponse(status_code=gas_graph_error.status, content=gas_graph_error.to_dict())

    return gas_graph_data

@router.get("/atmosphere/reports/environmentalgraph/{mine_id}")
async def get_environmental_graph_data(
    mine_id: int = Path(..., title="The ID of the mine"),
    device_ids: str = Query(..., title="Comma-separated device IDs"),
    start_date: date = Query(..., title="Start date for the data (YYYY-MM-DD)"),
    end_date: date = Query(..., title="End date for the data (YYYY-MM-DD)"),
    session: Session = Depends(create_session),
) -> Dict:
    """
    Get environmental graph data for specific devices and date range.

    This endpoint returns environmental module data for specified devices within a date range.
    Environmental modules include: Temperature, Relative Humidity, and Absolute Pressure.

    - For multi-day ranges (start_date != end_date): Returns daily aggregated data with max values per day
    - For same-day ranges (start_date == end_date): Returns hourly aggregated data with max values per hour

    The data is sourced from pre-aggregated tables (sensor_data_aggr_daily and sensor_data_aggr_hourly)
    and filtered by Environment category.

    The response includes xAxisData and xAxisLabels for easy chart plotting:
    - For hourly data: xAxisData contains hours [0-23], xAxisLabels contains formatted times ["00:00", "01:00", ...]
    - For daily data: xAxisData contains date strings, xAxisLabels contains formatted dates

    Parameters:
    - device_ids: Comma-separated list of device IDs (e.g., "1,2,3")
    - start_date: Start date in YYYY-MM-DD format
    - end_date: End date in YYYY-MM-DD format
    """
    service = AtmosphereService(session)
    logger.debug("Getting environmental graph data for mine id: %s, devices: %s from %s to %s", mine_id, device_ids, start_date, end_date)

    environmental_graph_data, environmental_graph_error = service.get_environmental_graph_data(mine_id, device_ids, start_date, end_date)
    if environmental_graph_error is not None:
        return JSONResponse(status_code=environmental_graph_error.status, content=environmental_graph_error.to_dict())

    return environmental_graph_data

@router.get("/atmosphere/reports/ventilationgraph/{mine_id}")
async def get_ventilation_graph_data(
    mine_id: int = Path(..., title="The ID of the mine"),
    device_ids: str = Query(..., title="Comma-separated device IDs"),
    start_date: date = Query(..., title="Start date for the data (YYYY-MM-DD)"),
    end_date: date = Query(..., title="End date for the data (YYYY-MM-DD)"),
    session: Session = Depends(create_session),
) -> Dict:
    """
    Get ventilation graph data for specific devices and date range.

    This endpoint returns ventilation module data for specified devices within a date range.
    Only specific ventilation module types are included: 17, 18, 5001, 5002, 5003.

    Special handling:
    - Module types 17 and 18 are both treated as "Differential Pressure" and consolidated
    - Module type 5001: Compensated Pressure
    - Module type 5002: Air Velocity
    - Module type 5003: Air Quantity

    - For multi-day ranges (start_date != end_date): Returns daily aggregated data with max values per day
    - For same-day ranges (start_date == end_date): Returns hourly aggregated data with max values per hour

    The data is sourced from pre-aggregated tables (sensor_data_aggr_daily and sensor_data_aggr_hourly)
    and filtered by specific module type values.

    The response includes xAxisData and xAxisLabels for easy chart plotting:
    - For hourly data: xAxisData contains hours [0-23], xAxisLabels contains formatted times ["00:00", "01:00", ...]
    - For daily data: xAxisData contains date strings, xAxisLabels contains formatted dates

    Parameters:
    - device_ids: Comma-separated list of device IDs (e.g., "1,2,3")
    - start_date: Start date in YYYY-MM-DD format
    - end_date: End date in YYYY-MM-DD format
    """
    service = AtmosphereService(session)
    logger.debug("Getting ventilation graph data for mine id: %s, devices: %s from %s to %s", mine_id, device_ids, start_date, end_date)

    ventilation_graph_data, ventilation_graph_error = service.get_ventilation_graph_data(mine_id, device_ids, start_date, end_date)
    if ventilation_graph_error is not None:
        return JSONResponse(status_code=ventilation_graph_error.status, content=ventilation_graph_error.to_dict())

    return ventilation_graph_data


@router.get("/atmosphere/reports/gasalarmdays/{mine_id}")
async def get_gas_alarm_days(
    mine_id: int = Path(..., title="The ID of the mine"),    
    start_date: date = Query(..., title="Start date for the data (YYYY-MM-DD)"),
    end_date: date = Query(..., title="End date for the data (YYYY-MM-DD)"),
    session: Session = Depends(create_session),
) -> Dict:
    """
    Get all unique days when gas alarms occurred for a mine within a date range.
    """
    service = AtmosphereService(session)
    logger.debug("Getting gas alarm days data for mine id: %s from %s to %s", mine_id,  start_date, end_date)

    gas_alarm_days_data, gas_alarm_days_error = service.get_gas_alarm_days(mine_id, start_date, end_date)
    if gas_alarm_days_error is not None:
        return JSONResponse(status_code=gas_alarm_days_error.status, content=gas_alarm_days_error.to_dict())

    return gas_alarm_days_data


