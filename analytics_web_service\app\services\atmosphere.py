
from typing import Tuple, Dict
from sqlalchemy import  literal_column, desc, select, func, and_ ,cast, Date
from datetime import date, datetime, timedelta
from utils.common import CelsiusToFahrenheit, int_to_hex
from services.timezone import convert_dt_to_tz, get_day_range_utc_from_mine_tz
from services.mine import get_mine_with_timezone
from backend.api_errors import APIErrorType, APIError
from models.atmosphere import (
    DeviceTypes,
    SensorCategory,
    SensorDevice,
    SensorRawData,
    SensorModuleType,
    SensorModuleState,
    SensorDataAggrDaily,
    SensorDataAggrHourly,
)
from models.location import Section

from schemas.atmosphere import (      
    SensorTimeSeriesResponseSchema
)

from services.base import (
    BaseDataManager,
    BaseService,
)

from app_loggers.loggers_config import get_logger
import pytz
logger = get_logger(__name__)


class AtmosphereService(BaseService):
    """Service class for atmosphere-related operations."""  
    
    def get_system_stats(self, mine_id: int) -> <PERSON><PERSON>[Dict, APIError]:
        """Get system statistics for atmospheric monitoring."""
        return AtmosphereDataManager(self.session).get_system_stats(mine_id)
    
    def get_maintenance_stats(self, mine_id: int) -> Tuple[Dict, APIError]:
        """Get maintenance statistics for atmospheric monitoring."""
        return AtmosphereDataManager(self.session).get_maintenance_stats(mine_id)
    
    def get_sensor_time_series(
        self, 
        mine_id: int,
        limit: int = 1
    ) -> Tuple[SensorTimeSeriesResponseSchema, APIError]:
        """Get latest time series data for all sensors in a mine.
        
        Args:
            mine_id: The mine ID to get data for
            limit: Number of latest data points to retrieve for each sensor/module type combination
        
        Returns:
            Tuple containing the time series response and any error
        """
        return AtmosphereDataManager(self.session).get_sensor_time_series(mine_id, limit)

    def get_todays_notifications(self, mine_id: int, page_number: int,page_size: int) -> Tuple[Dict, APIError]:
        """Get all notifications for today based on mine's timezone.
        
        Args:
            mine_id: The mine ID to get notifications for
        
        Returns:
            Tuple containing the notifications response and any error
        """
        return AtmosphereDataManager(self.session).get_todays_notifications(mine_id,page_number,page_size)

    def get_notifications_by_date_range(self, mine_id: int, from_date: date, to_date: date,page_number: int,page_size: int) -> Tuple[Dict, APIError]:
        """Get notifications for a specific date range based on mine's timezone.
        
        Args:
            mine_id: The mine ID to get notifications for
            from_date: Start date for the report (inclusive)
            to_date: End date for the report (inclusive)
        
        Returns:
            Tuple containing the notifications response and any error
        """
        return AtmosphereDataManager(self.session).get_notifications_by_date_range(mine_id, from_date, to_date,page_number,page_size)

    def get_live_gas_data(self, mine_id: int) -> Tuple[Dict, APIError]:
        """Get live gas data for all sections in a mine.
        
        Args:
            mine_id: The mine ID to get data for
        
        Returns:
            Tuple containing the gas data response and any error
        """
        return AtmosphereDataManager(self.session).get_live_gas_data(mine_id)

    def get_live_environmental_data(self, mine_id: int) -> Tuple[Dict, APIError]:
        """Get live environmental data for all sections in a mine.

        Args:
            mine_id: The mine ID to get data for

        Returns:
            Tuple containing the environmental data response and any error
        """
        return AtmosphereDataManager(self.session).get_live_environmental_data(mine_id)

    def get_live_ventilation_data(self, mine_id: int) -> Tuple[Dict, APIError]:
        """Get live atmospheric data for all sections in a mine from compensated and calculated atmospheric data.

        Args:
            mine_id: The mine ID to get data for

        Returns:
            Tuple containing the atmospheric data response and any error
        """
        return AtmosphereDataManager(self.session).get_live_ventilation_data(mine_id)

    def get_gas_report(self, mine_id: int, start_date: date, end_date: date) -> Tuple[Dict, APIError]:
        """Get gas report data for a specific date range.

        Args:
            mine_id: The mine ID to get data for
            start_date: Start date for the report (inclusive)
            end_date: End date for the report (inclusive)

        Returns:
            Tuple containing the gas report response and any error
        """
        return AtmosphereDataManager(self.session).get_gas_report(mine_id, start_date, end_date)

    def get_environmental_report(self, mine_id: int, start_date: date, end_date: date) -> Tuple[Dict, APIError]:
        """Get environmental report data for a specific date range.

        Args:
            mine_id: The mine ID to get data for
            start_date: Start date for the report (inclusive)
            end_date: End date for the report (inclusive)

        Returns:
            Tuple containing the environmental report response and any error
        """
        return AtmosphereDataManager(self.session).get_environmental_report(mine_id, start_date, end_date)

    def get_ventilation_report(self, mine_id: int, start_date: date, end_date: date) -> Tuple[Dict, APIError]:
        """Get ventilation report data for a specific date range.

        Args:
            mine_id: The mine ID to get data for
            start_date: Start date for the report (inclusive)
            end_date: End date for the report (inclusive)

        Returns:
            Tuple containing the ventilation report response and any error
        """
        return AtmosphereDataManager(self.session).get_ventilation_report(mine_id, start_date, end_date)

    def get_gas_graph_data(self, mine_id: int, device_ids: str, start_date: date, end_date: date) -> Tuple[Dict, APIError]:
        """Get gas graph data for specific devices and date range.

        Args:
            mine_id: The mine ID to get data for
            device_ids: Comma-separated string of device IDs
            start_date: Start date for the data (inclusive)
            end_date: End date for the data (inclusive)

        Returns:
            Tuple containing the gas graph data response and any error
        """
        return AtmosphereDataManager(self.session).get_gas_graph_data(mine_id, device_ids, start_date, end_date)

    def get_environmental_graph_data(self, mine_id: int, device_ids: str, start_date: date, end_date: date) -> Tuple[Dict, APIError]:
        """Get environmental graph data for specific devices and date range.

        Args:
            mine_id: The mine ID to get data for
            device_ids: Comma-separated string of device IDs
            start_date: Start date for the data (inclusive)
            end_date: End date for the data (inclusive)

        Returns:
            Tuple containing the environmental graph data response and any error
        """
        return AtmosphereDataManager(self.session).get_environmental_graph_data(mine_id, device_ids, start_date, end_date)

    def get_ventilation_graph_data(self, mine_id: int, device_ids: str, start_date: date, end_date: date) -> Tuple[Dict, APIError]:
        """Get ventilation graph data for specific devices and date range.

        Args:
            mine_id: The mine ID to get data for
            device_ids: Comma-separated string of device IDs
            start_date: Start date for the data (inclusive)
            end_date: End date for the data (inclusive)

        Returns:
            Tuple containing the ventilation graph data response and any error
        """
        return AtmosphereDataManager(self.session).get_ventilation_graph_data(mine_id, device_ids, start_date, end_date)

    def get_gas_alarm_days(self, mine_id: int, start_date: date, end_date: date) -> Tuple[Dict, APIError]:
        """Get gas alarms day for given mines and date range.

        Args:
            mine_id: The mine ID to get data for            
            start_date: Start date for the data (inclusive)
            end_date: End date for the data (inclusive)

        Returns:
            Tuple containing the ventilation graph data response and any error
        """
        return AtmosphereDataManager(self.session).get_gas_alarm_days(mine_id, start_date, end_date)


class AtmosphereDataManager(BaseDataManager):
    """Data manager class for atmosphere-related database operations."""
    
    def get_system_stats(self, mine_id: int) -> Tuple[Dict, APIError]:
        """Get system statistics for atmospheric monitoring."""
        try:
            # Get mine timezone info
            mine_dict = get_mine_with_timezone(self, mine_id)
            if mine_dict is None:
                logger.error("Error in get_system_stats: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")
            
            # Get total monitors count
            total_monitors_stmt = select(func.count(SensorDevice.id)).where(
                and_(
                SensorDevice.mine_id == mine_id,
                SensorDevice.is_active == 1
                )
            )
            total_monitors = self.get_one(total_monitors_stmt) or 0 
            
            # Get offline sensors count
            offline_sensors_stmt = select(func.count(SensorDevice.id)).where(
                and_(
                    SensorDevice.mine_id == mine_id,
                    SensorDevice.is_active == 1,
                    SensorDevice.comm_status == 0  # Assuming 0 means offline
                )
            )
            offline_sensors = self.get_one(offline_sensors_stmt) or 0
            
            # Get active sensors count (different from online - these are enabled)
            active_sensors_stmt = select(func.count(SensorDevice.id)).where(
                and_(
                    SensorDevice.mine_id == mine_id,
                    SensorDevice.is_active == 1
                )
            )
            active_sensors = self.get_one(active_sensors_stmt) or 0
            
            # Get online sensors count
            online_sensors_stmt = select(func.count(SensorDevice.id)).where(
                and_(
                    SensorDevice.mine_id == mine_id,
                    SensorDevice.is_active == 1,
                    SensorDevice.comm_status == 1  # Assuming 1 means online
                )
            )
            online_sensors = self.get_one(online_sensors_stmt) or 0
            
            # Get ventilation enabled count  
            ventilation_stmt = (
                select(func.count(SensorDevice.id))
                .join(
                    DeviceTypes,
                    SensorDevice.device_type_id == DeviceTypes.id
                )                
                .where(
                    and_(
                        SensorDevice.mine_id == mine_id,
                        SensorDevice.is_active == 1,
                        # SensorDevice.comm_status == 1,
                        DeviceTypes.value == 72,  # Device type value 72  WVM - Wirless Ventilation Monitor
                        
                    )
                )
            )
            ventilation_enabled = self.get_one(ventilation_stmt) or 0

            # Get WGM count  
            WGM_stmt = (
                select(func.count(SensorDevice.id))
                .join(
                    DeviceTypes,
                    SensorDevice.device_type_id == DeviceTypes.id
                )  
                .where(
                    and_(
                        SensorDevice.mine_id == mine_id,
                        SensorDevice.is_active == 1,
                        # SensorDevice.comm_status == 1,
                        DeviceTypes.value == 55,  # Device type value 55  MGM - Wireless Gas Monitor
                        
                    )
                )
            )

            WGM = self.get_one(WGM_stmt) or 0

            # Get WGMPlus count   
            WGMPlus_stmt = (
                select(func.count(SensorDevice.id))
                .join(
                    DeviceTypes,
                    SensorDevice.device_type_id == DeviceTypes.id
                )  
                .where(
                    and_(
                        SensorDevice.mine_id == mine_id,
                        SensorDevice.is_active == 1,
                        # SensorDevice.comm_status == 1,
                        DeviceTypes.value == 69,  # Device type value 69  MGM - Wireless Gas Monitor
                        
                    )
                )
            )

            WGMPlus = self.get_one(WGMPlus_stmt) or 0
            
            # Create response
            system_stats = {
                "totalMonitors": total_monitors,
                "WGM": WGM,
                "WGMPlus": WGMPlus,
                "offline": offline_sensors,
                "active": active_sensors,
                "online": online_sensors,
                "ventilationEnabled": ventilation_enabled
            }
            
            return system_stats, None
            
        except Exception as e:
            logger.error(f"Error in get_system_stats: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, str(e))
    
    def get_maintenance_stats(self, mine_id: int) -> Tuple[Dict, APIError]:
        """Get maintenance statistics for atmospheric monitoring."""
        try:
            # Placeholder implementation - in a real system, you would have specific
            # maintenance-related tables or fields to query
            
            maintenance_stats = {
                "calibrationsDue": 0,
                "calibrationsNext7Days": 0,
                "maintenanceDue": 0
            }
            
            return maintenance_stats, None
            
        except Exception as e:
            logger.error(f"Error in get_maintenance_stats: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, str(e))
    
    def get_sensor_time_series(
        self, 
        mine_id: int,
        limit: int = 1
    ) -> Tuple[SensorTimeSeriesResponseSchema, APIError]:
        """Get latest time series data for all sensors in a mine.
        
        Args:
            mine_id: The mine ID to get data for
            limit: Number of latest data points to retrieve for each sensor/module type combination
        
        Returns:
            Tuple containing the time series response and any error
        """
        try:
            # Get mine timezone info
            mine_dict = get_mine_with_timezone(self, mine_id)
            if mine_dict is None:
                logger.error("Error in get_sensor_time_series: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")
            
            # Get current time in mine's timezone
            mine_tz = mine_dict.get("tz_code") or pytz.utc
            mine_current_datetime = datetime.now(mine_tz)  
            mine_current_date = mine_current_datetime.date()          
            formatted_mine_current_datetime = mine_current_datetime.isoformat(timespec='milliseconds')    

            # Get start and end of today in UTC using common function
            start_of_day_utc, end_of_day_utc = get_day_range_utc_from_mine_tz(mine_current_date, mine_tz)

            if start_of_day_utc is None or end_of_day_utc is None:
                logger.error("Error in get_todays_notifications: Failed to get day range")
                return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, "Failed to get day range")

            all_states_stmt = select(
                    SensorModuleState.id,
                    SensorModuleState.display_name 
                ).where(and_(
                    SensorModuleState.is_active == 1,
                    SensorModuleState.value.in_([ 2, 3,5]) , # Alert, Alarm, Fault
                    )
                ).union(
                    select(
                        literal_column("999").label("id"),
                        literal_column("'Ventilation'").label("display_name")
                    )
                )
            all_states_result = self.get_execute_all(all_states_stmt).fetchall()


            # Get all active sensors with their latest data for each module type
            # This query gets the latest data for each sensor/module type combination
            latest_data_subquery = (
                select(
                    SensorRawData.sensor_device_id,
                    SensorRawData.module_type_id,
                    SensorRawData.module_state_id,
                    func.max(SensorRawData.ts_datetime).label("max_ts")
                )
                .where( 
                    and_(
                        SensorRawData.is_active == 1,
                        SensorRawData.module_state_id.in_([ 2, 3,5]) , # Alert, Alarm, Fault
                        SensorRawData.ts_datetime.between(start_of_day_utc, end_of_day_utc)
                    )
                )
                .group_by(
                    SensorRawData.sensor_device_id,
                    SensorRawData.module_type_id,
                    SensorRawData.module_state_id
                    )
                .subquery()
            )
            
            # Main query to get the sensor data with the latest timestamps
            data_stmt = (
                select(
                    SensorDevice.id.label("sensor_id"),
                    SensorDevice.label.label("sensor_label"),
                    SensorDevice.nid.label("sensor_nid"),
                    SensorDevice.comm_status.label("status"),
                    DeviceTypes.value.label("device_type"),                    
                    SensorModuleType.id.label("module_type_id"),
                    SensorModuleType.display_name.label("module_type_name"),
                    SensorModuleType.unit,
                    SensorModuleType.category_id.label("category_id"),
                    SensorRawData.ts_datetime,
                    SensorRawData.value,
                    Section.ext_section_name.label("section_name"),
                    SensorModuleState.id.label("state_id"),
                    SensorModuleState.display_name.label("state_name"),
                    SensorModuleState.value.label("state_value")    

                )
                .select_from(SensorRawData)
                .join(
                    latest_data_subquery,
                    and_(
                        SensorRawData.sensor_device_id == latest_data_subquery.c.sensor_device_id,
                        SensorRawData.module_type_id == latest_data_subquery.c.module_type_id,
                        SensorRawData.ts_datetime == latest_data_subquery.c.max_ts,
                        SensorRawData.module_state_id == latest_data_subquery.c.module_state_id
                    )
                )
                .join(
                    SensorDevice, 
                    SensorDevice.id == SensorRawData.sensor_device_id
                ).join(
                    DeviceTypes,
                    DeviceTypes.id == SensorDevice.device_type_id
                )
                .join(
                    SensorModuleType, 
                    SensorModuleType.id == SensorRawData.module_type_id
                ).join(
                    SensorModuleState, 
                    SensorModuleState.id == SensorRawData.module_state_id
                )
                .outerjoin(
                   Section, 
                    Section.id == SensorDevice.section_id
                )
                .where(
                    and_(
                        SensorDevice.mine_id == mine_id,
                        SensorDevice.is_active == 1,
                        SensorRawData.is_active == 1
                    )
                ).order_by( desc(SensorDevice.comm_status),desc(SensorRawData.ts_datetime))
            )
                
            
            
            data_result = self.get_execute_all(data_stmt).fetchall()
            
            # Organize data by sensor and module type
            time_series_data = []
            sensor_module_data = {}

            
            # Initialize state counts with all possible states
            state_counts = {}
            for state in all_states_result:
                state_counts[state.id] = {
                    "state_name": state.display_name,
                    "count": 0
                }

            for row in data_result:
                key = (row.sensor_id, row.module_type_id)              
                # Map ventilation state to 999 if device type is 
                state_id = 999 if (row.device_type == 72 and (row.state_value == 2 or row.state_value == 3)) else row.state_id  
                if key not in sensor_module_data:
                    sensor_module_data[key] = {
                        "sensorId": row.sensor_id,
                        "sensorLabel": row.sensor_label,
                        "nid": int_to_hex( row.sensor_nid),
                        "status": row.status,
                        "moduleType": f"{row.module_type_name} {row.state_name}"  if state_id != 999 else "Ventilation",
                        "unit": row.unit if row.unit else ''  ,
                        "sectionName": row.section_name if row.section_name else "No Section",
                        "timestamp": convert_dt_to_tz(row.ts_datetime, mine_tz).strftime("%I:%M%p").lower() if row.ts_datetime else None,
                        "value": row.value,
                        "isCompliance": True if row.state_value == 3  else False,                        
                        "moduleStateId": state_id ,
                        "categoryId": row.category_id
                    }           
                    # Count states                            
                    if state_id in state_counts:                   
                        state_counts[state_id]["count"] += 1
                        
            # Convert dictionary to list
            time_series_data = list(sensor_module_data.values())
            
            # Format state counts for response
            state_data = [
                {
                    "moduleStateId": state_id, 
                    "moduleStateName": state_info["state_name"] if state_id == 999 else state_info["state_name"]+'s', 
                    "count": state_info["count"]
                } 
                for state_id, state_info in state_counts.items()
            ]
                       
            # Create response
            time_series_response = SensorTimeSeriesResponseSchema(
                lastUpdatedTs=formatted_mine_current_datetime,
                timeSeriesData=time_series_data,
                stateData=state_data  # Add state data to the response
            )
            
            return time_series_response, None
            
        except Exception as e:
            logger.error(f"Error in get_sensor_time_series: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, str(e))

    def get_todays_notifications(self, mine_id: int, page_number: int,page_size: int ) -> Tuple[Dict, APIError]:
        """Get all notifications for today based on mine's timezone.
        
        Args:
            mine_id: The mine ID to get notifications for
        
        Returns:
            Tuple containing the notifications response and any error
        """
        try:
            # Get mine timezone info
            mine_dict = get_mine_with_timezone(self, mine_id)
            if mine_dict is None:
                logger.error("Error in get_todays_notifications: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")
            
            # Get current time in mine's timezone
            mine_tz = mine_dict.get("tz_code") or pytz.utc
            mine_current_datetime = datetime.now(mine_tz)
            mine_current_date = mine_current_datetime.date()
            formatted_mine_current_datetime = mine_current_datetime.isoformat(timespec='milliseconds')
            
            # Get start and end of today in UTC using common function
            start_of_day_utc, end_of_day_utc = get_day_range_utc_from_mine_tz(mine_current_date, mine_tz)

            if start_of_day_utc is None or end_of_day_utc is None:
                logger.error("Error in get_notifications_by_date_range: Failed to get day range")
                return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, "Failed to get day range")
            
            # Query for all notifications (alerts, alarms, faults) for today
            notifications_stmt = select(
                SensorRawData.id,
                SensorDevice.id.label("sensor_id"),
                SensorDevice.nid.label("sensor_nid"),
                SensorDevice.label,
                SensorDevice.comm_status.label("status"),
                DeviceTypes.value.label("device_type"),
                SensorModuleType.display_name.label("module_type"),
                SensorModuleState.display_name.label("module_state"),
                SensorModuleState.id.label("state_id"),
                SensorModuleState.value.label("state_value"),
                SensorModuleType.category_id.label("category_id"),
                SensorRawData.ts_datetime.label("timestamp"),
                Section.ext_section_name.label("section_name")                
            ).select_from(
                SensorRawData
            ).join(
                SensorDevice, SensorDevice.id == SensorRawData.sensor_device_id
            ).join(
                   DeviceTypes, DeviceTypes.id == SensorDevice.device_type_id
            ).join(
                SensorModuleType, SensorModuleType.id == SensorRawData.module_type_id
            ).join(
                SensorModuleState, SensorModuleState.id == SensorRawData.module_state_id
            ).outerjoin(
                Section, Section.id == SensorDevice.section_id
            ).where(
                and_(
                    SensorDevice.mine_id == mine_id,
                    SensorDevice.is_active == 1,
                    # SensorModuleState.value.in_([2, 3, 5]),  # Alert, Alarm, Fault states
                    SensorRawData.ts_datetime.between(start_of_day_utc, end_of_day_utc)
                )
            ).order_by(
                desc(SensorDevice.comm_status),
                desc(SensorRawData.ts_datetime)
            ).offset(page_number * page_size).limit(page_size)           
            

            notifications_result = self.session.execute(notifications_stmt).all()
            
            notifications = []
            for row in notifications_result:
                # Convert UTC timestamp to mine's timezone for display
                local_timestamp = convert_dt_to_tz(row.timestamp, mine_tz) if row.timestamp else None
                formatted_time = local_timestamp.strftime("%I:%M:%S %p").lower()             

                notification = {
                    "time": formatted_time,
                    "type": f"{row.module_type} {row.module_state}" if row.device_type != 72 else "Ventilation",
                    "section": row.section_name if row.section_name else "No Section",
                    "label": row.label,
                    "id": row.sensor_id,
                    "status": row.status,
                    "nid": int_to_hex(row.sensor_nid),
                    "compliance": True if row.state_value == 3  else False,
                    "categoryId": row.category_id
                }
                notifications.append(notification)
            
            # Create response
            notifications_response = {
                "lastUpdatedTs": formatted_mine_current_datetime,
                "notifications": notifications,
                "totalCount": len(notifications)
            }
            
            return notifications_response, None
            
        except Exception as e:
            logger.error(f"Error in get_todays_notifications: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, str(e))

    def get_notifications_by_date_range(self, mine_id: int, from_date: date, to_date: date,page_number: int,page_size: int) -> Tuple[Dict, APIError]:
        """Get notifications for a specific date range based on mine's timezone.
        
        Args:
            mine_id: The mine ID to get notifications for
            from_date: Start date for the report (inclusive)
            to_date: End date for the report (inclusive)
        
        Returns:
            Tuple containing the notifications response and any error
        """
        try:
            # Get mine timezone info
            mine_dict = get_mine_with_timezone(self, mine_id)
            if mine_dict is None:
                logger.error("Error in get_notifications_by_date_range: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")
            
            # Get current time in mine's timezone
            mine_tz = mine_dict.get("tz_code") or pytz.utc
            mine_current_datetime = datetime.now(mine_tz)
            formatted_mine_current_datetime = mine_current_datetime.isoformat(timespec='milliseconds')
            
            # Get start and end of the date range in mine's timezone
            start_of_range = datetime.combine(from_date, datetime.min.time())
            start_of_range = mine_tz.localize(start_of_range)
            end_of_range = datetime.combine(to_date, datetime.max.time())
            end_of_range = mine_tz.localize(end_of_range)
            
            # Convert to UTC for database query
            start_of_range_utc = start_of_range.astimezone(pytz.UTC)
            end_of_range_utc = end_of_range.astimezone(pytz.UTC)
            
            # Query for all notifications (alerts, alarms, faults) for the date range
            notifications_stmt = select(
                SensorRawData.id,
                SensorDevice.id.label("sensor_id"),
                SensorDevice.nid.label("sensor_nid"),
                SensorDevice.label,
                SensorModuleType.display_name.label("module_type"),
                SensorModuleState.display_name.label("module_state"),
                SensorModuleState.id.label("state_id"),
                SensorModuleState.value.label("state_value"),
                SensorRawData.ts_datetime.label("timestamp"),
                Section.ext_section_name.label("section_name")
            ).select_from(
                SensorRawData
            ).join(
                SensorDevice, SensorDevice.id == SensorRawData.sensor_device_id
            ).join(
                SensorModuleType, SensorModuleType.id == SensorRawData.module_type_id
            ).join(
                SensorModuleState, SensorModuleState.id == SensorRawData.module_state_id
            ).outerjoin(
                Section, Section.id == SensorDevice.section_id
            ).where(
                and_(
                    SensorDevice.mine_id == mine_id,
                    # SensorModuleState.value.in_([2, 3, 5]),  # Alert, Alarm, Fault states
                    SensorRawData.ts_datetime.between(start_of_range_utc, end_of_range_utc)
                )
            ).order_by(
                desc(SensorRawData.ts_datetime)
            ).offset(page_number * page_size).limit(page_size)
            notifications_result = self.session.execute(notifications_stmt).all()
            
            logger.debug("notifications_result: %s", notifications_result)

            notifications = []
            for row in notifications_result:
                # Convert UTC timestamp to mine's timezone for display
                local_timestamp = convert_dt_to_tz(row.timestamp,mine_tz)
                formatted_time = local_timestamp.strftime("%I:%M:%S %p").lower()
                formatted_date = local_timestamp.strftime("%m/%d/%Y")
                
                notification = {
                    "day": formatted_date,
                    "time": formatted_time,
                    "type": f"{row.module_type} {row.module_state}",
                    "section": row.section_name if row.section_name else "No Section",
                    "label": row.label,
                    "id": row.sensor_id,
                    "nid": int_to_hex(row.sensor_nid),
                    "compliance": True if row.state_value == 3 else False,
                }
                notifications.append(notification)
            
            # Create response
            notifications_response = {
                "lastUpdatedTs": formatted_mine_current_datetime,
                "notifications": notifications,
                "totalCount": len(notifications)
            }
            
            return notifications_response, None
            
        except Exception as e:
            logger.error(f"Error in get_notifications_by_date_range: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, str(e))

    def get_live_gas_data(self, mine_id: int) -> Tuple[Dict, APIError]:
        """Get live gas data for all sections in a mine.
        
        Args:
            mine_id: The mine ID to get data for
        
        Returns:
            Tuple containing the gas data response and any error
        """
        try:
            # Get mine timezone info
            mine_dict = get_mine_with_timezone(self, mine_id)
            if mine_dict is None:
                logger.error("Error in get_live_gas_data: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")
            
             # Get current time in mine's timezone
            mine_tz = mine_dict.get("tz_code") or pytz.utc
            mine_current_datetime = datetime.now(mine_tz)
            mine_current_date = mine_current_datetime.date()
            formatted_mine_current_datetime = mine_current_datetime.isoformat(timespec='milliseconds')

            # Get start and end of today in UTC using common function
            start_of_day_utc, end_of_day_utc = get_day_range_utc_from_mine_tz(mine_current_date, mine_tz)

            if start_of_day_utc is None or end_of_day_utc is None:
                logger.error("Error in get_live_gas_data: Failed to get day range")
                return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, "Failed to get day range")
            
            # Get all sections in the mine
            sections_stmt = select(
                Section.id,
                Section.ext_section_name
            ).where(
                Section.mine_id == mine_id
            ).order_by(
                Section.ext_section_name
            )
            
            sections_result = self.session.execute(sections_stmt).all()
            
            # Get latest data for each sensor
            latest_data_subquery = (
                select(
                    SensorRawData.sensor_device_id,
                    SensorRawData.module_type_id,
                    func.max(SensorRawData.ts_datetime).label("max_ts")
                )
                .where(
                    and_(
                        SensorRawData.is_active == 1,
                        SensorRawData.ts_datetime.between(start_of_day_utc, end_of_day_utc)
                    )
                )
                .group_by(
                    SensorRawData.sensor_device_id,
                    SensorRawData.module_type_id
                )
                .subquery()
            )
            
            # Main query to get the sensor data with the latest timestamps
            sensors_stmt = (
                select(
                    SensorDevice.id.label("sensor_id"),
                    SensorDevice.label.label("sensor_label"),   
                    SensorDevice.nid.label("sensor_nid"),    
                    SensorDevice.comm_status.label("status"),    
                    Section.id.label("section_id"),
                    Section.ext_section_name.label("section_name"),
                    SensorModuleType.id.label("module_type_id"),
                    SensorModuleType.display_name.label("module_type_name"),
                    SensorModuleType.unit,
                    SensorModuleType.alert_min,
                    SensorModuleType.alert_max,
                    SensorModuleType.alarm_min,
                    SensorModuleType.alarm_max,
                    SensorModuleState.display_name.label("state_name"),
                    SensorRawData.value,
                    SensorRawData.ts_datetime,
                    DeviceTypes.value.label("device_type")
                )
                .select_from(SensorRawData)
                .join(
                    latest_data_subquery,
                    and_(
                        SensorRawData.sensor_device_id == latest_data_subquery.c.sensor_device_id,
                        SensorRawData.module_type_id == latest_data_subquery.c.module_type_id,
                        SensorRawData.ts_datetime == latest_data_subquery.c.max_ts
                    )
                )
                .join(
                    SensorDevice, 
                    SensorDevice.id == SensorRawData.sensor_device_id
                ).join(
                    DeviceTypes,
                    DeviceTypes.id == SensorDevice.device_type_id
                )               
                .join(
                    SensorModuleType,
                    SensorModuleType.id == SensorRawData.module_type_id
                )
                 .join(SensorCategory,
                    SensorCategory.id == SensorModuleType.category_id
                )
                .join(
                    SensorModuleState,
                    SensorModuleState.id == SensorRawData.module_state_id
                )
                .outerjoin(
                    Section,
                    Section.id == SensorDevice.section_id
                )
                .where(and_(
                    SensorDevice.mine_id == mine_id,
                    SensorDevice.is_active == 1,
                    DeviceTypes.value != 72,# Exclude ventilation sensors
                    SensorCategory.name == "Gas") # Include only gas category modules data
                ) 
                .order_by(
                   desc(SensorDevice.comm_status),                    
                    Section.ext_section_name,
                    SensorDevice.label,
                    SensorModuleType.display_name                    
                )
            )
            
            sensors_result = self.session.execute(sensors_stmt).all()


            # Count module types per sensor to find the maximum
            sensor_module_counts = {}
            for row in sensors_result:
                sensor_id = row.sensor_id
                module_type = row.module_type_name
                
                if sensor_id not in sensor_module_counts:
                    sensor_module_counts[sensor_id] = set()
                
                sensor_module_counts[sensor_id].add(module_type)


            # Find the sensor with maximum number of gas module types
            sensor_module_counts = {}
            for row in sensors_result:
                sensor_id = row.sensor_id
                if sensor_id not in sensor_module_counts:
                    sensor_module_counts[sensor_id] = set()
                sensor_module_counts[sensor_id].add(row.module_type_name)


            # Get all unique module types for reference
            all_module_types = set()
            for row in sensors_result:
                all_module_types.add(row.module_type_name)
                      
                  
            # Organize data by section
            sections_data = []
            for section in sections_result:
                section_data = {
                    "sectionId": section.id,
                    "sectionName": section.ext_section_name,                   
                    "sensors": []
                }
                sections_data.append(section_data)
            
            # Add "No Section" if there are sensors without a section
            # if no_section_notification_count > 0:
            no_section_data = {
                "sectionId": None,
                "sectionName": "No Section",                   
                "sensors": []
            }
            sections_data.append(no_section_data)
            
            # Group sensors by section
            section_map = {section["sectionId"]: section for section in sections_data}
         
            # Group module types for each sensor
            sensor_data = {}
            for row in sensors_result:
                section_id = row.section_id
                sensor_id = row.sensor_id

                # Find the right section (or "No Section")
                if section_id not in section_map:
                    section_id = None

                    # If "No Section" doesn't exist yet, create it
                    if None not in section_map:
                        no_section_data = {
                            "sectionId": None,
                            "sectionName": "No Section",
                            "notificationCount": 0,
                            "sensors": []
                        }
                        sections_data.append(no_section_data)
                        section_map[None] = no_section_data

                # Create sensor entry if it doesn't exist
                if sensor_id not in sensor_data:
                    sensor_data[sensor_id] = {
                        "sensorId": sensor_id,
                        "label": row.sensor_label,
                        "nid": int_to_hex(row.sensor_nid), # This will change with new column which is not yet introduced
                        "status": row.status,  # Will be updated with the most severe status
                        "stateStatus": "-",
                        "lastUpdate": None,  # Will be updated with the most recent timestamp
                        "moduleData": {},
                        "gas_counter": 1  # Counter for this sensor's gas numbering
                    }

                # Add module data
                module_type = row.module_type_name

                # Generate gas name based on counter (Gas1, Gas2, Gas3, etc.)
                gas_name = f"Gas{sensor_data[sensor_id]['gas_counter']}"
                sensor_data[sensor_id]['gas_counter'] += 1
                
                sensor_data[sensor_id]["moduleData"][gas_name] = {
                    "value": round(row.value,2),
                    "unit": row.unit,
                    "state": row.state_name,
                    "moduleType": module_type,  # Keep the original type for reference
                    "color": "W" if module_type != "CO" and module_type != "CH4" else ( "Y" if row.alert_min <= row.value <= row.alert_max else "R" if row.alarm_min <= row.value  else "W" )                   
                }
                current_state_status = sensor_data[sensor_id]["stateStatus"]           
                
                sensor_data[sensor_id]["stateStatus"] = "Offline" if sensor_data[sensor_id]["status"] == 0 else ( "Alarm" if row.state_name == "Alarm"  else "Alert" if row.state_name == "Alert" and current_state_status not in ['Alarm'] else "Fault" if row.state_name == "Fault" and current_state_status not in ['Alarm', 'Alert']  else current_state_status )

                # Update last update timestamp with the most recent one
                timestamp = row.ts_datetime
                if timestamp:
                    local_timestamp = convert_dt_to_tz(timestamp,mine_tz)
                    formatted_time = local_timestamp.strftime("%I:%M%p").lower()
                    formatted_date = local_timestamp.strftime("%m/%d/%y")
                    
                    if sensor_data[sensor_id]["lastUpdate"] is None:
                        sensor_data[sensor_id]["lastUpdate"] = {
                            "time": formatted_time,
                            "date": formatted_date
                        }
                    elif timestamp > datetime.fromisoformat(sensor_data[sensor_id]["lastUpdate"].get("timestamp", "2000-01-01")):
                        sensor_data[sensor_id]["lastUpdate"] = {
                            "time": formatted_time,
                            "date": formatted_date,
                            "timestamp_utc": timestamp.isoformat()
                        }
            
            # Add sensors to their respective sections
            for sensor_id, sensor in sensor_data.items():
                # Remove the gas_counter before adding to section
                sensor_copy = sensor.copy()
                sensor_copy.pop('gas_counter', None)

                section_id = next((row.section_id for row in sensors_result if row.sensor_id == sensor_id), None)
                if section_id in section_map:
                    section_map[section_id]["sensors"].append(sensor_copy)
                else:
                    # Add to "No Section" if section not found
                    if None in section_map:
                        section_map[None]["sensors"].append(sensor_copy)
            
            # Create response
            response = {
                "lastUpdatedTs": formatted_mine_current_datetime,
                "sections": sections_data
            }
            
            return response, None
            
        except Exception as e:
            logger.error(f"Error in get_live_gas_data: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, str(e))

    def get_live_environmental_data(self, mine_id: int) -> Tuple[Dict, APIError]:
        """Get live environmental data for all sections in a mine.
        
        Args:
            mine_id: The mine ID to get data for
        
        Returns:
            Tuple containing the environmental data response and any error
        """
        try:
            # Get mine timezone info
            mine_dict = get_mine_with_timezone(self, mine_id)
            if mine_dict is None:
                logger.error("Error in get_live_environmental_data: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")
            
            # Get current time in mine's timezone
            mine_tz = mine_dict.get("tz_code") or pytz.utc
            mine_current_datetime = datetime.now(mine_tz)
            mine_current_date = mine_current_datetime.date()
            formatted_mine_current_datetime = mine_current_datetime.isoformat(timespec='milliseconds')

            # Get start and end of today in UTC using common function
            start_of_day_utc, end_of_day_utc = get_day_range_utc_from_mine_tz(mine_current_date, mine_tz)

            if start_of_day_utc is None or end_of_day_utc is None:
                logger.error("Error in get_live_environmental_data: Failed to get day range")
                return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, "Failed to get day range")
            
            # Get all sections in the mine
            sections_stmt = select(
                Section.id,
                Section.ext_section_name
            ).where(
                Section.mine_id == mine_id
            ).order_by(
                Section.ext_section_name
            )
            
            sections_result = self.session.execute(sections_stmt).all()
            
            # Get latest data for each sensor
            latest_data_subquery = (
                select(
                    SensorRawData.sensor_device_id,
                    SensorRawData.module_type_id,
                    func.max(SensorRawData.ts_datetime).label("max_ts")
                )
                .where(
                    and_(
                    SensorRawData.is_active == 1,
                    SensorRawData.ts_datetime.between(start_of_day_utc, end_of_day_utc)
                    )
                )
                .group_by(
                    SensorRawData.sensor_device_id,
                    SensorRawData.module_type_id
                )
                .subquery()
            )
            
            # Main query to get the environmental sensor data with the latest timestamps
            # Focusing on module types 16 (Pressure), 6 (Temperature), and 4 (Humidity)
            sensors_stmt = (
                select(
                    SensorDevice.id.label("sensor_id"),
                    SensorDevice.label.label("sensor_label"),
                    SensorDevice.nid.label("sensor_nid"),
                    SensorDevice.comm_status.label("status"),
                    Section.id.label("section_id"),
                    Section.ext_section_name.label("section_name"),
                    SensorModuleType.id.label("module_type_id"),
                    SensorModuleType.value.label("module_type_value"),
                    SensorModuleType.display_name.label("module_type_name"),
                    SensorModuleType.unit,
                    SensorRawData.value,
                    SensorRawData.ts_datetime,
                    SensorModuleState.display_name.label("state_name")
                )
                .select_from(SensorRawData)
                .join(
                    latest_data_subquery,
                    and_(
                        SensorRawData.sensor_device_id == latest_data_subquery.c.sensor_device_id,
                        SensorRawData.module_type_id == latest_data_subquery.c.module_type_id,
                        SensorRawData.ts_datetime == latest_data_subquery.c.max_ts
                    )
                )
                .join(
                    SensorDevice, 
                    SensorDevice.id == SensorRawData.sensor_device_id
                )
                .join(
                    SensorModuleType,
                    SensorModuleType.id == SensorRawData.module_type_id
                )
                .join(
                    SensorModuleState,
                    SensorModuleState.id == SensorRawData.module_state_id
                )
                .outerjoin(
                    Section,
                    Section.id == SensorDevice.section_id
                )
                .where(and_(
                    SensorDevice.mine_id == mine_id,
                    SensorDevice.is_active == 1,
                    SensorModuleType.value.in_([16, 6, 4])  # Pressure, Temperature, Humidity
                ))
                .order_by(
                    desc(SensorDevice.comm_status),
                    Section.ext_section_name,
                    SensorDevice.label                    
                )
            )
            
            sensors_result = self.session.execute(sensors_stmt).all()
            
            # Organize data by section
            sections_data = []
            for section in sections_result:
                section_data = {
                    "sectionId": section.id,
                    "sectionName": section.ext_section_name,
                    "sensorCount": 0,
                    "sensors": []
                }
                sections_data.append(section_data)
            
            # Add "No Section" for sensors without a section
            no_section_data = {
                "sectionId": None,
                "sectionName": "No Section",
                "sensorCount": 0,
                "sensors": []
            }
            sections_data.append(no_section_data)
            
            # Create a mapping for easy access
            section_map = {section["sectionId"]: section for section in sections_data}
            
            # Group data by sensor
            sensor_data = {}
            for row in sensors_result:
                section_id = row.section_id
                sensor_id = row.sensor_id
                
                # Create sensor entry if it doesn't exist
                if sensor_id not in sensor_data:
                    sensor_data[sensor_id] = {
                        "sensorId": sensor_id,
                        "label": row.sensor_label,
                        "nid": int_to_hex(row.sensor_nid),
                        "status": row.status,
                        "lastUpdate": None,
                        "moduleData": {}
                    }
                
                # Add module data
                module_type = 'Humidity' if row.module_type_name == 'Relative Humidity' else 'AbsPressure' if row.module_type_name == 'Absolute Pressure' else row.module_type_name 
                
                sensor_data[sensor_id]["moduleData"][module_type] = {                   
                    "value": round(row.value,2) if module_type != 'Temperature' else CelsiusToFahrenheit(row.value),
                    "unit": row.unit  if module_type != 'Temperature' else '°F',
                    "state": row.state_name
                }
                
                # Update last update time if newer
                timestamp = convert_dt_to_tz(row.ts_datetime,mine_tz) 
                if timestamp:
                    formatted_time = timestamp.strftime("%I:%M%p").lower()                    
                    formatted_timestamp = f"{formatted_time}"
                    
                    if not sensor_data[sensor_id]["lastUpdate"] or timestamp > sensor_data[sensor_id]["lastUpdate"]["timestamp"]:
                        sensor_data[sensor_id]["lastUpdate"] = {
                            "timestamp": timestamp,
                            "formatted": formatted_timestamp
                        }
            
            # Add sensors to their respective sections
            for sensor_id, sensor in sensor_data.items():
                section_id = next((row.section_id for row in sensors_result if row.sensor_id == sensor_id), None)
                
                # Update the formatted lastUpdate
                if sensor["lastUpdate"]:
                    sensor["lastUpdate"] = sensor["lastUpdate"]["formatted"]
                
                if section_id in section_map:
                    section_map[section_id]["sensors"].append(sensor)
                    section_map[section_id]["sensorCount"] += 1
                else:
                    # Add to "No Section" if section not found
                    section_map[None]["sensors"].append(sensor)
                    section_map[None]["sensorCount"] += 1
            
            # Remove empty sections
            sections_data = [section for section in sections_data if section["sensorCount"] > 0]
            
            # Create response
            response = {
                "lastUpdatedTs": formatted_mine_current_datetime,
                "sections": sections_data
            }
            
            return response, None
            
        except Exception as e:
            logger.error(f"Error in get_live_environmental_data: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, str(e))

    def get_live_ventilation_data(self, mine_id: int) -> Tuple[Dict, APIError]:
        """Get live ventilation data for all sections in a mine.

        This function retrieves data from SensorRawData table for device type 72 (ventilation sensors)
        with specific module types:
        - Differential Pressure: module types 17 and 18
        - Compensated Pressure: module type 5001
        - Air Velocity: module type 5002
        - Air Quantity: module type 5003

        Args:
            mine_id: The mine ID to get data for

        Returns:
            Tuple containing the ventilation data response and any error
        """
        try:
            # Get mine timezone info
            mine_dict = get_mine_with_timezone(self, mine_id)
            if mine_dict is None:
                logger.error("Error in get_live_ventilation_data: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")

            # Get current time in mine's timezone
            mine_tz = mine_dict.get("tz_code") or pytz.utc
            mine_current_datetime = datetime.now(mine_tz)
            mine_current_date = mine_current_datetime.date()
            formatted_mine_current_datetime = mine_current_datetime.isoformat(timespec='milliseconds')

            # Get start and end of today in UTC using common function
            start_of_day_utc, end_of_day_utc = get_day_range_utc_from_mine_tz(mine_current_date, mine_tz)

            if start_of_day_utc is None or end_of_day_utc is None:
                logger.error("Error in get_live_ventilation_data: Failed to get day range")
                return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, "Failed to get day range")

            # Get all sections in the mine
            sections_stmt = select(
                Section.id,
                Section.ext_section_name
            ).where(
                Section.mine_id == mine_id
            ).order_by(
                Section.ext_section_name
            )

            sections_result = self.session.execute(sections_stmt).all()

            # Get latest data for each sensor/module type combination (current date only)
            latest_data_subquery = (
                select(
                    SensorRawData.sensor_device_id,
                    SensorRawData.module_type_id,
                    func.max(SensorRawData.ts_datetime).label("max_ts")
                )
                .select_from(SensorRawData)
                .join(SensorModuleType, SensorModuleType.id == SensorRawData.module_type_id)
                .where(
                    and_(
                        SensorRawData.is_active == 1,
                        SensorModuleType.value.in_([5001, 5002, 5003]),
                        SensorRawData.ts_datetime.between(start_of_day_utc, end_of_day_utc)
                    )
                )
                .group_by(
                    SensorRawData.sensor_device_id,
                    SensorRawData.module_type_id
                )
                .subquery()
            )

            # Special subquery for differential pressure - get the latest between module types 17 and 18
            latest_diff_pressure_subquery = (
                select(
                    SensorRawData.sensor_device_id,
                    func.max(SensorRawData.ts_datetime).label("max_ts")
                )
                .select_from(SensorRawData)
                .join(SensorModuleType, SensorModuleType.id == SensorRawData.module_type_id)
                .where(
                    and_(
                        SensorRawData.is_active == 1,
                        SensorModuleType.value.in_([17, 18]),
                        SensorRawData.ts_datetime.between(start_of_day_utc, end_of_day_utc)
                    )
                )
                .group_by(SensorRawData.sensor_device_id)
                .subquery()
            )

            # Query for differential pressure data (latest between 17 and 18)
            diff_pressure_stmt = (
                select(
                    SensorDevice.id.label("sensor_id"),
                    SensorDevice.label.label("sensor_label"),
                    SensorDevice.nid.label("sensor_nid"),
                    SensorDevice.comm_status.label("status"),
                    Section.id.label("section_id"),
                    Section.ext_section_name.label("section_name"),
                    SensorModuleType.id.label("module_type_id"),
                    SensorModuleType.value.label("module_type_value"),
                    SensorModuleType.display_name.label("module_type_name"),
                    SensorModuleType.unit,
                    SensorRawData.value,
                    SensorRawData.ts_datetime,
                    SensorModuleState.display_name.label("state_name")
                )
                .select_from(SensorRawData)
                .join(
                    latest_diff_pressure_subquery,
                    and_(
                        SensorRawData.sensor_device_id == latest_diff_pressure_subquery.c.sensor_device_id,
                        SensorRawData.ts_datetime == latest_diff_pressure_subquery.c.max_ts
                    )
                )
                .join(
                    SensorDevice,
                    SensorDevice.id == SensorRawData.sensor_device_id
                )
                .join(
                    DeviceTypes,
                    DeviceTypes.id == SensorDevice.device_type_id
                )
                .join(
                    SensorModuleType,
                    SensorModuleType.id == SensorRawData.module_type_id
                )
                .join(
                    SensorModuleState,
                    SensorModuleState.id == SensorRawData.module_state_id
                )
                .outerjoin(
                    Section,
                    Section.id == SensorDevice.section_id
                )
                .where(and_(
                    SensorDevice.mine_id == mine_id,
                    SensorDevice.is_active == 1,
                    DeviceTypes.value == 72,  # Only ventilation sensors
                    SensorModuleType.value.in_([17, 18])  # Differential pressure module types
                ))
                .order_by(
                    desc(SensorDevice.comm_status),
                    Section.ext_section_name,
                    SensorDevice.label
                    
                )
            )

            # Query for other module types (5001, 5002, 5003)
            other_modules_stmt = (
                select(
                    SensorDevice.id.label("sensor_id"),
                    SensorDevice.label.label("sensor_label"),
                    SensorDevice.nid.label("sensor_nid"),
                    SensorDevice.comm_status.label("status"),
                    Section.id.label("section_id"),
                    Section.ext_section_name.label("section_name"),
                    SensorModuleType.id.label("module_type_id"),
                    SensorModuleType.value.label("module_type_value"),
                    SensorModuleType.display_name.label("module_type_name"),
                    SensorModuleType.unit,
                    SensorRawData.value,
                    SensorRawData.ts_datetime,
                    SensorModuleState.display_name.label("state_name")
                )
                .select_from(SensorRawData)
                .join(
                    latest_data_subquery,
                    and_(
                        SensorRawData.sensor_device_id == latest_data_subquery.c.sensor_device_id,
                        SensorRawData.module_type_id == latest_data_subquery.c.module_type_id,
                        SensorRawData.ts_datetime == latest_data_subquery.c.max_ts
                    )
                )
                .join(
                    SensorDevice,
                    SensorDevice.id == SensorRawData.sensor_device_id
                )
                .join(
                    DeviceTypes,
                    DeviceTypes.id == SensorDevice.device_type_id
                )
                .join(
                    SensorModuleType,
                    SensorModuleType.id == SensorRawData.module_type_id
                )
                .join(
                    SensorModuleState,
                    SensorModuleState.id == SensorRawData.module_state_id
                )
                .outerjoin(
                    Section,
                    Section.id == SensorDevice.section_id
                )
                .where(and_(
                    SensorDevice.mine_id == mine_id,
                    DeviceTypes.value == 72,  # Only ventilation sensors
                    SensorModuleType.value.in_([5001, 5002, 5003])  # Other module types
                ))
                .order_by(
                    desc(SensorDevice.comm_status),
                    Section.ext_section_name,
                    SensorDevice.label
                )
            )

            # Execute queries
            diff_pressure_result = self.session.execute(diff_pressure_stmt).all()
            other_modules_result = self.session.execute(other_modules_stmt).all()

            # Combine results
            sensors_result = list(diff_pressure_result) + list(other_modules_result)

            # Create section map
            section_map = {}
            for section in sections_result:
                section_map[section.id] = {
                    "sectionId": section.id,
                    "sectionName": section.ext_section_name or "Unknown Section",
                    "sensorCount": 0,
                    "sensors": []
                }

            # Add "No Section" for sensors without section assignment
            section_map[None] = {
                "sectionId": None,
                "sectionName": "No Section",
                "sensorCount": 0,
                "sensors": []
            }
            
            # Group data by sensor
            sensor_data = {}
            for row in sensors_result:
                section_id = row.section_id
                sensor_id = row.sensor_id

                # Create sensor entry if it doesn't exist
                if sensor_id not in sensor_data:
                    sensor_data[sensor_id] = {
                        "sensorId": sensor_id,
                        "label": row.sensor_label,
                        "nid": int_to_hex(row.sensor_nid),  
                        "status": row.status,                     
                        "moduleData": {}
                    }

                # Map module types to readable names
                # For differential pressure, use generic name since we only get the latest one
                module_type_mapping = {
                    17: "DifferentialPressure",
                    18: "DifferentialPressure",
                    5001: "CompensatedPressure",
                    5002: "AirVelocity",
                    5003: "AirQuantity"
                }

                module_type_name = module_type_mapping.get(row.module_type_value, row.module_type_name)

                # For differential pressure, only keep the latest one if there are duplicates
                if module_type_name == "DifferentialPressure":
                    existing_data = sensor_data[sensor_id]["moduleData"].get(module_type_name)
                    if existing_data:
                        # Compare timestamps and keep the latest
                        existing_ts = existing_data["utctimestamp"] if existing_data["utctimestamp"] else None
                        current_ts = row.ts_datetime
                        if existing_ts and current_ts and current_ts <= existing_ts:
                            continue  # Skip this entry as we already have a more recent one

                sensor_data[sensor_id]["moduleData"][module_type_name] = {
                    "value": round(row.value,2),
                    "unit": row.unit,
                    "state": row.state_name,
                    "utctimestamp":row.ts_datetime,
                    "timestamp": convert_dt_to_tz(row.ts_datetime,mine_tz).strftime("%I:%M%p").lower() if row.ts_datetime else None,
                    "moduleTypeValue": row.module_type_value  # Include original module type for reference
                }               
                
            # Add sensors to their respective sections
            for sensor_id, sensor in sensor_data.items():
                section_id = next((row.section_id for row in sensors_result if row.sensor_id == sensor_id), None)
                if section_id in section_map:
                    section_map[section_id]["sensors"].append(sensor)
                    section_map[section_id]["sensorCount"] += 1
                else:
                    # Add to "No Section" if section not found
                    section_map[None]["sensors"].append(sensor)
                    section_map[None]["sensorCount"] += 1

            # Convert section map to list and remove empty sections
            sections_data = [section for section in section_map.values() if section["sensorCount"] > 0]

            # Create response
            response = {
                "lastUpdatedTs": formatted_mine_current_datetime,
                "sections": sections_data
            }

            return response, None

        except Exception as e:
            logger.error(f"Error in get_live_atmospheric_data: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, str(e))

    def get_gas_report(self, mine_id: int, start_date: date, end_date: date) -> Tuple[Dict, APIError]:
        """Get gas report data for a specific date range.

        Args:
            mine_id: The mine ID to get data for
            start_date: Start date for the report (inclusive)
            end_date: End date for the report (inclusive)

        Returns:
            Tuple containing the gas report response and any error
        """
        try:
            # Get mine timezone info
            mine_dict = get_mine_with_timezone(self, mine_id)
            if mine_dict is None:
                logger.error("Error in get_gas_report: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")

            # Get current time in mine's timezone
            mine_tz = mine_dict.get("tz_code") or pytz.utc
            mine_current_datetime = datetime.now(mine_tz)
            formatted_mine_current_datetime = mine_current_datetime.isoformat(timespec='milliseconds')

            # Convert start and end dates to UTC datetime range
            start_of_range = datetime.combine(start_date, datetime.min.time())
            start_of_range = mine_tz.localize(start_of_range)
            end_of_range = datetime.combine(end_date, datetime.max.time())
            end_of_range = mine_tz.localize(end_of_range)

            # Convert to UTC for database query
            start_of_range_utc = start_of_range.astimezone(pytz.UTC)
            end_of_range_utc = end_of_range.astimezone(pytz.UTC)

            # Get all sections in the mine
            sections_stmt = select(
                Section.id,
                Section.ext_section_name
            ).where(
                Section.mine_id == mine_id
            ).order_by(
                Section.ext_section_name
            )

            sections_result = self.session.execute(sections_stmt).all()

            # Query to get gas data with max and avg calculations from sensor raw data
            gas_data_stmt = (
                select(
                    SensorDevice.id.label("device_id"),
                    SensorDevice.label.label("device_name"),
                    SensorDevice.nid.label("device_code"),
                    SensorDevice.section_id.label("section_id"),
                    SensorModuleType.id.label("module_type_id"),
                    SensorModuleType.display_name.label("module_type_name"),
                    SensorModuleType.unit,
                    SensorModuleType.alert_min,
                    SensorModuleType.alert_max,
                    SensorModuleType.alarm_min,
                    SensorModuleType.alarm_max,
                    func.max(SensorRawData.value).label("max_value"),
                    func.avg(SensorRawData.value).label("avg_value")
                )
                .select_from(SensorRawData)
                .join(
                    SensorDevice,
                    SensorDevice.id == SensorRawData.sensor_device_id
                )
                .join(
                    DeviceTypes,
                    DeviceTypes.id == SensorDevice.device_type_id
                )
                .join(
                    SensorModuleType,
                    SensorModuleType.id == SensorRawData.module_type_id
                )
                .join(
                    SensorCategory,
                    SensorCategory.id == SensorModuleType.category_id
                )
                .where(
                    and_(
                        SensorDevice.mine_id == mine_id,                       
                        SensorRawData.is_active == 1,
                        DeviceTypes.value != 72,  # Exclude ventilation sensors
                        SensorCategory.name == "Gas",  # Include only gas category modules
                        SensorRawData.ts_datetime.between(start_of_range_utc, end_of_range_utc)
                    )
                )
                .group_by(
                    SensorDevice.id,
                    SensorDevice.label,
                    SensorDevice.nid,
                    SensorDevice.section_id,
                    SensorModuleType.id,
                    SensorModuleType.display_name,
                    SensorModuleType.unit,
                    SensorModuleType.alert_min,
                    SensorModuleType.alert_max,
                    SensorModuleType.alarm_min,
                    SensorModuleType.alarm_max,
                )
                .order_by(
                    SensorDevice.label,
                    SensorModuleType.display_name
                )
            )

            gas_data_result = self.session.execute(gas_data_stmt).all()      

            # Query to get alarm counts per device for the date range
            alarm_counts_stmt = (
                select(
                    SensorDevice.id.label("device_id"),
                    func.count(SensorRawData.id).label("alarm_count")
                )
                .select_from(SensorRawData)
                .join(
                    SensorDevice,
                    SensorDevice.id == SensorRawData.sensor_device_id
                )
                .join(
                    DeviceTypes,
                    DeviceTypes.id == SensorDevice.device_type_id
                )
                .join(
                    SensorModuleType,
                    SensorModuleType.id == SensorRawData.module_type_id
                )
                .join(
                    SensorCategory,
                    SensorCategory.id == SensorModuleType.category_id
                )
                .join(
                    SensorModuleState,
                    SensorModuleState.id == SensorRawData.module_state_id
                )
                .where(
                    and_(
                        SensorDevice.mine_id == mine_id,                       
                        SensorRawData.is_active == 1,
                        DeviceTypes.value != 72,  # Exclude ventilation sensors
                        SensorCategory.name == "Gas",  # Include only gas category modules
                        SensorModuleState.description == "Alarm",  # Only alarm states
                        SensorRawData.ts_datetime.between(start_of_range_utc, end_of_range_utc)
                    )
                )
                .group_by(SensorDevice.id)
            )
            
            

            alarm_counts_result = self.session.execute(alarm_counts_stmt).all()


            # Create a mapping of device_id to alarm count
            device_alarm_counts = {}
            for row in alarm_counts_result:
                device_alarm_counts[row.device_id] = row.alarm_count
          

            # Find the device with maximum number of gas module types
            device_module_counts = {}
            for row in gas_data_result:
                device_id = row.device_id
                if device_id not in device_module_counts:
                    device_module_counts[device_id] = set()
                device_module_counts[device_id].add(row.module_type_name)

            # Get all unique module types for reference
            all_module_types = set()
            for row in gas_data_result:
                all_module_types.add(row.module_type_name)


            # Organize data by section
            sections_data = []
            for section in sections_result:
                section_data = {
                    "sectionId": section.id,
                    "sectionName": section.ext_section_name,
                    "devices": []
                }
                sections_data.append(section_data)

            # Add "No Section" for devices without a section
            no_section_data = {
                "sectionId": None,
                "sectionName": "No Section",
                "devices": []
            }
            sections_data.append(no_section_data)

            # Create a mapping for easy access
            section_map = {section["sectionId"]: section for section in sections_data}

            # Group data by device
            device_data = {}

            for row in gas_data_result:
                device_id = row.device_id

                # Create device entry if it doesn't exist
                if device_id not in device_data:
                    device_data[device_id] = {
                        "deviceId": device_id,
                        "deviceName": row.device_name,
                        "deviceCode": int_to_hex(row.device_code),
                        "sectionId": row.section_id,
                        "alarmCount": device_alarm_counts.get(device_id, 0),  # Add alarm count
                        "gasData": {},
                        "gas_counter": 1  # Counter for this device's gas numbering
                    }

                # Generate gas name based on counter (gas1, gas2, gas3, etc.)
                gas_name = f"gas{device_data[device_id]['gas_counter']}"
                device_data[device_id]['gas_counter'] += 1
                maxValue = round(float(row.max_value), 2) if row.max_value is not None else 0.0
                # Add gas module data
                device_data[device_id]["gasData"][gas_name] = {
                    "moduleType": row.module_type_name,
                    "unit": row.unit,
                    "maxValue": maxValue,
                    "avgValue": round(float(row.avg_value), 2) if row.avg_value is not None else 0.0,
                    "color": "W" if row.module_type_name != "CO" and row.module_type_name != "CH4" else ( "Y" if row.alert_min <= maxValue <= row.alert_max else "R" if row.alarm_min <= maxValue else "W" )   
                }

            # Add devices to their respective sections
            for device_id, device in device_data.items():
                # Remove the gas_counter and sectionId before adding to section
                device_copy = device.copy()
                device_copy.pop('gas_counter', None)
                section_id = device_copy.pop('sectionId', None)

                if section_id in section_map:
                    section_map[section_id]["devices"].append(device_copy)
                else:
                    # Add to "No Section" if section not found
                    section_map[None]["devices"].append(device_copy)

            # Create response
            response = {
                "lastUpdatedTs": formatted_mine_current_datetime,
                "startDate": start_date.isoformat(),
                "endDate": end_date.isoformat(),                
                "sections": sections_data
            }

            return response, None

        except Exception as e:
            logger.error(f"Error in get_gas_report: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, str(e))

    def get_environmental_report(self, mine_id: int, start_date: date, end_date: date) -> Tuple[Dict, APIError]:
        """Get environmental report data for a specific date range.

        This method retrieves environmental sensor data (Pressure=16, Temperature=6, Humidity=4)
        with average values and alarm counts, organized by sections.

        Args:
            mine_id: The mine ID to get data for
            start_date: Start date for the report (inclusive)
            end_date: End date for the report (inclusive)

        Returns:
            Tuple containing the environmental report response and any error
        """
        try:
            # Get mine timezone info
            mine_dict = get_mine_with_timezone(self, mine_id)
            if mine_dict is None:
                logger.error("Error in get_environmental_report: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")

            mine_tz = mine_dict.get("tz_code") or pytz.utc

            mine_current_datetime = datetime.now(mine_tz)
            formatted_mine_current_datetime = mine_current_datetime.isoformat(timespec='milliseconds')

            # Convert start and end dates to UTC datetime range
            start_of_range = datetime.combine(start_date, datetime.min.time())
            start_of_range = mine_tz.localize(start_of_range)
            end_of_range = datetime.combine(end_date, datetime.max.time())
            end_of_range = mine_tz.localize(end_of_range)

            # Convert to UTC for database query
            start_of_range_utc = start_of_range.astimezone(pytz.UTC)
            end_of_range_utc = end_of_range.astimezone(pytz.UTC)
            
            # Get all sections in the mine
            sections_stmt = select(
                Section.id,
                Section.ext_section_name
            ).where(
                Section.mine_id == mine_id
            ).order_by(
                Section.ext_section_name
            )

            sections_result = self.session.execute(sections_stmt).all()

            # Query to get environmental data with avg calculations from sensor raw data
            # Environmental module types: 16 (Pressure), 6 (Temperature), 4 (Humidity)
            environmental_data_stmt = (
                select(
                    SensorDevice.id.label("device_id"),
                    SensorDevice.label.label("device_name"),
                    SensorDevice.nid.label("device_code"),
                    SensorDevice.section_id.label("section_id"),
                    SensorModuleType.id.label("module_type_id"),
                    SensorModuleType.value.label("module_type_value"),
                    SensorModuleType.display_name.label("module_type_name"),
                    SensorModuleType.unit,
                    func.avg(SensorRawData.value).label("avg_value")
                )
                .select_from(SensorRawData)
                .join(
                    SensorDevice,
                    SensorDevice.id == SensorRawData.sensor_device_id
                )
                .join(
                    SensorModuleType,
                    SensorModuleType.id == SensorRawData.module_type_id
                )
                .where(
                    and_(
                        SensorDevice.mine_id == mine_id,
                        SensorRawData.is_active == 1,
                        SensorModuleType.value.in_([16, 6, 4]),  # Pressure, Temperature, Humidity
                        SensorRawData.ts_datetime.between(start_of_range_utc, end_of_range_utc)
                    )
                )
                .group_by(
                    SensorDevice.id,
                    SensorDevice.label,
                    SensorDevice.nid,
                    SensorDevice.section_id,
                    SensorModuleType.id,
                    SensorModuleType.value,
                    SensorModuleType.display_name,
                    SensorModuleType.unit,
                )
                .order_by(
                    SensorDevice.label,
                    SensorModuleType.display_name
                )
            )

            environmental_data_result = self.session.execute(environmental_data_stmt).all()       

            # Query to get alarm counts per device for the date range
            alarm_counts_stmt = (
                select(
                    SensorDevice.id.label("device_id"),
                    func.count(SensorRawData.id).label("alarm_count")
                )
                .select_from(SensorRawData)
                .join(
                    SensorDevice,
                    SensorDevice.id == SensorRawData.sensor_device_id
                )
                .join(
                    SensorModuleType,
                    SensorModuleType.id == SensorRawData.module_type_id
                )
                .join(
                    SensorModuleState,
                    SensorModuleState.id == SensorRawData.module_state_id
                )
                .where(
                    and_(
                        SensorDevice.mine_id == mine_id,
                        SensorRawData.is_active == 1,
                        SensorModuleType.value.in_([16, 6, 4]),  # Pressure, Temperature, Humidity
                        SensorModuleState.description == "Alarm",  # Only alarm states
                        SensorRawData.ts_datetime.between(start_of_range_utc, end_of_range_utc)
                    )
                )
                .group_by(SensorDevice.id)
            )


            alarm_counts_result = self.session.execute(alarm_counts_stmt).all()
 

            # Create a mapping of device_id to alarm count
            device_alarm_counts = {}
            for row in alarm_counts_result:
                device_alarm_counts[row.device_id] = row.alarm_count

            # Organize data by section
            sections_data = []
            for section in sections_result:
                section_data = {
                    "sectionId": section.id,
                    "sectionName": section.ext_section_name,
                    "devices": []
                }
                sections_data.append(section_data)

            # Add "No Section" for devices without a section
            no_section_data = {
                "sectionId": None,
                "sectionName": "No Section",
                "devices": []
            }
            sections_data.append(no_section_data)

            # Create a mapping for easy access
            section_map = {section["sectionId"]: section for section in sections_data}

            # Group data by device
            device_data = {}

            for row in environmental_data_result:
                device_id = row.device_id

                # Create device entry if it doesn't exist
                if device_id not in device_data:
                    device_data[device_id] = {
                        "deviceId": device_id,
                        "deviceName": row.device_name,
                        "deviceCode": int_to_hex(row.device_code),
                        "sectionId": row.section_id,
                        "alarmCount": device_alarm_counts.get(device_id, 0),  # Add alarm count
                        "environmentalData": {}
                    }

                # Map module type value to environmental type name
                env_type_mapping = {
                    16: "pressure",
                    4: "temperature",
                    6: "humidity"
                }

                env_type = env_type_mapping.get(row.module_type_value, f"type_{row.module_type_value}")
                avg_value = round(float(row.avg_value), 2) if row.avg_value is not None else 0.0
                # Add environmental module data
                device_data[device_id]["environmentalData"][env_type] = {
                    "moduleType": row.module_type_name ,
                    "unit": row.unit if row.module_type_name != "Temperature" else "°F" ,
                    "avgValue": avg_value if row.module_type_name != "Temperature" else CelsiusToFahrenheit(avg_value)  
                }

            # Add devices to their respective sections
            for device_id, device in device_data.items():
                # Remove the sectionId before adding to section
                device_copy = device.copy()
                section_id = device_copy.pop('sectionId', None)

                if section_id in section_map:
                    section_map[section_id]["devices"].append(device_copy)
                else:
                    # Add to "No Section" if section not found
                    section_map[None]["devices"].append(device_copy)

            # Create response
            response = {
                "lastUpdatedTs": formatted_mine_current_datetime,
                "startDate": start_date.isoformat(),
                "endDate": end_date.isoformat(),                
                "sections": sections_data
            }

            return response, None

        except Exception as e:
            logger.error(f"Error in get_environmental_report: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, str(e))

    def get_ventilation_report(self, mine_id: int, start_date: date, end_date: date) -> Tuple[Dict, APIError]:
        """Get ventilation report data for a specific date range.

        This method retrieves ventilation sensor data (Differential Pressure=17,18,
        Compensated Pressure=5001, Air Velocity=5002, Air Quantity=5003)
        with average values and alarm counts, organized by sections.

        Args:
            mine_id: The mine ID to get data for
            start_date: Start date for the report (inclusive)
            end_date: End date for the report (inclusive)

        Returns:
            Tuple containing the ventilation report response and any error
        """
        try:
            # Get mine timezone info
            mine_dict = get_mine_with_timezone(self, mine_id)
            if mine_dict is None:
                logger.error("Error in get_ventilation_report: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")

            mine_tz = mine_dict.get("tz_code") or pytz.utc

            mine_current_datetime = datetime.now(mine_tz)
            formatted_mine_current_datetime = mine_current_datetime.isoformat(timespec='milliseconds')

            # Convert start and end dates to UTC datetime range
            start_of_range = datetime.combine(start_date, datetime.min.time())
            start_of_range = mine_tz.localize(start_of_range)
            end_of_range = datetime.combine(end_date, datetime.max.time())
            end_of_range = mine_tz.localize(end_of_range)

            # Convert to UTC for database query
            start_of_range_utc = start_of_range.astimezone(pytz.UTC)
            end_of_range_utc = end_of_range.astimezone(pytz.UTC)

            # Get all sections in the mine
            sections_stmt = select(
                Section.id,
                Section.ext_section_name
            ).where(
                Section.mine_id == mine_id
            ).order_by(
                Section.ext_section_name
            )

            sections_result = self.session.execute(sections_stmt).all()

            # Query to get ventilation data with avg calculations from sensor raw data
            # Ventilation module types: 17,18 (Differential Pressure), 5001 (Compensated Pressure),
            # 5002 (Air Velocity), 5003 (Air Quantity)
            ventilation_data_stmt = (
                select(
                    SensorDevice.id.label("device_id"),
                    SensorDevice.label.label("device_name"),
                    SensorDevice.nid.label("device_code"),
                    SensorDevice.section_id.label("section_id"),
                    SensorModuleType.id.label("module_type_id"),
                    SensorModuleType.value.label("module_type_value"),
                    SensorModuleType.display_name.label("module_type_name"),
                    SensorModuleType.unit,
                    func.avg(SensorRawData.value).label("avg_value")
                )
                .select_from(SensorRawData)
                .join(
                    SensorDevice,
                    SensorDevice.id == SensorRawData.sensor_device_id
                )
                .join(
                    SensorModuleType,
                    SensorModuleType.id == SensorRawData.module_type_id
                )
                .where(
                    and_(
                        SensorDevice.mine_id == mine_id,
                        SensorRawData.is_active == 1,
                        SensorModuleType.value.in_([17, 18, 5001, 5002, 5003]),  # Ventilation module types
                        SensorRawData.ts_datetime.between(start_of_range_utc, end_of_range_utc)
                    )
                )
                .group_by(
                    SensorDevice.id,
                    SensorDevice.label,
                    SensorDevice.nid,
                    SensorDevice.section_id,
                    SensorModuleType.id,
                    SensorModuleType.value,
                    SensorModuleType.display_name,
                    SensorModuleType.unit
                )
                .order_by(
                    SensorDevice.label,
                    SensorModuleType.display_name
                )
            )

            ventilation_data_result = self.session.execute(ventilation_data_stmt).all()           

            # Query to get alarm counts per device for the date range
            alarm_counts_stmt = (
                select(
                    SensorDevice.id.label("device_id"),
                    func.count(SensorRawData.id).label("alarm_count")
                )
                .select_from(SensorRawData)
                .join(
                    SensorDevice,
                    SensorDevice.id == SensorRawData.sensor_device_id
                )
                .join(
                    SensorModuleType,
                    SensorModuleType.id == SensorRawData.module_type_id
                )
                .join(
                    SensorModuleState,
                    SensorModuleState.id == SensorRawData.module_state_id
                )
                .where(
                    and_(
                        SensorDevice.mine_id == mine_id,
                        SensorRawData.is_active == 1,
                        SensorModuleType.value.in_([17, 18, 5001, 5002, 5003]),  # Ventilation module types
                        SensorModuleState.description == "Alarm",  # Only alarm states
                        SensorRawData.ts_datetime.between(start_of_range_utc, end_of_range_utc)
                    )
                )
                .group_by(SensorDevice.id)
            )

            alarm_counts_result = self.session.execute(alarm_counts_stmt).all()
           
            
            # Create a mapping of device_id to alarm count
            device_alarm_counts = {}
            for row in alarm_counts_result:
                device_alarm_counts[row.device_id] = row.alarm_count

            # Organize data by section
            sections_data = []
            for section in sections_result:
                section_data = {
                    "sectionId": section.id,
                    "sectionName": section.ext_section_name,
                    "devices": []
                }
                sections_data.append(section_data)

            # Add "No Section" for devices without a section
            no_section_data = {
                "sectionId": None,
                "sectionName": "No Section",
                "devices": []
            }
            sections_data.append(no_section_data)

            # Create a mapping for easy access
            section_map = {section["sectionId"]: section for section in sections_data}

            # Group data by device
            device_data = {}

            for row in ventilation_data_result:
                device_id = row.device_id

                # Create device entry if it doesn't exist
                if device_id not in device_data:
                    device_data[device_id] = {
                        "deviceId": device_id,
                        "deviceName": row.device_name,
                        "deviceCode": int_to_hex(row.device_code),
                        "sectionId": row.section_id,
                        "alarmCount": device_alarm_counts.get(device_id, 0),  # Add alarm count
                        "ventilationData": {}
                    }

                # Map module type value to ventilation type name
                # 17 and 18 are both Differential Pressure, treat them as the same
                ventilation_type_mapping = {
                    17: "differentialPressure",
                    18: "differentialPressure",
                    5001: "compensatedPressure",
                    5002: "airVelocity",
                    5003: "airQuantity"
                }

                ventilation_type = ventilation_type_mapping.get(row.module_type_value, f"type_{row.module_type_value}")

                # For differential pressure (17 and 18), we need to handle potential duplicates
                # by combining or averaging the values if both module types exist for the same device
                if ventilation_type in device_data[device_id]["ventilationData"]:
                    # If this ventilation type already exists, average the values
                    existing_avg = device_data[device_id]["ventilationData"][ventilation_type]["avgValue"]
                    new_avg = round(float(row.avg_value), 2) if row.avg_value is not None else 0.0
                    combined_avg = round((existing_avg + new_avg) / 2, 2)
                    device_data[device_id]["ventilationData"][ventilation_type]["avgValue"] = combined_avg
                else:
                    # Add ventilation module data
                    device_data[device_id]["ventilationData"][ventilation_type] = {
                        "moduleType": "Differential Pressure" if ventilation_type == "differentialPressure" else row.module_type_name,
                        "unit": row.unit,
                        "avgValue": round(float(row.avg_value), 2) if row.avg_value is not None else 0.0
                    }

            # Add devices to their respective sections
            for device_id, device in device_data.items():
                # Remove the sectionId before adding to section
                device_copy = device.copy()
                section_id = device_copy.pop('sectionId', None)

                if section_id in section_map:
                    section_map[section_id]["devices"].append(device_copy)
                else:
                    # Add to "No Section" if section not found
                    section_map[None]["devices"].append(device_copy)

            # Create response
            response = {
                "lastUpdatedTs": formatted_mine_current_datetime,
                "startDate": start_date.isoformat(),
                "endDate": end_date.isoformat(),                
                "sections": sections_data
            }

            return response, None

        except Exception as e:
            logger.error(f"Error in get_ventilation_report: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, str(e))

    def get_gas_graph_data(self, mine_id: int, device_ids: str, start_date: date, end_date: date) -> Tuple[Dict, APIError]:
        """Get gas graph data for specific devices and date range.

        Returns daily aggregated data for multi-day ranges, hourly data for same-day ranges.
        Uses SensorDataAggrDaily and SensorDataAggrHourly tables for gas module data.

        Args:
            mine_id: The mine ID to get data for
            device_ids: Comma-separated string of device IDs
            start_date: Start date for the data (inclusive)
            end_date: End date for the data (inclusive)

        Returns:
            Tuple containing the gas graph data response and any error
        """
        try:
            # Parse device IDs from comma-separated string
            try:
                device_id_list = [int(device_id.strip()) for device_id in device_ids.split(',') if device_id.strip()]
            except ValueError:
                return None, APIError(400, APIErrorType.INVALID_REQUEST, "Invalid device IDs format")

            if not device_id_list:
                return None, APIError(400, APIErrorType.INVALID_REQUEST, "No valid device IDs provided")

            # Get mine timezone info
            mine_dict = get_mine_with_timezone(self, mine_id)
            if mine_dict is None:
                logger.error("Error in get_gas_graph_data: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")

            mine_tz = mine_dict.get("tz_code") or pytz.utc

            mine_current_datetime = datetime.now(mine_tz)
            formatted_mine_current_datetime = mine_current_datetime.isoformat(timespec='milliseconds')

            # Convert start and end dates to UTC datetime range
            start_of_range = datetime.combine(start_date, datetime.min.time())
            start_of_range = mine_tz.localize(start_of_range)
            end_of_range = datetime.combine(end_date, datetime.max.time())
            end_of_range = mine_tz.localize(end_of_range)

            # Determine if we need hourly or daily data
            is_same_day = start_date == end_date

            # Generate x-axis data for charts
            if is_same_day:
                # Generate hour array (0-23) for same day
                x_axis_data = list(range(24))  # [0, 1, 2, ..., 23]
                x_axis_labels = [f"{(hour % 12) or 12:02d}:00 {'AM' if hour < 12 else 'PM'}" for hour in range(24)]  # ["00:00 AM", "01:00 AM", ..., "11:00 PM"]
            else:
                # Generate date array for date range
                x_axis_data = []
                x_axis_labels = []
                current_date = start_date
                while current_date <= end_date:
                    x_axis_data.append(current_date.isoformat())
                    x_axis_labels.append(current_date.strftime("%b-%d"))
                    current_date += timedelta(days=1)

            # Get device information first
            device_info_stmt = (
                select(
                    SensorDevice.id.label("device_id"),
                    SensorDevice.label.label("device_name"),
                    SensorDevice.nid.label("device_code")
                )
                .where(
                    and_(
                        SensorDevice.mine_id == mine_id,
                        SensorDevice.id.in_(device_id_list)
                        
                    )
                )
                .order_by(SensorDevice.label)
            )

            device_info_result = self.session.execute(device_info_stmt).all()

            if not device_info_result:
                return None, APIError(404, APIErrorType.DEVICE_NOT_FOUND, "No valid devices found")

            # Get gas module types for filtering
            gas_category_stmt = select(SensorCategory.id).where(SensorCategory.name == "Gas")
            gas_category_result = self.session.execute(gas_category_stmt).first()

            if not gas_category_result:
                return None, APIError(404, APIErrorType.DATA_NOT_FOUND, "Gas category not found")

            gas_category_id = gas_category_result.id

            if is_same_day:
                # Get hourly data for same day
                data_stmt = (
                    select(
                        SensorDataAggrHourly.sensor_device_id.label("device_id"),
                        SensorDataAggrHourly.module_type_id.label("module_type_id"),
                        SensorDataAggrHourly.hour.label("hour"),
                        SensorDataAggrHourly.max_value.label("max_value"),
                        SensorModuleType.display_name.label("module_type_name"),
                        SensorModuleType.unit.label("unit"),
                        SensorModuleType.alert_min.label("alert_min"),
                        SensorModuleType.alert_max.label("alert_max"),
                        SensorModuleType.alarm_min.label("alarm_min"),
                        SensorModuleType.alarm_max.label("alarm_max")
                    )
                    .select_from(SensorDataAggrHourly)
                    .join(
                        SensorModuleType,
                        SensorModuleType.id == SensorDataAggrHourly.module_type_id
                    )
                    .where(
                        and_(
                            SensorDataAggrHourly.sensor_device_id.in_(device_id_list),
                            SensorDataAggrHourly.category_id == gas_category_id,
                            SensorDataAggrHourly.mine_date == start_date,
                            SensorDataAggrHourly.is_active == 1
                        )
                    )
                    .order_by(
                        SensorDataAggrHourly.sensor_device_id,
                        SensorDataAggrHourly.module_type_id,
                        SensorDataAggrHourly.hour
                    )
                )
            else:
                # Get daily data for multi-day range
                data_stmt = (
                    select(
                        SensorDataAggrDaily.sensor_device_id.label("device_id"),
                        SensorDataAggrDaily.module_type_id.label("module_type_id"),
                        SensorDataAggrDaily.mine_date.label("date"),
                        SensorDataAggrDaily.max_value.label("max_value"),
                        SensorModuleType.display_name.label("module_type_name"),
                        SensorModuleType.unit.label("unit"),
                        SensorModuleType.alert_min.label("alert_min"),
                        SensorModuleType.alert_max.label("alert_max"),
                        SensorModuleType.alarm_min.label("alarm_min"),
                        SensorModuleType.alarm_max.label("alarm_max")
                    )
                    .select_from(SensorDataAggrDaily)
                    .join(
                        SensorModuleType,
                        SensorModuleType.id == SensorDataAggrDaily.module_type_id
                    )
                    .where(
                        and_(
                            SensorDataAggrDaily.sensor_device_id.in_(device_id_list),
                            SensorDataAggrDaily.category_id == gas_category_id,
                            SensorDataAggrDaily.mine_date.between(start_date, end_date),
                            SensorDataAggrDaily.is_active == 1
                        )
                    )
                    .order_by(
                        SensorDataAggrDaily.sensor_device_id,
                        SensorDataAggrDaily.module_type_id,
                        SensorDataAggrDaily.mine_date
                    )
                )

            data_result = self.session.execute(data_stmt).all()   

            # Process the data
            devices_data = []
            unique_modules = {}  # To track unique modules across all devices

            for device_info in device_info_result:
                device_id = device_info.device_id

                # Get all data for this device
                device_data_rows = [row for row in data_result if row.device_id == device_id]

                if not device_data_rows:
                    continue

                # Group by module type
                modules_data = {}
                for row in device_data_rows:
                    module_key = f"{row.module_type_id}_{row.module_type_name}"

                    # Track unique modules across all devices
                    if row.module_type_id not in unique_modules:
                        unique_modules[row.module_type_id] = {
                            "moduleId": row.module_type_id,
                            "moduleName": row.module_type_name,
                            "alertMin": row.alert_min,
                            "alertMax": row.alert_max,
                            "alarmMin": row.alarm_min,
                            "alarmMax": row.alarm_max
                        }

                    if module_key not in modules_data:
                        modules_data[module_key] = {
                            "moduleType": row.module_type_name,
                            "moduleTypeId": row.module_type_id,
                            "unit": row.unit,
                            "data": []
                        }

                    if is_same_day:
                        # Hourly data
                        modules_data[module_key]["data"].append({
                            "data": row.hour,
                            "value": round(float(row.max_value), 2) if row.max_value is not None else 0.0
                        })
                    else:
                        # Daily data
                        modules_data[module_key]["data"].append({
                            "data": row.date.isoformat(),
                            "value": round(float(row.max_value), 2) if row.max_value is not None else 0.0
                        })

                # Convert modules_data to list format with gas naming
                modules_list = []
             
                for module_key, module_data in modules_data.items():
                    modules_list.append({
                        
                        "moduleType": module_data["moduleType"],
                        "moduleTypeId": module_data["moduleTypeId"],
                        "unit": module_data["unit"],
                        "data": module_data["data"]
                    })
                    

                device_entry = {
                    "deviceId": device_id,
                    "deviceName": device_info.device_name,
                    "deviceCode": int_to_hex(device_info.device_code),
                    "modules": modules_list
                }

                devices_data.append(device_entry)

            # Convert unique modules to list
            unique_modules_list = list(unique_modules.values())

            # Create response
            response = {
                "lastUpdatedTs": formatted_mine_current_datetime,
                "startDate": start_date.isoformat(),
                "endDate": end_date.isoformat(),
                "dataType": "hourly" if is_same_day else "daily",
                "xAxisData": x_axis_data,
                "xAxisLabels": x_axis_labels,
                "uniqueModules": unique_modules_list,        
                "devices": devices_data
            }

            return response, None

        except Exception as e:
            logger.error(f"Error in get_gas_graph_data: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, str(e))

    def get_environmental_graph_data(self, mine_id: int, device_ids: str, start_date: date, end_date: date) -> Tuple[Dict, APIError]:
        """Get environmental graph data for specific devices and date range.

        Returns daily aggregated data for multi-day ranges, hourly data for same-day ranges.
        Uses SensorDataAggrDaily and SensorDataAggrHourly tables for environmental module data.
        Environmental modules include: Temperature, Relative Humidity, Absolute Pressure.

        Args:
            mine_id: The mine ID to get data for
            device_ids: Comma-separated string of device IDs
            start_date: Start date for the data (inclusive)
            end_date: End date for the data (inclusive)

        Returns:
            Tuple containing the environmental graph data response and any error
        """
        try:
            # Parse device IDs from comma-separated string
            try:
                device_id_list = [int(device_id.strip()) for device_id in device_ids.split(',') if device_id.strip()]
            except ValueError:
                return None, APIError(400, APIErrorType.INVALID_REQUEST, "Invalid device IDs format")

            if not device_id_list:
                return None, APIError(400, APIErrorType.INVALID_REQUEST, "No valid device IDs provided")

            # Get mine timezone info
            mine_dict = get_mine_with_timezone(self, mine_id)
            if mine_dict is None:
                logger.error("Error in get_environmental_graph_data: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")

            mine_tz = mine_dict.get("tz_code") or pytz.utc
           
            # Get current datetime in mine's timezone for response
            mine_current_datetime = datetime.now(mine_tz)
            formatted_mine_current_datetime = mine_current_datetime.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + mine_current_datetime.strftime("%z")
            formatted_mine_current_datetime = formatted_mine_current_datetime[:-2] + ":" + formatted_mine_current_datetime[-2:]

            # Convert start and end dates to UTC datetime range
            start_of_range = datetime.combine(start_date, datetime.min.time())
            start_of_range = mine_tz.localize(start_of_range)
            end_of_range = datetime.combine(end_date, datetime.max.time())
            end_of_range = mine_tz.localize(end_of_range)

            # Determine if we need hourly or daily data
            is_same_day = start_date == end_date

            # Generate x-axis data for charts
            if is_same_day:
                # Generate hour array (0-23) for same day
                x_axis_data = list(range(24))  # [0, 1, 2, ..., 23]
                x_axis_labels = [f"{(hour % 12) or 12:02d}:00 {'AM' if hour < 12 else 'PM'}" for hour in range(24)]  # ["00:00 AM", "01:00 AM", ..., "11:00 PM"]
            else:
                # Generate date array for date range
                x_axis_data = []
                x_axis_labels = []
                current_date = start_date
                while current_date <= end_date:
                    x_axis_data.append(current_date.isoformat())
                    x_axis_labels.append(current_date.strftime("%b-%d"))
                    current_date += timedelta(days=1)

            # Get device information first
            device_info_stmt = (
                select(
                    SensorDevice.id.label("device_id"),
                    SensorDevice.label.label("device_name"),
                    SensorDevice.nid.label("device_code")
                )
                .where(
                    and_(
                        SensorDevice.mine_id == mine_id,
                        SensorDevice.id.in_(device_id_list)
                       
                    )
                )
                .order_by(SensorDevice.label)
            )

            device_info_result = self.session.execute(device_info_stmt).all()

            if not device_info_result:
                return None, APIError(404, APIErrorType.DEVICE_NOT_FOUND, "No valid devices found")

            # Get environmental category for filtering
            env_category_stmt = select(SensorCategory.id).where(SensorCategory.name == "Environment")
            env_category_result = self.session.execute(env_category_stmt).first()

            if not env_category_result:
                return None, APIError(404, APIErrorType.DATA_NOT_FOUND, "Environment category not found")

            env_category_id = env_category_result.id

            if is_same_day:
                # Get hourly data for same day
                data_stmt = (
                    select(
                        SensorDataAggrHourly.sensor_device_id.label("device_id"),
                        SensorDataAggrHourly.module_type_id.label("module_type_id"),
                        SensorDataAggrHourly.hour.label("hour"),
                        SensorDataAggrHourly.max_value.label("max_value"),
                        SensorModuleType.display_name.label("module_type_name"),
                        SensorModuleType.unit.label("unit"),
                        SensorModuleType.alert_min.label("alert_min"),
                        SensorModuleType.alert_max.label("alert_max"),
                        SensorModuleType.alarm_min.label("alarm_min"),
                        SensorModuleType.alarm_max.label("alarm_max")
                    )
                    .select_from(SensorDataAggrHourly)
                    .join(
                        SensorModuleType,
                        SensorModuleType.id == SensorDataAggrHourly.module_type_id
                    )
                    .where(
                        and_(
                            SensorDataAggrHourly.sensor_device_id.in_(device_id_list),
                            SensorDataAggrHourly.category_id == env_category_id,
                            SensorDataAggrHourly.mine_date == start_date,
                            SensorDataAggrHourly.is_active == 1
                        )
                    )
                    .order_by(
                        SensorDataAggrHourly.sensor_device_id,
                        SensorDataAggrHourly.module_type_id,
                        SensorDataAggrHourly.hour
                    )
                )
            else:
                # Get daily data for multi-day range
                data_stmt = (
                    select(
                        SensorDataAggrDaily.sensor_device_id.label("device_id"),
                        SensorDataAggrDaily.module_type_id.label("module_type_id"),
                        SensorDataAggrDaily.mine_date.label("date"),
                        SensorDataAggrDaily.max_value.label("max_value"),
                        SensorModuleType.display_name.label("module_type_name"),
                        SensorModuleType.unit.label("unit"),
                        SensorModuleType.alert_min.label("alert_min"),
                        SensorModuleType.alert_max.label("alert_max"),
                        SensorModuleType.alarm_min.label("alarm_min"),
                        SensorModuleType.alarm_max.label("alarm_max")
                    )
                    .select_from(SensorDataAggrDaily)
                    .join(
                        SensorModuleType,
                        SensorModuleType.id == SensorDataAggrDaily.module_type_id
                    )
                    .where(
                        and_(
                            SensorDataAggrDaily.sensor_device_id.in_(device_id_list),
                            SensorDataAggrDaily.category_id == env_category_id,
                            SensorDataAggrDaily.mine_date.between(start_date, end_date),
                            SensorDataAggrDaily.is_active == 1
                        )
                    )
                    .order_by(
                        SensorDataAggrDaily.sensor_device_id,
                        SensorDataAggrDaily.module_type_id,
                        SensorDataAggrDaily.mine_date
                    )
                )

            data_result = self.session.execute(data_stmt).all()
           
            # Process the data
            devices_data = []
            unique_modules = {}  # To track unique modules across all devices

            for device_info in device_info_result:
                device_id = device_info.device_id

                # Get all data for this device
                device_data_rows = [row for row in data_result if row.device_id == device_id]

                if not device_data_rows:
                    continue

                # Group by module type
                modules_data = {}
                for row in device_data_rows:
                    module_key = f"{row.module_type_id}_{row.module_type_name}"

                    # Track unique modules across all devices
                    if row.module_type_id not in unique_modules:
                        unique_modules[row.module_type_id] = {
                            "moduleId": row.module_type_id,
                            "moduleName": row.module_type_name,
                            "alertMin": row.alert_min,
                            "alertMax": row.alert_max,
                            "alarmMin": row.alarm_min,
                            "alarmMax": row.alarm_max
                        }


                    if module_key not in modules_data:
                        modules_data[module_key] = {
                            "moduleType": row.module_type_name,
                            "moduleTypeId": row.module_type_id,
                            "unit": row.unit if row.module_type_name != "Temperature" else "°F",
                            "data": []
                        }
                    max_value = round(float(row.max_value), 2) if row.max_value is not None else 0.0
                    if is_same_day:
                        # Hourly data
                        modules_data[module_key]["data"].append({
                            "data": row.hour,
                            "value": max_value if row.module_type_name != "Temperature" else CelsiusToFahrenheit(max_value)
                        })
                    else:
                        # Daily data
                        modules_data[module_key]["data"].append({
                            "data": row.date.isoformat(),
                            "value": max_value if row.module_type_name != "Temperature" else CelsiusToFahrenheit(max_value)
                        })

                # Convert modules_data to list format
                modules_list = []
                for module_key, module_data in modules_data.items():
                    modules_list.append({
                        "moduleType": module_data["moduleType"],
                        "moduleTypeId": module_data["moduleTypeId"],
                        "unit": module_data["unit"],
                        "data": module_data["data"]
                    })

                device_entry = {
                    "deviceId": device_id,
                    "deviceName": device_info.device_name,
                    "deviceCode": int_to_hex(device_info.device_code),
                    "modules": modules_list
                }

                devices_data.append(device_entry)

            # Convert unique modules to list
            unique_modules_list = list(unique_modules.values())

            # Create response
            response = {
                "lastUpdatedTs": formatted_mine_current_datetime,
                "startDate": start_date.isoformat(),
                "endDate": end_date.isoformat(),
                "dataType": "hourly" if is_same_day else "daily",
                "xAxisData": x_axis_data,
                "xAxisLabels": x_axis_labels,
                "uniqueModules": unique_modules_list,             
                "devices": devices_data
            }

            return response, None

        except Exception as e:
            logger.error(f"Error in get_environmental_graph_data: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, str(e))

    def get_ventilation_graph_data(self, mine_id: int, device_ids: str, start_date: date, end_date: date) -> Tuple[Dict, APIError]:
        """Get ventilation graph data for specific devices and date range.

        Returns daily aggregated data for multi-day ranges, hourly data for same-day ranges.
        Uses SensorDataAggrDaily and SensorDataAggrHourly tables for ventilation module data.
        Ventilation modules are filtered by Ventilation category.

        Args:
            mine_id: The mine ID to get data for
            device_ids: Comma-separated string of device IDs
            start_date: Start date for the data (inclusive)
            end_date: End date for the data (inclusive)

        Returns:
            Tuple containing the ventilation graph data response and any error
        """
        try:
            # Parse device IDs from comma-separated string
            try:
                device_id_list = [int(device_id.strip()) for device_id in device_ids.split(',') if device_id.strip()]
            except ValueError:
                return None, APIError(400, APIErrorType.INVALID_REQUEST, "Invalid device IDs format")

            if not device_id_list:
                return None, APIError(400, APIErrorType.INVALID_REQUEST, "No valid device IDs provided")

            # Get mine timezone info
            mine_dict = get_mine_with_timezone(self, mine_id)
            if mine_dict is None:
                logger.error("Error in get_ventilation_graph_data: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")

            mine_tz = mine_dict.get("tz_code") or pytz.utc

            # Convert start and end dates to UTC datetime range
            start_of_range = datetime.combine(start_date, datetime.min.time())
            start_of_range = mine_tz.localize(start_of_range)
            end_of_range = datetime.combine(end_date, datetime.max.time())
            end_of_range = mine_tz.localize(end_of_range)
           

            # Get current datetime in mine's timezone for response
            mine_current_datetime = datetime.now(mine_tz)
            formatted_mine_current_datetime = mine_current_datetime.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + mine_current_datetime.strftime("%z")
            formatted_mine_current_datetime = formatted_mine_current_datetime[:-2] + ":" + formatted_mine_current_datetime[-2:]

            # Determine if we need hourly or daily data
            is_same_day = start_date == end_date

            # Generate x-axis data for charts
            if is_same_day:
                # Generate hour array (0-23) for same day
                x_axis_data = list(range(24))  # [0, 1, 2, ..., 23]
                x_axis_labels = [f"{(hour % 12) or 12:02d}:00 {'AM' if hour < 12 else 'PM'}" for hour in range(24)]  # ["00:00 AM", "01:00 AM", ..., "11:00 PM"]
            else:
                # Generate date array for date range
                x_axis_data = []
                x_axis_labels = []
                current_date = start_date
                while current_date <= end_date:
                    x_axis_data.append(current_date.isoformat())
                    x_axis_labels.append(current_date.strftime("%b-%d"))
                    current_date += timedelta(days=1)

            # Get device information first
            device_info_stmt = (
                select(
                    SensorDevice.id.label("device_id"),
                    SensorDevice.label.label("device_name"),
                    SensorDevice.nid.label("device_code")
                )
                .where(
                    and_(
                        SensorDevice.mine_id == mine_id,
                        SensorDevice.id.in_(device_id_list)                    
                    )
                )
                .order_by(SensorDevice.label)
            )

            device_info_result = self.session.execute(device_info_stmt).all()

            if not device_info_result:
                return None, APIError(404, APIErrorType.DEVICE_NOT_FOUND, "No valid devices found")

            # Define specific ventilation module types
            ventilation_module_types = [17, 18, 5001, 5002, 5003]

            if is_same_day:
                # Get hourly data for same day
                data_stmt = (
                    select(
                        SensorDataAggrHourly.sensor_device_id.label("device_id"),
                        SensorDataAggrHourly.module_type_id.label("module_type_id"),
                        SensorDataAggrHourly.hour.label("hour"),
                        SensorDataAggrHourly.max_value.label("max_value"),
                        SensorModuleType.display_name.label("module_type_name"),
                        SensorModuleType.unit.label("unit"),
                        SensorModuleType.value.label("module_type_value"),
                        SensorModuleType.alert_min.label("alert_min"),
                        SensorModuleType.alert_max.label("alert_max"),
                        SensorModuleType.alarm_min.label("alarm_min"),
                        SensorModuleType.alarm_max.label("alarm_max")
                    )
                    .select_from(SensorDataAggrHourly)
                    .join(
                        SensorModuleType,
                        SensorModuleType.id == SensorDataAggrHourly.module_type_id
                    )
                    .where(
                        and_(
                            SensorDataAggrHourly.sensor_device_id.in_(device_id_list),
                            SensorModuleType.value.in_(ventilation_module_types),
                            SensorDataAggrHourly.mine_date == start_date,
                            SensorDataAggrHourly.is_active == 1
                        )
                    )
                    .order_by(
                        SensorDataAggrHourly.sensor_device_id,
                        SensorDataAggrHourly.module_type_id,
                        SensorDataAggrHourly.hour
                    )
                )
            else:
                # Get daily data for multi-day range
                data_stmt = (
                    select(
                        SensorDataAggrDaily.sensor_device_id.label("device_id"),
                        SensorDataAggrDaily.module_type_id.label("module_type_id"),
                        SensorDataAggrDaily.mine_date.label("date"),
                        SensorDataAggrDaily.max_value.label("max_value"),
                        SensorModuleType.display_name.label("module_type_name"),
                        SensorModuleType.unit.label("unit"),
                        SensorModuleType.value.label("module_type_value"),
                        SensorModuleType.alert_min.label("alert_min"),
                        SensorModuleType.alert_max.label("alert_max"),
                        SensorModuleType.alarm_min.label("alarm_min"),
                        SensorModuleType.alarm_max.label("alarm_max")
                    )
                    .select_from(SensorDataAggrDaily)
                    .join(
                        SensorModuleType,
                        SensorModuleType.id == SensorDataAggrDaily.module_type_id
                    )
                    .where(
                        and_(
                            SensorDataAggrDaily.sensor_device_id.in_(device_id_list),
                            SensorModuleType.value.in_(ventilation_module_types),
                            SensorDataAggrDaily.mine_date.between(start_date, end_date),
                            SensorDataAggrDaily.is_active == 1
                        )
                    )
                    .order_by(
                        SensorDataAggrDaily.sensor_device_id,
                        SensorDataAggrDaily.module_type_id,
                        SensorDataAggrDaily.mine_date
                    )
                )

            data_result = self.session.execute(data_stmt).all()
          
            # Process the data
            devices_data = []
            unique_modules = {}  # To track unique modules across all devices

            for device_info in device_info_result:
                device_id = device_info.device_id

                # Get all data for this device
                device_data_rows = [row for row in data_result if row.device_id == device_id]

                if not device_data_rows:
                    continue

                # Group by module type
                modules_data = {}
                for row in device_data_rows:
                    # Handle special case for module types 17 and 18 (treat as Differential Pressure)
                    if row.module_type_value in [17, 18]:
                        module_type_name = "Differential Pressure"
                        module_type_value = 17  # Use 17 as the representative ID for both 17 and 18
                        module_key = f"{module_type_value}_{module_type_name}"
                    else:
                        module_type_name = row.module_type_name
                        module_type_value = row.module_type_value
                        module_key = f"{module_type_value}_{module_type_name}"

                    # Track unique modules across all devices
                    if module_type_value not in unique_modules:
                        unique_modules[module_type_value] = {
                            "moduleValue": module_type_value,
                            "moduleName": module_type_name,
                            "alertMin": row.alert_min,
                            "alertMax": row.alert_max,
                            "alarmMin": row.alarm_min,
                            "alarmMax": row.alarm_max
                        }

                    if module_key not in modules_data:
                        modules_data[module_key] = {
                            "moduleType": module_type_name,
                            "moduleTypeValue": module_type_value,
                            "unit": row.unit,
                            "data": []
                        }

                    if is_same_day:
                        # Hourly data
                        modules_data[module_key]["data"].append({
                            "data": row.hour,
                            "value": round(float(row.max_value), 2) if row.max_value is not None else 0.0
                        })
                    else:
                        # Daily data
                        modules_data[module_key]["data"].append({
                            "data": row.date.isoformat(),
                            "value": round(float(row.max_value), 2) if row.max_value is not None else 0.0
                        })

                # Convert modules_data to list format
                modules_list = []
                for module_key, module_data in modules_data.items():
                    modules_list.append({
                        "moduleType": module_data["moduleType"],
                        "moduleTypeValue": module_data["moduleTypeValue"],
                        "unit": module_data["unit"],
                        "data": module_data["data"]
                    })

                device_entry = {
                    "deviceId": device_id,
                    "deviceName": device_info.device_name,
                    "deviceCode": int_to_hex(device_info.device_code),
                    "modules": modules_list
                }

                devices_data.append(device_entry)

            # Convert unique modules to list
            unique_modules_list = list(unique_modules.values())

            # Create response
            response = {
                "lastUpdatedTs": formatted_mine_current_datetime,
                "startDate": start_date.isoformat(),
                "endDate": end_date.isoformat(),
                "dataType": "hourly" if is_same_day else "daily",
                "xAxisData": x_axis_data,
                "xAxisLabels": x_axis_labels,
                "uniqueModules": unique_modules_list,              
                "devices": devices_data
            }

            return response, None

        except Exception as e:
            logger.error(f"Error in get_ventilation_graph_data: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, str(e))
        
    def get_gas_alarm_days(self, mine_id: int, start_date: date, end_date: date) -> Tuple[Dict, APIError]:
        """Get all unique days when gas alarms occurred for a mine within a date range."""
        try:
            # Get mine timezone info
            mine_dict = get_mine_with_timezone(self, mine_id)
            if mine_dict is None:
                logger.error("Error in getGasAlarmDays: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")
            
            # Get current time in mine's timezone
            mine_tz = mine_dict.get("tz_code") or pytz.utc

            # Convert start and end dates to UTC datetime range
            start_of_range = datetime.combine(start_date, datetime.min.time())
            start_of_range = mine_tz.localize(start_of_range)
            end_of_range = datetime.combine(end_date, datetime.max.time())
            end_of_range = mine_tz.localize(end_of_range)

            # Convert to UTC for database query
            start_of_range_utc = start_of_range.astimezone(pytz.UTC)
            end_of_range_utc = end_of_range.astimezone(pytz.UTC)

            # Query to get all unique alarm dates for the date range
            alarm_dates_stmt = (
                    select(
                        cast(SensorRawData.ts_datetime, Date).label("alarm_date")
                    )
                    .select_from(SensorRawData)
                    .join(
                        SensorDevice,
                        SensorDevice.id == SensorRawData.sensor_device_id
                    )
                    .join(
                        DeviceTypes,
                        DeviceTypes.id == SensorDevice.device_type_id
                    )
                    .join(
                        SensorModuleType,
                        SensorModuleType.id == SensorRawData.module_type_id
                    )
                    .join(
                        SensorCategory,
                        SensorCategory.id == SensorModuleType.category_id
                    )
                    .join(
                        SensorModuleState,
                        SensorModuleState.id == SensorRawData.module_state_id
                    )
                    .where(
                        and_(
                            SensorDevice.mine_id == mine_id,
                            DeviceTypes.value != 72,  # Exclude ventilation sensors
                            SensorCategory.name == "Gas",  # Include only gas category modules
                            SensorRawData.is_active == 1,                                            
                            SensorModuleState.value == 3 ,  # Only alarm states which is value 3
                            SensorRawData.ts_datetime.between(start_of_range_utc, end_of_range_utc)
                        )
                    )
                    .group_by(cast(SensorRawData.ts_datetime, Date))
                    .order_by(cast(SensorRawData.ts_datetime, Date))
                )

            alarm_dates_result = self.session.execute(alarm_dates_stmt).all()     

            alarm_date = {}
            # Create list of all unique alarm dates
            alarm_dates_list = []
            for row in alarm_dates_result:
                alarm_date={"date" : row.alarm_date.isoformat()}
                alarm_dates_list.append(alarm_date) 

            response = {
                    "alarmDates": alarm_dates_list
            }  
                
            return response, None

        except Exception as e:
            logger.error(f"Error in get_gas_alaram_days: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.INTERNAL_SERVER_ERROR, str(e))