from backend.api_errors import APIErrorType, APIError

from models.production import Cut, DownEvent,ProductionGoals
from models.location import Shift,Section


from app_loggers.loggers_config import get_logger, env_settings
from services.base import (
    BaseDataManager,
)
from utils.production_utils import get_feet_mined_in_day
from datetime import datetime, time, timedelta, timezone, date

from sqlalchemy import (
    TIME,
    Date,
    asc,
    cast,
    desc,
    distinct,
    false,
    null,
    or_,
    select,
    func,
    and_,
    true,
    case,
    literal_column
)
logger = get_logger(__name__)


def get_activity_metrics(data_manager: BaseDataManager,mine_id, from_date, to_date,section_list:list, shift_list:list,total_feet_mined):
    """_summary_ : Wrapper function for getting activity metrics

    Args:
        mine_id (_int_): id of the mine
        from_date (_datetime.date_): start date
        to_date (_datetime.date_): end date
    """
    goal = calculate_goal(data_manager, from_date, to_date,section_list,shift_list)
    inspection_downtime = get_inspection_downtime(data_manager,mine_id, from_date, to_date)
    planned_downtime = get_planned_downtime(data_manager,mine_id, from_date, to_date)
    unplanned_downtime = get_unplanned_downtime(data_manager,mine_id, from_date, to_date)
    last_report = get_last_report_ts_by_date(data_manager,mine_id,to_date)

    working_hours = goal.get("working_hours")
    total_goal = goal.get("total_goal")
    average_goal =  goal.get("average_goal")
    activity_metrics = downtime_to_feet_calculations(total_goal, working_hours, inspection_downtime, planned_downtime,unplanned_downtime)
    activity_metrics["on_time"] = total_feet_mined - total_goal
    activity_metrics["average_goal"] = round(average_goal,2)
    activity_metrics["last_report"] = last_report
    activity_metrics["total_goal"] = total_goal

    return activity_metrics

def downtime_to_feet_calculations(total_goal, working_hours, inspection_downtime, planned_downtime,unplanned_downtime):

    if working_hours > 0 :
        per_hour_production_goal = total_goal/working_hours
        per_minute_production_goal = per_hour_production_goal/60
    else:
        per_minute_production_goal = 0

    inspection_downtime = per_minute_production_goal * inspection_downtime
    planned_downtime = per_minute_production_goal * planned_downtime
    unplanned_downtime = per_minute_production_goal * unplanned_downtime

    activity_metrics = {"inspection_downtime":round(inspection_downtime,2),"planned_downtime":round(planned_downtime,2),
                        "unplanned_downtime":round(unplanned_downtime,2)}
    
    logger.info(f"activity_matrics: {activity_metrics} , per_minute_production_goal: {per_minute_production_goal}, working_hours: {working_hours}")

    return activity_metrics


def get_shift_duration(data_manager: BaseDataManager,shift_id):
    query = (
            select(Shift.start_time,
                   Shift.end_time
                    )
            .where(
               Shift.id == shift_id
                )
            )
    result = data_manager.get_execute_all(query)
    for row in result:
        end_time = row.end_time
        start_time = row.start_time
        common_date = datetime.today().date()
        start_time = datetime.combine(common_date, start_time)
        end_time = datetime.combine(common_date, end_time)
        if end_time <= start_time:
                end_time += timedelta(days=1)
        duration = end_time - start_time

        shift_duration = duration.seconds/3600

    return shift_duration

def calculate_goal(data_manager: BaseDataManager,from_date, to_date,section_list:list, shift_list:list):

    query = (
            select(ProductionGoals.goal,
                    ProductionGoals.shift_id,
                    ProductionGoals.section_id,
                    ProductionGoals.effective_date,
                    ProductionGoals.end_date
                    )
            .where(
                ProductionGoals.effective_date <= to_date,
                or_(
                    ProductionGoals.end_date.is_(None),
                    ProductionGoals.end_date >= from_date
                ),
                ProductionGoals.section_id.in_(section_list),
                ProductionGoals.shift_id.in_(shift_list)
            )
    )
    # (df['effective_date'] <= to_date) & ((df['end_date'].isna()) | (df['end_date'] >= start_date))
    result = data_manager.get_execute_all(query)
    shift_duration = {}
    total_goal = 0
    working_hours = 0

    for row in result:
        effective_date = row.effective_date
        end_date = row.end_date if row.end_date is not None else to_date
        overlap_start = max(from_date, effective_date)
        overlap_end = min(to_date, end_date)
        overlap_days = (overlap_end - overlap_start).days + 1
        if row.shift_id not in shift_duration.keys():
            duration = get_shift_duration(data_manager,row.shift_id)
            shift_duration[row.shift_id] = duration
        else:
            duration = shift_duration.get(row.shift_id)
 
        working_hours += duration * overlap_days
        goal = row.goal * overlap_days
        total_goal += goal

    no_of_days = (abs((to_date - from_date).days)) + 1
    average_goal = total_goal/no_of_days

    goal_dict = {"total_goal":total_goal, "average_goal":average_goal,"working_hours":working_hours}

    logger.info(f"goal_dict : {goal_dict}")

    return goal_dict

def calculate_downtime(data_manager: BaseDataManager,mine_id, from_date, to_date,downtime_type:list[str]):
    query = (
            select(DownEvent.duration_of_delay,
                    DownEvent.dt_start_time,
                    DownEvent.dt_end_time,
                    DownEvent.down_event_type)
            .where(
                DownEvent.down_event_type.in_(downtime_type),
                cast(DownEvent.dt_end_time, Date) >= from_date,
                cast(DownEvent.dt_end_time, Date) <= to_date,
                DownEvent.mine_id == mine_id
                )
            )
    result = data_manager.get_execute_all(query)
    downtime_minutes = 0
    for i in result:
        downtime_minutes += (i.duration_of_delay).hour *60 + (i.duration_of_delay).minute
    return downtime_minutes

def calculate_shift_goal(data_manager: BaseDataManager,selected_date,section, shift):
    query = (
            select(ProductionGoals.goal,
                    )
            .where(
                # ProductionGoals.end_date == None,
                ProductionGoals.section_id == section,
                ProductionGoals.shift_id == shift,
                ProductionGoals.effective_date <= selected_date,
                ProductionGoals.end_date >= selected_date

                )
            )

    result = data_manager.get_one(query)

    if not result:
        query = (
            select(ProductionGoals.goal,
                    )
            .where(
                ProductionGoals.section_id == section,
                ProductionGoals.shift_id == shift,
                ProductionGoals.end_date == None,
                ProductionGoals.effective_date <= selected_date
                )
            )
        result = data_manager.get_one(query)
    
    return result


def get_last_report_ts_by_date(
    data_manager: BaseDataManager, mine_id: int, desired_date
):
    "function to get last_report_ts for reports APIs"
    try:
        # print(mine_id, desired_date, desired_date.strftime("%Y-%m-%d"))
        # query for the Cut table
        cut_stmt = (
            select(
                func.max(Cut.form_saved_at).label("max_saved_at"),
                func.max(Cut.form_submitted_at).label("max_submitted_at"),
            )
            .select_from(Cut)
            .filter(
                Cut.mine_id == mine_id,
                Cut.dt_end_time.cast(Date) <= desired_date,
            )
        )

        cuts_result = data_manager.get_execute_all(cut_stmt)

        if cuts_result is not None:
            for row in cuts_result:
                cuts_last_saved = row.max_saved_at
                cuts_last_submitted = row.max_submitted_at
                logger.debug(f"\n get_last_report_ts_by_date{row.max_saved_at}{row.max_submitted_at}")

                if cuts_last_saved is not None and cuts_last_submitted is not None:
                    return max(cuts_last_saved, cuts_last_submitted)
                elif cuts_last_saved is not None:
                    return cuts_last_saved

        # If no result from Cut table, query for the DownEvent table
        downevent_stmt = (
            select(
                func.max(DownEvent.form_saved_at).label("max_saved_at"),
                func.max(DownEvent.form_submitted_at).label("max_submitted_at"),
            )
            .select_from(DownEvent)
            .filter(
                DownEvent.mine_id == mine_id,
                DownEvent.dt_end_time.cast(Date) == desired_date,
            )
        )

        downevent_result = data_manager.get_execute_all(downevent_stmt)

        if downevent_result is not None:
            for row in downevent_result:
                downevent_last_saved = row.max_saved_at
                downevent_last_submitted = row.max_submitted_at

                if (
                    downevent_last_saved is not None
                    and downevent_last_submitted is not None
                ):
                    return max(downevent_last_saved, downevent_last_submitted)
                elif downevent_last_saved is not None:
                    return downevent_last_saved

        return None

    except Exception as e:
        logger.error(f"Error in get_last_report_ts: {e}", exc_info=True)
        return None

def get_inspection_downtime(data_manager: BaseDataManager,mine_id, from_date, to_date):
    inspection_downtime = calculate_downtime(data_manager,mine_id, from_date, to_date,["Inspection"])
    return inspection_downtime

def get_planned_downtime(data_manager: BaseDataManager,mine_id, from_date, to_date):
    planned_downtime = calculate_downtime(data_manager,mine_id, from_date, to_date,["Planned","Travel"])
    return planned_downtime

def get_unplanned_downtime(data_manager: BaseDataManager,mine_id, from_date, to_date):
    unplanned_downtime = calculate_downtime(data_manager,mine_id, from_date, to_date,["Unplanned",""])
    return unplanned_downtime

def calculate_total_feet_mined(data_manager: BaseDataManager,mine_id, from_date, to_date):

    total_feet_mined = 0
    while from_date <= to_date:
        coordinate =  get_feet_mined_in_day(data_manager,mine_id,from_date)
        from_date += timedelta(days=1)
        total_feet_mined += coordinate[1]
    return total_feet_mined


def get_mine_goal_from_mine(
    data_manager: BaseDataManager, mine_id: int, section_id = None
):
    "function to get mine_goal for overview API"
    query = (
        select(
            ProductionGoals.section_id,
            ProductionGoals.shift_id,
            ProductionGoals.effective_date,
            ProductionGoals.end_date,
            ProductionGoals.goal,
        )
        .join(Section, ProductionGoals.section_id == Section.id)
        .select_from(ProductionGoals)
        .where(
            Section.mine_id == mine_id,
            ProductionGoals.end_date == None,
        )
        .distinct(ProductionGoals.section_id, ProductionGoals.shift_id)
    )

    if section_id is not None:
        # print("section_id from get_cuts_by_interval", section_id, "\n")
        query = query.where(ProductionGoals.section_id == section_id)

    results = data_manager.get_execute_all(query)

    goal = 0
    for result in results:
        goal += result.goal
    return goal