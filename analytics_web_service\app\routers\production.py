from datetime import date
from datetime import date
from typing import Optional
from datetime import date

from fastapi import APIRouter, Depends, Path
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from backend.session import create_session
from schemas.production import (
    LiveSectionsSchema,
    OverviewSchema ,ReportMineSchema,ReportMineCompareSchema,ReportShiftSchema
)
from services.production import ProductionService
from const import (
    PROD_TAGS,
)
from app_loggers.loggers_config import get_logger

from middleware import validate_api_key
from fastapi import Security
from const import API_BASE_URL, AUTH_ENABLE


logger = get_logger(__name__)

router = APIRouter(prefix=API_BASE_URL, tags=PROD_TAGS, dependencies = [Security(validate_api_key)])


@router.get("/production/live/overview/{mine_id}")
async def get_overview_data(
    mine_id: int = Path(..., title="The ID of the mine"),
    session: Session = Depends(create_session),
) -> OverviewSchema:
    """Get overview Data."""

    result, api_error = ProductionService(session).get_overview_data(mine_id)
    if api_error is not None:
        return JSONResponse(status_code=api_error.status, content=api_error.to_dict())
    return result


@router.get("/production/live/sections/{mine_id}")
async def get_live_sections_data(
    mine_id: int = Path(..., title="The ID of the mine"),
    section_id: Optional[int] = None,
    session: Session = Depends(create_session),
) -> LiveSectionsSchema:
    """Get live sections Data."""

    result, api_error = ProductionService(session).get_live_sections_data(mine_id, section_id)
    if api_error is not None:
        return JSONResponse(status_code=api_error.status, content=api_error.to_dict())
    return result



@router.get("/production/reports/mine/{mine_id}/{from_date}/{to_date}")
async def get_report_mine_data(
    mine_id: int = Path(..., title="The ID of the mine"),
    from_date: date = Path(..., title="From Date", description="Start date for the report (YYYY-MM-DD)"),
    to_date: date = Path(..., title="To Date", description="End date for the report (YYYY-MM-DD)"),
    session: Session = Depends(create_session),
) -> ReportMineSchema :
    """Get mine report data for given time interval."""

    result, api_error = ProductionService(session).get_report_mine_data(mine_id,from_date, to_date)
    if api_error is not None:
        return JSONResponse(status_code=api_error.status, content=api_error.to_dict())
    return result


@router.get("/production/reports/compare/{mine_id}/{from_date1}/{to_date1}/{from_date2}/{to_date2}")
async def get_compare_report_mine_data(
    mine_id: int = Path(..., title="The ID of the mine"),
    from_date1: date = Path(..., title="From Date", description="Start date for the report (YYYY-MM-DD)"),
    to_date1: date = Path(..., title="To Date", description="End date for the report (YYYY-MM-DD)"),
    from_date2: date = Path(..., title="From Date", description="Start date for the report (YYYY-MM-DD)"),
    to_date2: date = Path(..., title="To Date", description="End date for the report (YYYY-MM-DD)"),
    session: Session = Depends(create_session),
) -> ReportMineCompareSchema :
    """Get mine report data for given time interval."""

    result, api_error = ProductionService(session).get_compare_report_mine_data(mine_id,from_date1, to_date1,from_date2,to_date2)
    if api_error is not None:
        return JSONResponse(status_code=api_error.status, content=api_error.to_dict())
    return result

@router.get("/production/reports/shifts/{mine_id}/{date}")
async def get_report_shift_data(
    mine_id: int = Path(..., title="The ID of the mine"),
    date: date = Path(..., title="From Date", description="Start date for the report (YYYY-MM-DD)"),
    session: Session = Depends(create_session),
) -> ReportShiftSchema :
    """Get mine report data for given time interval."""

    result, api_error = ProductionService(session).get_report_shift_data(mine_id,date)
    if api_error is not None:
        return JSONResponse(status_code=api_error.status, content=api_error.to_dict())
    return result