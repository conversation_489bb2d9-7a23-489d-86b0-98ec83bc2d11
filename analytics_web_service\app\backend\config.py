from pydantic import BaseModel
from pydantic_settings import (
    BaseSettings,
    SettingsConfigDict,
)
# # import pyodbc
from urllib.parse import quote
from app_loggers.loggers_config import env_settings, get_logger 

logger= get_logger(__name__)

# SQLAlchemy configuration
SQLALCHEMY_DATABASE_URL = (
    f"mssql+{env_settings.SQL_ODBC}://{env_settings.SQL_SERVER_USER}:{quote(env_settings.SQL_SERVER_PWD)}@{env_settings.SQL_SERVER_ADDR}/{env_settings.SQL_SERVER_DB_NAME}{env_settings.SQL_DRIVER}"
)

class DatabaseConfig(BaseModel):
    """Backend database configuration parameters.

    Attributes:
        dsn:
            DSN for target database.
    """
    dsn: str = SQLALCHEMY_DATABASE_URL

class Config(BaseSettings):
    """API configuration parameters.

    Automatically read modifications to the configuration parameters
    from environment variables and ``.env`` file.

    Attributes:
        database:
            Database configuration settings.
            Instance of :class:`app.backend.config.DatabaseConfig`.
        token_key:
            Random secret key used to sign JWT tokens.
    """

    database: DatabaseConfig = DatabaseConfig()
    token_key: str = ""

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        env_prefix="MYAPI_",
        env_nested_delimiter="__",
        case_sensitive=False,
    )


config = Config()
