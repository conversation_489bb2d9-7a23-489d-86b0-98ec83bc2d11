from sqlalchemy.orm import (
    Mapped,
    mapped_column,
)

from sqlalchemy import Foreign<PERSON>ey
# from models.base import SQLModel
from .base import SQLModel


import datetime

# class MinerModel(SQLModel):
#     __tablename__ = "miner_status"
#     __table_args__ = {"schema": "dbo"}

#     id: Mapped[int] = mapped_column("id", primary_key=True)
#     pid: Mapped[int] = mapped_column("pid")
#     name: Mapped[str] = mapped_column("name")
#     node_id: Mapped[int] = mapped_column("node_id")
#     org_id: Mapped[int] = mapped_column("org_id")
#     ts: Mapped[datetime.datetime] = mapped_column("ts")
#     node_status: Mapped[str] = mapped_column("node_status")
#     node_comm: Mapped[str] = mapped_column("node_comm")


class Mine(SQLModel):
    __tablename__ = "mines"
    __table_args__ = {"schema": "dbo"}

    id: Mapped[int] = mapped_column("id", primary_key=True)
    name: Mapped[str] = mapped_column("name")
    location: Mapped[str] = mapped_column("location")
    code: Mapped[str] = mapped_column("code")
    is_active:Mapped[bool] = mapped_column("is_active")
    company_id: Mapped[int] = mapped_column("company_id")
    created_at:Mapped[datetime.datetime] = mapped_column("created_at")
    created_by: Mapped[int] = mapped_column("created_by")
    updated_at:Mapped[datetime.datetime] = mapped_column("updated_at")
    updated_by: Mapped[int] = mapped_column("updated_by")
    is_delete:Mapped[bool] = mapped_column("is_delete")
    timezone_id: Mapped[int] = mapped_column("timezone_id")

    def __repr__(self):
        return (
            f"<Mine(id={self.id}, name='{self.name}', location='{self.location}', "
            f"code='{self.code}', is_active={self.is_active}, company_id={self.company_id}, "
            f"created_at='{self.created_at}', created_by={self.created_by}, "
            f"updated_at='{self.updated_at}', updated_by={self.updated_by},  is_delete={self.is_delete}, timezone_id={self.timezone_id})>"
        )



class Miner(SQLModel):
    __tablename__ = "miners"
    __table_args__ = {"schema": "dbo"}

    id: Mapped[int] = mapped_column("id", primary_key=True)
    mine_id: Mapped[int] = mapped_column(ForeignKey("dbo.mines.id"))
    ext_miner_id: Mapped[int] = mapped_column("ext_miner_id")
    first_name: Mapped[str] = mapped_column("first_name")
    last_name: Mapped[str] = mapped_column("last_name")
    ext_node_id: Mapped[int] = mapped_column("ext_node_id")
    # occupation_id:Mapped[int] = mapped_column(ForeignKey("occupation.occupation_id"))
    occupation_id:Mapped[int] = mapped_column("occupation_id")
    is_active:Mapped[bool] = mapped_column("is_active")
    created_at:Mapped[datetime.datetime] = mapped_column("created_at")
    updated_at:Mapped[datetime.datetime] = mapped_column("updated_at")


class Section(SQLModel):
    __tablename__ = "sections"
    __table_args__ = {"schema": "dbo"}

    id: Mapped[int] = mapped_column("id", primary_key=True)
    mine_id: Mapped[int] = mapped_column(ForeignKey("dbo.mines.id"))
    ext_section_id: Mapped[int] = mapped_column("ext_section_id")
    ext_section_name: Mapped[str] = mapped_column("ext_section_name")
    is_active:Mapped[bool] = mapped_column("is_active")
    created_at:Mapped[datetime.datetime] = mapped_column("created_at")
    updated_at:Mapped[datetime.datetime] = mapped_column("updated_at")


class Shift(SQLModel):
    __tablename__ = "shifts"
    __table_args__ = {"schema": "dbo"}

    id: Mapped[int] = mapped_column("id", primary_key=True)
    ext_shift_id: Mapped[int] = mapped_column("ext_shift_id")
    mine_id: Mapped[int] = mapped_column(ForeignKey("dbo.mines.id"))
    shift_name: Mapped[str] = mapped_column("shift_name")
    start_time:Mapped[datetime.datetime] = mapped_column("start_time")
    end_time:Mapped[datetime.datetime] = mapped_column("end_time")
    is_active:Mapped[bool] = mapped_column("is_active")
    created_at:Mapped[datetime.datetime] = mapped_column("created_at")
    updated_at:Mapped[datetime.datetime] = mapped_column("updated_at")
    shift_type:Mapped[str] = mapped_column("shift_type")


class MinerActivity(SQLModel):
    __tablename__ = "miner_activity"
    __table_args__ = {"schema": "dbo"}

    id: Mapped[int] = mapped_column("id", primary_key=True)
    miner_id: Mapped[int] = mapped_column(ForeignKey("dbo.miners.id"))
    section_id: Mapped[int] = mapped_column(ForeignKey("dbo.sections.id"))
    shift_id: Mapped[int] = mapped_column(ForeignKey("dbo.shift.id"))
    ground_status: Mapped[str] = mapped_column("ground_status")
    comm_status: Mapped[int] = mapped_column("comm_status")
    last_heard:Mapped[datetime.datetime] = mapped_column("last_heard")
    version_stamp: Mapped[int] = mapped_column("version_stamp")
    created_at: Mapped[datetime.datetime] = mapped_column("created_at")
    updated_at:Mapped[datetime.datetime] = mapped_column("updated_at")
    is_active:Mapped[bool] = mapped_column("is_active")


class Watchlist(SQLModel):
    __tablename__ = "watchlist_items"
    __table_args__ = {"schema": "dbo"}

    id: Mapped[int] = mapped_column("id", primary_key=True)
    user_id: Mapped[int] = mapped_column("user_id")
    miner_id: Mapped[int] = mapped_column(ForeignKey("dbo.miners.id"))
    created_at:Mapped[datetime.datetime] = mapped_column("created_at")
    created_by: Mapped[int] = mapped_column("created_by")
    updated_at:Mapped[datetime.datetime] = mapped_column("updated_at")
    updated_by: Mapped[int] = mapped_column("updated_by")


class MinerStatus(SQLModel):
    __tablename__ = "miner_status"
    __table_args__ = {"schema": "dbo"}

    id: Mapped[int] = mapped_column("id", primary_key=True)
    miner_id: Mapped[int] = mapped_column(ForeignKey("dbo.miners.id"))
    section_id: Mapped[int] = mapped_column(ForeignKey("dbo.sections.id"))
    shift_id: Mapped[int] = mapped_column(ForeignKey("dbo.shift.id"))
    version_stamp: Mapped[int] = mapped_column("version_stamp")
    msg_timestamp: Mapped[int] = mapped_column("msg_timestamp")
    ext_node_id: Mapped[int] = mapped_column("ext_node_id")
    check_in: Mapped[datetime.datetime] = mapped_column("check_in")
    went_ug: Mapped[datetime.datetime] = mapped_column("went_ug")
    went_ag: Mapped[datetime.datetime] = mapped_column("went_ag")
    section_arrive: Mapped[datetime.datetime] = mapped_column("section_arrive")
    section_left: Mapped[datetime.datetime] = mapped_column("section_left")
    travel_to: Mapped[datetime.time] = mapped_column("travel_to")
    travel_from: Mapped[datetime.time] = mapped_column("travel_from")
    on_section: Mapped[datetime.time] = mapped_column("on_section")
    last_section: Mapped[int] = mapped_column(ForeignKey("dbo.sections.id"))
    last_ug: Mapped[datetime.datetime] = mapped_column("last_ug")
    g_session:Mapped[int] = mapped_column("g_session") 
    s_session:Mapped[int] = mapped_column("s_session") 
    day_went_ug:Mapped[datetime.datetime] = mapped_column("day_went_ug") 
    day_went_ag:Mapped[datetime.datetime] = mapped_column("day_went_ag") 
    day_section_arrive:Mapped[datetime.datetime] = mapped_column("day_section_arrive") 
    day_section_left:Mapped[datetime.datetime] = mapped_column("day_section_left") 
    ug_session_time:Mapped[datetime.time] = mapped_column("ug_session_time")
    ts_day:Mapped[int] = mapped_column("ts_day") 
    ts_month:Mapped[int] = mapped_column("ts_month") 
    ts_year:Mapped[int] = mapped_column("ts_year") 
    ts_week:Mapped[int] = mapped_column("ts_week") 
    cs:Mapped[int] = mapped_column("cs") 
    z:Mapped[int] = mapped_column("z") 
    created_at: Mapped[datetime.datetime] = mapped_column("created_at")
    updated_at: Mapped[datetime.datetime] = mapped_column("updated_at")
    is_active: Mapped[bool] = mapped_column("is_active")



# class Occupation(SQLModel):
#     __tablename__ = "occupation"
#     __table_args__ = {"schema": "dbo"}

#     id: Mapped[int] = mapped_column("id", primary_key=True)
#     # mine_id: Mapped[int] = mapped_column(ForeignKey("mine.mine_id"))
#     mine_id: Mapped[int] = mapped_column("mine_id")
#     occupation_id: Mapped[int] = mapped_column("occupation_id")
#     description: Mapped[str] = mapped_column("section_name")
#     is_active:Mapped[bool] = mapped_column("is_active")
#     created_at:Mapped[datetime.datetime] = mapped_column("created_at")
#     updated_at:Mapped[datetime.datetime] = mapped_column("updated_at")


class Timezone(SQLModel):
    __tablename__ = "timezones"
    __table_args__ = {"schema": "dbo"}

    id: Mapped[int] = mapped_column("id", primary_key=True)
    name: Mapped[str] = mapped_column("name")
    code: Mapped[str] = mapped_column("code")
    is_active: Mapped[bool] = mapped_column("is_active")
    abbreviation: Mapped[str] = mapped_column("abbreviation")
    windows_tz: Mapped[str] = mapped_column("windows_tz")

    def __repr__(self):
        return (
            f"<Timezone(id={self.id}, name='{self.name}', code='{self.code}', "
            f"is_active={self.is_active}, abbreviation='{self.abbreviation}', "
            f"windows_tz='{self.windows_tz}')>"
        )
