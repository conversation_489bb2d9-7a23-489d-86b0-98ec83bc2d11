from typing import Optional
from app_loggers.loggers_config import get_logger
from models.location import Mine, Timezone
from sqlalchemy import select, func
from services.base import BaseDataManager
import pytz

logger = get_logger(__name__)


def get_mine(data_manager: BaseDataManager, mine_id: int) -> Optional[dict]:
    """Get mine by mine_id"""
    try:
        if not isinstance(mine_id, int) or mine_id <= 0:
            logger.error("From get_mine - Invalid mine_id: %s", mine_id)
            return None

        stmt = select(Mine).where(Mine.id == mine_id, Mine.is_active == 1)

        mine = data_manager.get_one(stmt)
        logger.debug("Mine : %s", mine)

        if mine is None:
            return None

        # Converting the mine object to a dictionary
        # mine_dict = table_record_to_dict(mine)
        mine_dict = mine.to_dict()

        return mine_dict

    except Exception as e:
        logger.error(f"Error in get_mine: {e}", exc_info=True)
        return None


def get_mine_with_timezone(data_manager: BaseDataManager, mine_id: int) -> Optional[dict]:
    """Get mine by mine_id"""
    try:
        if not isinstance(mine_id, int) or mine_id <= 0:
            logger.error("From get_mine_with_timezone - Invalid mine_id: %s", mine_id)
            return None

        stmt = (
            select(
                Mine.id.label("mine_id"),
                Mine.name.label("mine_name"),
                Mine.location.label("mine_location"),
                Mine.code.label("mine_code"),
                Mine.is_active.label("mine_is_active"),
                Mine.company_id.label("mine_company_id"),
                Mine.created_at.label("mine_created_at"),
                Mine.created_by.label("mine_created_by"),
                Mine.updated_at.label("mine_updated_at"),
                Mine.updated_by.label("mine_updated_by"),
                Mine.is_delete.label("mine_is_delete"),
                Timezone.id.label("tz_id"),
                Timezone.name.label("tz_name"),
                Timezone.code.label("tz_code_str"),
                Timezone.abbreviation.label("tz_abbreviation"),
                Timezone.windows_tz.label("windows_tz")
            )
            .outerjoin(Timezone, Timezone.id == Mine.timezone_id)
            .where(
                Mine.id == mine_id,
                Mine.is_active == 1,
            )
        )

        mine_record = data_manager.get_execute_one(stmt)

        if mine_record is None:
            logger.error("From get_mine_with_timezone : Mine not found")
            return None

        if "tz_code_str" in mine_record:
            tz_code = mine_record.get("tz_code_str")

            mine_record["tz_code"] = (
                get_timezone_object(tz_code) if tz_code else pytz.UTC
            )
            if tz_code is None:
                mine_record["tz_code_str"] = "UTC" 
            
            return mine_record

    except Exception as e:
        logger.error(f"Exception in get_mine_with_timezone: {e}", exc_info=True)
        return None


def get_timezone_object(tz_code: str) -> pytz.BaseTzInfo:
    """Convert a timezone code to a pytz timezone object, defaulting to UTC if invalid."""
    try:
        return pytz.timezone(tz_code)
    except pytz.UnknownTimeZoneError:
        logger.info(f"Unknown timezone code: {tz_code}. Defaulting to UTC.")
        return pytz.UTC
