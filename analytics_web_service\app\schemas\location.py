from pydantic import BaseModel,validator
from datetime import datetime
from typing import Optional,List,Dict

# class MinerSchema(BaseModel):
#     id: int
#     pid:int
#     name:str
#     node_id:int
#     org_id:int
#     ts:Optional[datetime] = None
#     node_status:str| None = None
#     node_comm:str| None = None

#     @validator('ts', pre=True, always=True)
#     def parse_timestamp(cls, v):
#         if v is None or v == "":
#             return None
#         return datetime.fromisoformat(v)


class MinerSchema(BaseModel):
    id: int
    mine_id:int
    ext_miner_id:int
    first_name:str
    last_name:str
    ext_node_id:int
    occupation_id:int
    is_active:bool
    created_at:Optional[datetime] = None
    updated_at:Optional[datetime] = None

   
class SectionSchema(BaseModel):
    id: int
    mine_id:int
    ext_section_id:int
    ext_section_name:str
    is_active:bool
    created_at:Optional[datetime] = None
    updated_at:Optional[datetime] = None

    
class MinerActivitySchema(BaseModel):
    id: int
    miner_id:int
    section_id:int
    shift_id:int
    ground_status:str
    comm_status:int
    last_heard:Optional[datetime] = None
    version_stamp:Optional[datetime] = None
    created_at:Optional[datetime] = None
    updated_at:Optional[datetime] = None
    is_active:bool


class UGBreakupSchema(BaseModel):
    sectionName: str
    sectionBreakup: Dict[str, int]


class DashboardSchema(BaseModel):
    lastUpdatedTs: Optional[str]
    totalMiners: Optional[int]
    totalAG: Optional[int]
    totalUG: Optional[int]
    totalOffline: Optional[int]
    # ugBreakup: List[UGBreakupSchema]


class MinerStatusSchema(BaseModel):
    minerId: int
    minerName: str
    arriveTime: Optional[str]
    leftTime: Optional[str]
    travelTo: Optional[str]
    travelFrom: Optional[str]
    onSection: Optional[str]
    arriveTimeCC: str
    leftTimeCC: str
    travelToCC: str
    travelFromCC: str
    onSectionCC: str
    isWatched: bool
    watchlistId: Optional[int]

class SectionBreakupSchema(BaseModel):
    sectionName: str
    minersCount: int
    firstArrival: str
    miners: List[MinerStatusSchema]

class SectionsResponseSchema(BaseModel):
    lastUpdatedTs: str
    sectionBreakup: List[SectionBreakupSchema]

class Watchlist(BaseModel):
    minerId: int
    minerName: str
    ug: Optional[str]
    ugHrs: Optional[str]
    onSection: Optional[str]
    section: Optional[str]
    ugCC: Optional[str]
    ugHrsCC: Optional[str]
    onSectionCC: Optional[str]
    isWatched: bool
    watchlistId: Optional[int]


class WatchlistResponseSchema(BaseModel):
    lastUpdatedTs: str
    watchlist: List[Watchlist]
    

# Schemas related to live/checkins API
class UGBreakupForLiveCheckinsSchema(BaseModel):
    minerId: int
    minerName: str
    wentUG: Optional[str]
    currentSection: Optional[str] = None


class AGBreakupForLiveCheckinsSchema(BaseModel):
    minerId: int
    minerName: str
    checkedInTime: Optional[str]


class LiveCheckinsSchema(BaseModel):
    lastUpdatedTs: str
    totalAG: Optional[int]
    totalUG: Optional[int]
    totalOffline: Optional[int]
    agBreakup: Optional[List[AGBreakupForLiveCheckinsSchema]]
    ugBreakup: Optional[List[UGBreakupForLiveCheckinsSchema]]

# Schemas related to report/checkins API
class ReportCheckinsMinersDataSchema(BaseModel):
    minerId : int 
    minerName : str
    checkedInTime : str 
    firstUG : Optional[str] = None
    lastUG : Optional[str] = None
    lastSection : Optional[str] = None
    
class ReportCheckinsSchema(BaseModel):
    minersData : List[ReportCheckinsMinersDataSchema]

class ReportSectionBreakupSchema(BaseModel):
    sectionName: str
    totalNodes: int
    totalNodesBreakup: List[MinerStatusSchema]

class ReportSectionsResponseSchema(BaseModel):
    sectionBreakup: List[ReportSectionBreakupSchema]
    
# Schemas related to report/checkins API  
class ReportDataSchema(BaseModel):
    date: str
    dayOfWeek: str
    enterUG: Optional[str]
    leaveUG: Optional[str]
    totalTimeUG: Optional[str]
    enterUGCC: Optional[str]
    leaveUGCC: Optional[str]
    totalTimeUGCC: Optional[str]

class ShiftsSchema(BaseModel):
    shiftId : int
    startTime : Optional[str]
    endTime : Optional[str]
    
class ReportPersonnelSchema(BaseModel):
    minerId: Optional[int]
    minerName: Optional[str]
    avgEnterUG: Optional[str]
    avgLeaveUG: Optional[str]
    avgTimeUG: Optional[str]
    reportData: List[ReportDataSchema]
    shifts : Optional[List]

from datetime import time

class ShiftSchema(BaseModel):
    id: int
    ext_shift_id:int
    mine_id:int
    shift_name:str
    start_time:time
    end_time:time
    is_active:bool
    created_at:Optional[datetime] = None
    updated_at:Optional[datetime] = None
