import json
from operator import itemgetter
import time as tm
from typing import Optional, <PERSON><PERSON>

from sqlalchemy import case, literal_column, desc, select, func, and_, TIME, or_, cast, Date, text
from sqlalchemy.dialects import mssql
from datetime import date, datetime, time, timedelta, timezone, date, date

from services.timezone import convert_dt_to_tz, get_epoch_range_using_tz, get_min_epoch_using_tz
from services.mine import get_mine_with_timezone
from backend.api_errors import APIErrorType, APIError
from models.location import Miner, MinerActivity, Section, MinerStatus, Watchlist

from schemas.location import MinerSchema, DashboardSchema, ReportSectionsResponseSchema, ReportPersonnelSchema,SectionsResponseSchema,WatchlistResponseSchema, LiveCheckinsSchema, ReportCheckinsSchema
# , ReportPersonnelSchema
from services.base import (
    BaseDataManager,
    BaseService,
)

from app_loggers.loggers_config import get_logger 
from utils.locations_utils import generate_latest_timestamp_subquery_v2, generate_latest_lastheard_subquery_v2, apply_epoch_range_conditions, check_miner_exists, generate_latest_lastheard_subquery, generate_latest_timestamp_subquery, get_average_time, get_current_shift, get_epoch_range, get_largest_versionstamp_from_miner_activity, get_largest_versionstamp_from_miner_status, get_miner_stats, get_shift_times, get_shifts_by_ids,  get_utc_isoformat, get_miners_by_ground_status, get_total_miners, get_miners_by_comm_status, check_if_miner_watched, get_watchlist_id, timedelta_to_hh_mm, update_CC, check_mine_exists, latest_timestamp_subquery, minutes_to_datetime, get_empty_schema, get_user_watchlist
import pytz

logger= get_logger(__name__)

class LocationService(BaseService):
    def get_miner_by_id(self, Miner_id: int) -> Tuple[MinerSchema, APIError]:
        """Get Miner by ID."""
        
        return LocationDataManager(self.session).get_miner_by_id(Miner_id)

    def get_dashboard_data(self, mine_id:int) -> Tuple[DashboardSchema, APIError]:
        """Get Dashboard Data."""

        return LocationDataManager(self.session).get_dashboard_data(mine_id)
    
    def get_sections_data(self, mine_id: int, user_id: int, section_id: Optional[int] = None, sort_by: str = "first_arrival") -> Tuple[SectionsResponseSchema, APIError]:
        """Get Miners Data Sectionswise ."""

        return LocationDataManager(self.session).get_sections_data(mine_id, user_id,section_id,sort_by)
    
    def get_watchlist_data(self, mine_id: int, user_id: int) -> Tuple[WatchlistResponseSchema, APIError] :
        """Get Watchlist Data."""

        return LocationDataManager(self.session).get_watchlist_data(mine_id, user_id)
    
    def get_live_checkins_data(self, mine_id:int) -> Tuple[LiveCheckinsSchema, APIError]:
        """Get Live Checkins Data."""
        
        return LocationDataManager(self.session).get_live_checkins_data(mine_id)
    
    def get_sections_report(self, mine_id: int, user_id: int, date: date, section_id: Optional[int] = None) -> Tuple[Optional[ReportSectionsResponseSchema], Optional[APIError]]:
        """Get Sections Report."""

        return LocationDataManager(self.session).get_sections_report_v2(mine_id, user_id, date, section_id)
    
    def get_personnel_report_data(self, mine_id:int, miner_id:int, from_date:date, to_date:date) -> Tuple[Optional[ReportPersonnelSchema], Optional[APIError]]:
        """Get Report Personnel Data."""
        
        return LocationDataManager(self.session).get_personnel_report_data_v2(mine_id, miner_id, from_date, to_date)
    
    def get_report_checkins_data(self, mine_id:int, date:date) -> Tuple[ReportCheckinsSchema, APIError]:
        """Get Report Checkins Data."""
        
        return LocationDataManager(self.session).get_report_checkins_data(mine_id, date)

class LocationDataManager(BaseDataManager):
    def get_miner_by_id(self, Miner_id: int) -> Tuple[MinerSchema, APIError]:
        try:
            stmt = select(Miner).where(Miner.id == Miner_id)
            model = self.get_one(stmt)
            if model is None:
                logger.error(f"Error in get_miner_by_id: Miner not found")
                return None, APIError(404, APIErrorType.MINER_NOT_FOUND, "Miner not found")
            return MinerSchema(**model.to_dict()), None
        except Exception as e:
            logger.error(f"Error in get_miner_by_id: {e}", exc_info=True)


    # Locations/Live/Dashboard
    def get_dashboard_data(self, mine_id: int) -> Tuple[DashboardSchema, APIError]:
        """Get dashboard data for the specified mine ID."""

        location_data_manager_obj = LocationDataManager(self.session)

        mine_dict = get_mine_with_timezone(location_data_manager_obj, mine_id)
        # Mine check
        if mine_dict is None:
            logger.error("Error in get_dashboard_data: Mine not found.")
            return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")

        # Mine's Timezone related code
        mine_timezone = mine_dict.get("tz_code") or pytz.utc
        mine_current_datetime = datetime.now(mine_timezone)
        mine_current_date = mine_current_datetime.date()
        formatted_mine_current_datetime = mine_current_datetime.isoformat(timespec='milliseconds')
        logger.debug(f"Mine's Current_datetime  : {mine_current_datetime}, tz : {mine_timezone}")

        epoch_range = get_epoch_range_using_tz(mine_current_date, mine_timezone)
      
        # Main response fetching
        miner_stats = get_miner_stats(data_manager=location_data_manager_obj, mine_id=mine_id, epoch_range=epoch_range)
        if miner_stats is None:
            logger.error("Failed to retrieve miner stats, miner_stats is None")
            total_ag_miners_count = total_ug_miners_count = total_offline_miners_count = 0
        else:
            total_miners_count = miner_stats.get("total_miners", 0)
            total_ag_miners_count = miner_stats.get("total_ag_miners", 0)
            total_ug_miners_count = miner_stats.get("total_ug_miners", 0)
            total_offline_miners_count = miner_stats.get("total_offline_miners", 0)
            logger.debug(f"miner_stats , value: {miner_stats}")

       

        # Response building
        dashboard_response = DashboardSchema(
        lastUpdatedTs = formatted_mine_current_datetime,
        totalMiners = total_miners_count,
        totalAG = total_ag_miners_count,
        totalUG = total_ug_miners_count,
        totalOffline = total_offline_miners_count,
        )
        return dashboard_response, None

    def get_live_checkins_data(self, mine_id:int) -> Tuple[LiveCheckinsSchema, APIError]:
        """Get Live checkins data for the specified mine ID."""

        location_data_manager_obj = LocationDataManager(self.session)

        mine_dict = get_mine_with_timezone(location_data_manager_obj, mine_id)
        # Mine check
        if mine_dict is None :
            logger.error("Error in get_checkins_data: Mine not found.")
            return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")


        # Mine's Timezone related code
        mine_timezone = mine_dict.get("tz_code") or pytz.utc
        mine_current_datetime = datetime.now(mine_timezone)
        mine_current_date = mine_current_datetime.date()
        formatted_mine_current_datetime= mine_current_datetime.isoformat(timespec='milliseconds')
        logger.debug(f"Mine's Current_datetime  : {mine_current_datetime}, tz : {mine_timezone}")

        epoch_range = get_epoch_range_using_tz(mine_current_date, mine_timezone)

        # Main response fetching
        miner_stats = get_miner_stats(data_manager=location_data_manager_obj, mine_id=mine_id,  epoch_range=epoch_range)
        if miner_stats is None:
            logger.error("Failed to retrieve miner stats, miner_stats is None")
            total_ag_miners_count = total_ug_miners_count = total_offline_miners_count = 0
        else:
            total_ag_miners_count = miner_stats.get("total_ag_miners", 0)
            total_ug_miners_count = miner_stats.get("total_ug_miners", 0)
            total_offline_miners_count = miner_stats.get("total_offline_miners", 0)
        logger.debug(f"miner_stats , value: {miner_stats}")

        ag_breakup , ug_breakup = self.get_ag_ug_breakup_for_checkins(mine_id=mine_id, epoch_range=epoch_range, mine_dict=mine_dict)
        # logger.debug("ag_breakup : %s", len(ag_breakup) )
        # logger.debug("ug_breakup : %s", len(ug_breakup) )

        # Response building
        checkins_response = LiveCheckinsSchema(
            lastUpdatedTs = formatted_mine_current_datetime,
            totalAG = total_ag_miners_count,
            totalUG = total_ug_miners_count,
            totalOffline = total_offline_miners_count,
            agBreakup = ag_breakup,
            ugBreakup = ug_breakup
        )
        return checkins_response, None

    # Helper methods for dashboard API
    def get_ug_breakup_sectionwise(self, mine_id: int, shift_id:int, epoch_range: tuple):
        try:
            start_of_day, end_of_day = epoch_range
            latest_lastheard_subquery = generate_latest_lastheard_subquery_v2(shift_id, 1, start_of_day, end_of_day)
       
            stmt = (
                select(
                    Section.ext_section_id.label("section_external_id"),
                    Section.ext_section_name.label("section_name"),
                    func.count(func.distinct(MinerActivity.miner_id)).label(
                        "total_nodes"
                    ),
                )
                .join(MinerActivity, Section.id == MinerActivity.section_id)
                .outerjoin(Miner, MinerActivity.miner_id == Miner.id)
                .join(
                    latest_lastheard_subquery,
                    and_(
                        latest_lastheard_subquery.c.miner_id == MinerActivity.miner_id,
                        latest_lastheard_subquery.c.latest_last_heard
                        == MinerActivity.last_heard,
                    ),
                )
                .filter(
                    Miner.mine_id == mine_id,
                    MinerActivity.is_active == 1,
                    MinerActivity.ground_status == "UG",
                    MinerActivity.shift_id == shift_id,
                    apply_epoch_range_conditions(MinerActivity.last_heard, start_of_day, end_of_day)
                )
                .group_by(Section.ext_section_id, Section.ext_section_name)
            )

            result = self.get_execute_all(stmt)
            ug_breakup = []

            for section_external_id, section_name, total_nodes in result:
                ug_breakup.append(
                    {
                        "sectionName": section_name,
                        "sectionBreakup": {
                            "totalNodes": total_nodes,
                        }
                    }
                )

            return ug_breakup

        except Exception as e:
            logger.error(f"Error in get_ug_breakup_sectionwise: {e}", exc_info=True)
            # raise e

    # Helper methods for live/checkins
    def get_ag_breakup_for_checkins(self, mine_id: int, shift_id:int, epoch_range: tuple, mine_dict):
        try:
            start_of_day, end_of_day = epoch_range
            tz = mine_dict.get("tz_code") or pytz.utc
            latest_timestamp_subquery = generate_latest_timestamp_subquery_v2( 1, start_of_day, end_of_day)
            latest_lastheard_subquery = generate_latest_lastheard_subquery_v2( 1, start_of_day, end_of_day)

            stmt = (
                select(
                    Miner.id, 
                    Miner.first_name, 
                    Miner.last_name, 
                    MinerStatus.check_in
                )
                .join(Miner, MinerStatus.miner_id == Miner.id)
                .join(MinerActivity, MinerStatus.miner_id ==MinerActivity.miner_id)
                .join(
                    latest_timestamp_subquery,
                    and_(
                        latest_timestamp_subquery.c.miner_id == MinerStatus.miner_id,
                        latest_timestamp_subquery.c.latest_timestamp == MinerStatus.msg_timestamp
                    )
                )
                .join(
                    latest_lastheard_subquery,
                    and_(
                        latest_lastheard_subquery.c.miner_id == MinerActivity.miner_id,
                        latest_lastheard_subquery.c.latest_last_heard
                        == MinerActivity.last_heard,
                    ),
                )
                .select_from(MinerStatus)
                .filter(
                    Miner.mine_id == mine_id,
                    MinerStatus.is_active == 1,
                    MinerActivity.is_active == 1,
                    MinerActivity.ground_status == "AG",
                    MinerActivity.shift_id == shift_id,
                    MinerStatus.shift_id == shift_id,
                    apply_epoch_range_conditions(MinerActivity.last_heard, start_of_day, end_of_day),
                    apply_epoch_range_conditions(MinerStatus.msg_timestamp, start_of_day, end_of_day)
                )
                .distinct()
            )

            result = self.get_execute_all(stmt)

            ag_breakup = []
            for id, first_name, last_name, check_in in result:
                ag_breakup.append({
                    "minerId": f"{id}",
                    "minerName": f"{first_name} {last_name}",
                    "checkedInTime": convert_dt_to_tz(check_in, tz).strftime("%I:%M %p").lower() if check_in else None,
                })
            return ag_breakup
        except Exception as e:
            logger.error(f"Error in get_ag_breakup_for_checkins: {e}", exc_info=True)

    def get_ug_breakup_for_checkins(self, mine_id: int, shift_id:int, epoch_range: tuple, mine_dict):
        try:
            start_of_day, end_of_day = epoch_range
            latest_lastheard_subquery = generate_latest_lastheard_subquery()
            tz = mine_dict.get("tz_code") or pytz.utc
            latest_timestamp_subquery = generate_latest_timestamp_subquery_v2(1, start_of_day, end_of_day)
            latest_lastheard_subquery = generate_latest_lastheard_subquery_v2( 1, start_of_day, end_of_day)
            stmt = (
                select(
                    Miner.id,
                    Miner.first_name,
                    Miner.last_name,
                    MinerStatus.went_ug,
                    # Section.ext_section_name
                    case((Section.ext_section_name.isnot(None), Section.ext_section_name), else_=None).label("ext_section_name")
                )
                .join(Miner, Miner.id == MinerStatus.miner_id)
                # .join(Section, Section.id == MinerStatus.section_id)
                .join(MinerActivity,  MinerStatus.miner_id == MinerActivity.miner_id)
                .join(
                    latest_timestamp_subquery,
                    and_(
                        latest_timestamp_subquery.c.miner_id == MinerStatus.miner_id,
                        latest_timestamp_subquery.c.latest_timestamp == MinerStatus.msg_timestamp
                    )
                )
                .join(
                    latest_lastheard_subquery,
                    and_(
                        latest_lastheard_subquery.c.miner_id == MinerActivity.miner_id,
                        latest_lastheard_subquery.c.latest_last_heard
                        == MinerActivity.last_heard,
                    ),
                )
                .outerjoin(Section, Section.id == MinerStatus.section_id)
                .select_from(MinerStatus)
                .filter(
                    Miner.mine_id == mine_id,
                    MinerStatus.is_active == 1,
                    MinerActivity.is_active == 1,
                    MinerActivity.ground_status == "UG",
                    MinerActivity.shift_id == shift_id,
                    MinerStatus.shift_id == shift_id,
                    apply_epoch_range_conditions(MinerActivity.last_heard, start_of_day, end_of_day),
                    apply_epoch_range_conditions(MinerStatus.msg_timestamp, start_of_day, end_of_day)
                )
                .distinct()
            )

            result = self.get_execute_all(stmt)

            ug_breakup = []
            for id, first_name, last_name, went_ug, ext_section_name in result:
                ug_breakup.append({
                    "minerId": f"{id}",
                    "minerName": f"{first_name} {last_name}",
                    "wentUG": convert_dt_to_tz(went_ug, tz=tz).strftime("%I:%M %p").lower() if went_ug else None,
                    "currentSection": ext_section_name
                })
            return ug_breakup
        except Exception as e:
            logger.error(f"Error in get_ug_breakup_for_checkins: {e}", exc_info=True)


    def get_ag_ug_breakup_for_checkins(
        self, mine_id: int, epoch_range: tuple, mine_dict
    ):
        try:
            start_of_day, end_of_day = epoch_range
            tz = mine_dict.get("tz_code") or pytz.utc
            latest_timestamp_subquery = generate_latest_timestamp_subquery_v2( 1, start_of_day, end_of_day)
            latest_lastheard_subquery = generate_latest_lastheard_subquery_v2( 1, start_of_day, end_of_day)
            stmt = (
                select(
                    Miner.id,
                    MinerStatus.id,
                    Miner.first_name,
                    Miner.last_name,
                    MinerStatus.check_in,
                    MinerStatus.went_ug,
                    case((Section.ext_section_name.isnot(None),Section.ext_section_name,),else_=None,).label("ext_section_name"),
                    MinerActivity.ground_status,
                )
                .join(Miner, MinerStatus.miner_id == Miner.id)
                .join(MinerActivity, MinerStatus.miner_id == MinerActivity.miner_id)
                .outerjoin(Section, Section.id == MinerStatus.section_id)
                .join(
                    latest_timestamp_subquery,
                    and_(
                        latest_timestamp_subquery.c.miner_id == MinerStatus.miner_id,
                        latest_timestamp_subquery.c.latest_timestamp == MinerStatus.msg_timestamp,
                    ),
                )
                .join(
                    latest_lastheard_subquery,
                    and_(
                        latest_lastheard_subquery.c.miner_id == MinerActivity.miner_id,
                        latest_lastheard_subquery.c.latest_last_heard == MinerActivity.last_heard,
                    ),
                )
                .select_from(MinerStatus)
                .filter(
                    Miner.mine_id == mine_id,
                    MinerActivity.is_active == 1,                    
                    apply_epoch_range_conditions(MinerActivity.last_heard, start_of_day, end_of_day),
                    MinerStatus.is_active == 1,                    
                    apply_epoch_range_conditions(MinerStatus.msg_timestamp, start_of_day, end_of_day),
                )
                .distinct()
            ).order_by(desc(MinerStatus.id))
            result = self.get_execute_all(stmt)
            # logger.info("sql %s", stmt.compile(dialect=mssql.dialect(),
            #                                 compile_kwargs={"literal_binds": True}))
            ag_breakup = []
            ug_breakup = []
            seen_ids = set()

            if result:
                for miner_id, _,first_name, last_name, check_in, went_ug, ext_section_name, ground_status in result:
                    if miner_id not in seen_ids:
                        if ground_status == "AG":
                            checked_in_time = None
                            if check_in:
                                check_in_mine_tz = convert_dt_to_tz(check_in, tz)
                                if check_in_mine_tz.date() == start_of_day.date():
                                    checked_in_time = check_in_mine_tz.strftime("%I:%M %p").lower()
                            ag_breakup.append(
                                {
                                    "minerId": f"{miner_id}",
                                    "minerName": f"{first_name} {last_name}",
                                    "checkedInTime": (
                                        checked_in_time
                                        if check_in
                                        else None
                                    ),
                                }
                            )
                        elif ground_status == "UG":
                            ug_breakup.append(
                                {
                                    "minerId": f"{miner_id}",
                                    "minerName": f"{first_name} {last_name}",
                                    "wentUG": (
                                        convert_dt_to_tz(went_ug, tz=tz).strftime("%I:%M %p").lower()
                                        if went_ug
                                        else None
                                    ),
                                    "currentSection": ext_section_name,
                                }
                        )
                        seen_ids.add(miner_id)

            return ag_breakup, ug_breakup
        except Exception as e:
            logger.error(f"Error in get_ag_ug_breakup_for_checkins: {e}", exc_info=True)
            return [],[]


    def get_sections_data(self, mine_id: int, user_id: int, section_id: int=None, sort_by:str="first_arrival") -> Tuple[SectionsResponseSchema, APIError]:
        try:
            location_data_manager_obj = LocationDataManager(self.session)

            mine_dict = get_mine_with_timezone(location_data_manager_obj, mine_id)
            # Mine check
            if mine_dict is None :
                logger.error("Error in get_sections_data: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")

            # Mine's Timezone related code
            mine_timezone = mine_dict.get("tz_code") or pytz.utc
            mine_current_datetime = datetime.now(mine_timezone)
            mine_current_date = mine_current_datetime.date()
            formatted_mine_current_datetime= mine_current_datetime.isoformat(timespec='milliseconds')
            logger.debug(f"Mine's Current_datetime  : {mine_current_datetime}, tz : {mine_timezone}")

            epoch_range = get_epoch_range_using_tz(mine_current_date, mine_timezone)

            current_shift = get_current_shift(location_data_manager_obj, mine_id, mine_current_datetime)
            # Shift check using mine's timezone
            if current_shift is None:
                logger.error("Current_shift is None")
                return get_empty_schema("live/sections", formatted_mine_current_datetime)

            current_shift_id = current_shift.get("id")
            logger.info(f"Current shift id: {current_shift['id']} : start_time: {current_shift['start_time']}  & end_time: {current_shift['end_time']} .")

            # Main response fetching
            sections_breakup = self.get_miner_data_sectionwise_v3(mine_id, mine_dict, user_id, current_shift_id, epoch_range=epoch_range, section_id=section_id, sort_by=sort_by) or  []
            # Response building
            sections_response = SectionsResponseSchema(
                lastUpdatedTs=formatted_mine_current_datetime,
                sectionBreakup=sections_breakup
            )
            return sections_response, None

        except Exception as e:
            logger.error(f"Error in get_sections_data: {e}", exc_info=True)


    def get_miner_data_sectionwise_v3(self, mine_id: int,  mine_dict:dict, user_id: int, shift_id, epoch_range: tuple, section_id: int=None, sort_by: str="first_arrival") -> list:
        try:
            start_func = tm.time()
            location_data_manager_obj = LocationDataManager(self.session)
            start_of_day, end_of_day = epoch_range
            tz = mine_dict.get("tz_code") or pytz.utc
            latest_timestamp_subquery = generate_latest_timestamp_subquery_v2( 1, start_of_day, end_of_day)

            stmt = (
                    select(
                        MinerStatus.id,
                        Section.ext_section_name.label("sectionName"),
                        Miner.id.label("minerId"),
                        func.concat(Miner.first_name, " ", Miner.last_name).label("minerName"),
                        MinerStatus.section_arrive.label("arriveTime"),
                        MinerStatus.section_left.label("leftTime"),
                        MinerStatus.travel_to.label("travelTo"),
                        MinerStatus.travel_from.label("travelFrom"),
                        MinerStatus.on_section.label("onSection"),
                    )
                    .select_from(MinerStatus)
                    .join(Miner, MinerStatus.miner_id == Miner.id)
                    .join(
                        Section, MinerStatus.last_section == Section.id
                        # Don't uncomment this block - excessive logical reads
                        # Section,
                        # or_(
                        #     MinerStatus.section_id == Section.id,
                        #     MinerStatus.last_section == Section.id,
                        # ),
                    )
                    .join(
                        latest_timestamp_subquery,
                        and_(
                            latest_timestamp_subquery.c.miner_id == MinerStatus.miner_id,
                            latest_timestamp_subquery.c.latest_timestamp == MinerStatus.msg_timestamp
                        )
                    )
                    .filter(Miner.mine_id == mine_id)
                    .filter(MinerStatus.is_active == 1)
                    .filter(MinerStatus.shift_id == shift_id)
                    .filter(apply_epoch_range_conditions(MinerStatus.msg_timestamp, start_of_day, end_of_day))
                )

            if section_id is not None:
                stmt = stmt.filter(
                    MinerStatus.last_section == section_id,
                    # Don't uncomment this block - excessive logical reads
                    # or_(
                    #     MinerStatus.last_section == section_id,
                    #     MinerStatus.section_id == section_id
                    # )
                )

            # # Uncomment this block if you don't want to do this using python
            # # Adding conditional sorting logic directly in the query
            # if sort_by == "first_arrival":
            #     stmt = stmt.order_by(
            #         MinerStatus.section_arrive.label("arriveTime").asc(),
            #         "minerName",
            #         MinerStatus.id.desc()
            #     )
            # else:
            #     stmt = stmt.order_by(
            #         # MinerStatus.section_left.label("leftTime").asc(),
            #         case(
            #             (MinerStatus.section_left.is_(None), 1),  # Assign 1 if `section_left` is NULL
            #             (MinerStatus.section_left.isnot(None), 0)  # Assign 0 if `section_left` is not NULL
            #         ).asc(),
            #         "minerName",
            #         MinerStatus.id.desc()
            #     )

            start_query = tm.time()
            results = self.fetch_all(stmt)
            logger.debug("query time %s", tm.time()-start_query)
            # logger.info("sql %s", stmt.compile(dialect=mssql.dialect(),
            #                                    compile_kwargs={"literal_binds": True}))
            
            # Sorting the results pythonically, comment this block if you want to do this using query
            if sort_by == "first_arrival":
                results.sort(key=lambda x: (x.arriveTime, x.minerName, -x.id))  # Sort by arriveTime, then minerName, then id descending
            else:
                results.sort(key=lambda x: (x.leftTime is None, x.leftTime, x.minerName, -x.id))  # Sort by leftTime, then minerName, then id descending


            section_breakup = {}
            watched_miners = {}
            start_query = tm.time()
            user_watchlist = get_user_watchlist(location_data_manager_obj, user_id)
            if user_watchlist is not None:
                for watchlist_id, _, m_id in user_watchlist:
                    watched_miners[m_id] = watchlist_id
            logger.debug("get_user_watchlist time %s", tm.time()-start_query)
            # logger.info("watched_miners %s", watched_miners)

            seen_ids = set()

            if results:
                for result in results:
                    if result.minerId not in seen_ids:
                        section_name = result.sectionName
                        arrive_time = result.arriveTime
                        left_time = result.leftTime

                        if section_name not in section_breakup:
                            section_breakup[section_name] = {
                                "sectionName": section_name,
                                "minersCount": 0,
                                "firstArrival": None,
                                "firstDeparture": None,
                                "miners": []
                            }

                        # Updating the section data
                        section_data = section_breakup[section_name]
                        section_data["minersCount"] += 1  # Increment miner count
                        # logger.debug(f"firstArrival : {section_data['firstArrival']} {arrive_time} {type(section_breakup[section_name])} {section_breakup[section_name]}")

                        # Updating first arrival time
                        if section_data["firstArrival"] is None or (arrive_time and arrive_time < section_data["firstArrival"]):
                            section_data["firstArrival"] = arrive_time

                        # logger.debug(f"firstDeparture : {section_data['firstDeparture']} {left_time}")

                        # Updating first departure time
                        if section_data["firstDeparture"] is None or (left_time and left_time < section_data["firstDeparture"]):
                            section_data["firstDeparture"] = left_time

                        miners_data = section_breakup[section_name]["miners"]

                        watchlist_id = watched_miners.get(result.minerId, None)
                        is_watched = True if watchlist_id is not None else False

                        miner_breakup_data = {
                            "minerId": result.minerId,
                            "minerName": result.minerName,
                            "arriveTime": convert_dt_to_tz(result.arriveTime, tz).strftime('%I:%M %p').lower() if result.arriveTime is not None else None, 
                            "leftTime": convert_dt_to_tz(result.leftTime, tz).strftime('%I:%M %p').lower() if result.leftTime is not None else None,
                            "travelTo": result.travelTo.strftime("%H:%M") if result.travelTo is not None else None,
                            "travelFrom": result.travelFrom.strftime("%H:%M") if result.travelFrom is not None else None,
                            "onSection": result.onSection.strftime("%H:%M") if result.onSection is not None else None,
                            "arriveTimeCC": None,
                            "leftTimeCC": None,
                            "travelToCC": None,
                            "travelFromCC": None,
                            "onSectionCC": None,
                            "isWatched": is_watched,
                            "watchlistId": watchlist_id
                        }
                        miners_data.append(miner_breakup_data)
                        seen_ids.add(result.minerId)

                for section_data in section_breakup.values():
                    # Converting first arrival and departure to the desired format
                    miners_data = section_data.get('miners')
                    # logger.debug(f"firstArrival : {section_data['firstArrival']}  {type(section_data['firstArrival'])}")
                    section_data["firstArrival"] = convert_dt_to_tz(section_data["firstArrival"], tz).strftime('%I:%M %p').lower() if section_data["firstArrival"] else None
                    section_data["firstDeparture"] = convert_dt_to_tz(section_data["firstDeparture"], tz).strftime('%I:%M %p').lower() if section_data["firstDeparture"] else None
                    section_data["miners"] = update_CC(miners_data, 'arriveTime')

                section_breakup = list(section_breakup.values())
                # logger.debug(f"sort_by :{sort_by}")

                def safe_datetime(value):
                    try:
                        # logger.debug(f"Value from safe_datetime:{value} : {datetime.strptime(value, '%I:%M %p') if value is not None else datetime.max}")
                        if value is None :
                            logger.debug(f"datetime.max assigned : {value}")
                        return (
                            datetime.strptime(value, "%I:%M %p")
                            if value is not None
                            else datetime.max
                        )
                    except ValueError:
                        # logger.debug(f"Value from safe_datetime:{value} : {datetime.strptime(value, '%I:%M %p') if value is not None else datetime.max}")
                        return datetime.max

                if sort_by == "first_arrival":
                    section_breakup.sort(key=lambda x: (safe_datetime(x["firstArrival"]), x["sectionName"]))
                else:
                    section_breakup.sort(key=lambda x: (safe_datetime(x["firstDeparture"]), x["sectionName"]))

                logger.debug("get_miner_data_sectionwise in time %s", tm.time()-start_func)

                # logger.debug(f"section_breakup : {section_breakup}")
                return section_breakup
            logger.debug("get_miner_data_sectionwise time %s", tm.time()-start_func)
            return []

        except Exception as e:
            logger.error(f"Error in get_miner_data_sectionwise_v3: {e}", exc_info=True)
            return []


    def get_miner_data_sectionwise(self, mine_id: int, mine_dict:dict, user_id: int, shift_id, epoch_range: tuple, section_id: int=None, sort_by: str="first_arrival") -> list:
        try:
            location_data_manager_obj = LocationDataManager(self.session)
            start_of_day, end_of_day = epoch_range
            tz = mine_dict.get("tz_code") or pytz.utc

            stmt = (
            select(
                Section.ext_section_name.label("sectionName"),
                func.count(func.distinct(MinerStatus.miner_id)).label("minersCount"),
                func.min(MinerStatus.section_arrive).label("firstArrival"),
                func.min(MinerStatus.section_left).label("firstDeparture"),
                func.min(MinerStatus.check_in).label("checkIn")

                )
                .select_from(MinerStatus)
                .join(Miner)
                .join(
                        latest_timestamp_subquery,
                        and_(
                            latest_timestamp_subquery.c.miner_id == MinerStatus.miner_id,
                            latest_timestamp_subquery.c.latest_timestamp == MinerStatus.msg_timestamp
                        )
                    )
                .join(
                Section,
                or_(
                    MinerStatus.section_id == Section.id, 
                    MinerStatus.last_section == Section.id
                )
                )
                .distinct()
                .filter(Miner.mine_id == mine_id)
                .filter(Section.mine_id == mine_id)
                .filter(MinerStatus.is_active == 1)
                .filter(MinerStatus.shift_id == shift_id)
                .filter(apply_epoch_range_conditions(MinerStatus.msg_timestamp, start_of_day, end_of_day))
                # # .filter(MinerStatus.msg_timestamp == largest_msg_timestamp)
                # .filter(or_(func.cast(MinerStatus.section_left, TIME) <= current_shift["end"], MinerStatus.section_left == None))

                .group_by(Section.ext_section_name)
            )

            if section_id is not None:
                # stmt = stmt.filter(MinerStatus.last_section == section_id)
                stmt = stmt.filter(
                    or_(
                        MinerStatus.last_section == section_id,
                        MinerStatus.section_id == section_id
                    )
                )
            # stmt = stmt.group_by(Section.ext_section_name)
            results = self.get_execute_all(stmt)

            section_breakup = []
            for result in results:
                section_data = {
                "sectionName": result.sectionName,
                "minersCount": result.minersCount,
                "firstArrival": convert_dt_to_tz(result.firstArrival, tz).strftime('%I:%M %p').lower() if result.firstArrival else None,
                "firstDeparture": convert_dt_to_tz(result.firstDeparture, tz).strftime('%I:%M %p').lower() if result.firstDeparture else None,
                "miners": []  
            }
                section_breakup.append(section_data)

            for section_data in section_breakup:
                miner_details_query = (
                    select(
                        Miner.id.label("minerId"),
                        func.concat(Miner.first_name, " ", Miner.last_name).label("minerName"),
                        MinerStatus.section_arrive.label("arriveTime"),
                        MinerStatus.section_left.label("leftTime"),
                        MinerStatus.travel_to.label("travelTo"),
                        MinerStatus.travel_from.label("travelFrom"),
                        MinerStatus.on_section.label("onSection"),
                    )
                    .distinct()
                    .select_from(MinerStatus)
                    .join(Miner, MinerStatus.miner_id == Miner.id)
                    .join(Section, or_(MinerStatus.section_id == Section.id, MinerStatus.last_section == Section.id))
                    .join(
                        latest_timestamp_subquery,
                        and_(
                            latest_timestamp_subquery.c.miner_id == MinerStatus.miner_id,
                            latest_timestamp_subquery.c.latest_timestamp == MinerStatus.msg_timestamp
                        )
                    )
                    .filter(Miner.mine_id == mine_id)
                    .filter(MinerStatus.is_active == 1)
                    .filter(Section.ext_section_name == section_data["sectionName"])
                    .filter(MinerStatus.shift_id == shift_id)
                    .filter(apply_epoch_range_conditions(MinerStatus.msg_timestamp, start_of_day, end_of_day))
                )

                miner_details = self.get_execute_all(miner_details_query)
                if miner_details:  
                    miner_details_dict = [dict(zip(miner_details.keys(), row)) for row in miner_details]
                    sort_field = "arriveTime" if sort_by == "first_arrival" else "leftTime"
                    all_values_not_none = all(miner.get(sort_field) is not None for miner in miner_details_dict)
                    if all_values_not_none:
                        miner_details_dict.sort(key=itemgetter(sort_field, "minerName"))

                miners_count_breakup = []
                for miner_detail in miner_details_dict:
                    is_watched = check_if_miner_watched(location_data_manager_obj, miner_detail["minerId"], user_id)
                    watchlist_id = get_watchlist_id(location_data_manager_obj, miner_detail["minerId"], user_id)
                    miner_breakup_data = {
                        "minerId":miner_detail["minerId"],
                        "minerName": miner_detail["minerName"],
                        "arriveTime": convert_dt_to_tz(miner_detail["arriveTime"], tz).strftime('%I:%M %p').lower() if miner_detail["arriveTime"] is not None else None, 
                        "leftTime": convert_dt_to_tz(miner_detail["leftTime"], tz).strftime('%I:%M %p').lower() if miner_detail["leftTime"] is not None else None,
                        "travelTo": miner_detail["travelTo"].strftime("%H:%M") if miner_detail["travelTo"] is not None else None,
                        "travelFrom": miner_detail["travelFrom"].strftime("%H:%M") if miner_detail["travelFrom"] is not None else None,
                        "onSection": miner_detail["onSection"].strftime("%H:%M") if miner_detail["onSection"] is not None else None,
                        "arriveTimeCC": None,
                        "leftTimeCC": None,
                        "travelToCC": None,
                        "travelFromCC": None,
                        "onSectionCC": None,
                        "isWatched":is_watched,
                        "watchlistId": watchlist_id if watchlist_id else None
                    }
                    miners_count_breakup.append(miner_breakup_data)

                final_miners_count_breakup = update_CC(miners_count_breakup, 'arriveTime')
                section_data["miners"] = final_miners_count_breakup

            if sort_by == "first_arrival":
                # section_breakup.sort(key=lambda x: (datetime.strptime(x["firstArrival"], '%I:%M %p'), x["sectionName"]))
                section_breakup.sort(key=lambda x: (datetime.strptime(x["firstArrival"], '%I:%M %p') if x["firstArrival"] is not None else None, x["sectionName"]))
            else:
                # section_breakup.sort(key=lambda x: (datetime.strptime(x["firstDeparture"], '%I:%M %p'), x["sectionName"]))
                section_breakup.sort(key=lambda x: (datetime.strptime(x["firstDeparture"], '%I:%M %p') if x["firstDeparture"] is not None else None,x["sectionName"]))

            return section_breakup

        except Exception as e:
            logger.error(f"Error in get_miner_data_sectionwise: {e}", exc_info=True)


    def get_miner_data_sectionwise_v2(self, mine_id: int,  mine_dict:dict, user_id: int, shift_id, epoch_range: tuple, section_id: int=None, sort_by: str="first_arrival") -> list:
        try:
            start_func=tm.time()
            location_data_manager_obj = LocationDataManager(self.session)

            start_of_day, end_of_day = epoch_range
            tz = mine_dict.get("tz_code") or pytz.utc
            latest_timestamp_subquery = generate_latest_timestamp_subquery_v2( 1, start_of_day, end_of_day)

            stmt = (
            select(
                Section.ext_section_name.label("sectionName"),
                func.count(func.distinct(MinerStatus.miner_id)).label("minersCount"),
                func.min(MinerStatus.section_arrive).label("firstArrival"),
                func.min(MinerStatus.section_left).label("firstDeparture"),
                func.min(MinerStatus.check_in).label("checkIn")

                )
                .select_from(MinerStatus)
                .join(Miner)
                .join(
                        latest_timestamp_subquery,
                        and_(
                            latest_timestamp_subquery.c.miner_id == MinerStatus.miner_id,
                            latest_timestamp_subquery.c.latest_timestamp == MinerStatus.msg_timestamp
                        )
                    )
                .join(
                Section,
                or_(
                    MinerStatus.section_id == Section.id, 
                    MinerStatus.last_section == Section.id
                )
                )
                .distinct()
                .filter(Miner.mine_id == mine_id)
                .filter(Section.mine_id == mine_id)
                .filter(MinerStatus.is_active == 1)
                .filter(MinerStatus.shift_id == shift_id)
                .filter(apply_epoch_range_conditions(MinerStatus.msg_timestamp, start_of_day, end_of_day))
                # # .filter(MinerStatus.msg_timestamp == largest_msg_timestamp)
                # .filter(or_(func.cast(MinerStatus.section_left, TIME) <= current_shift["end"], MinerStatus.section_left == None))

                .group_by(Section.ext_section_name)
            )

            if section_id is not None:
                # stmt = stmt.filter(MinerStatus.last_section == sectionId)
                stmt = stmt.filter(
                    or_(
                        MinerStatus.last_section == section_id,
                        MinerStatus.section_id == section_id
                    )
                )
            # stmt = stmt.group_by(Section.ext_section_name)
            start_query = tm.time()
            results = self.get_execute_all(stmt)
            logger.info("query time %s", tm.time()-start_query)
            # logger.info("sql %s", stmt.compile(dialect=mssql.dialect(),
            #                                    compile_kwargs={"literal_binds": True}))

            section_breakup = []
            for result in results:
                section_data = {
                "sectionName": result.sectionName,
                "minersCount": result.minersCount,
                "firstArrival": convert_dt_to_tz(result.firstArrival, tz).strftime('%I:%M %p').lower() if result.firstArrival else None,
                "firstDeparture": convert_dt_to_tz(result.firstDeparture, tz).strftime('%I:%M %p').lower() if result.firstDeparture else None,
                "miners": []  
            }
                section_breakup.append(section_data)

            # Load user watchlist
            start_load = tm.time()
            watched_miners = {}
            user_watchlist = get_user_watchlist(location_data_manager_obj, user_id)
            if user_watchlist is not None:
                for watchlist_id, _, m_id in user_watchlist:
                    watched_miners[m_id] = watchlist_id
            logger.info("get_user_watchlist time %s", tm.time()-start_load)

            logger.info("watched_miners %s", watched_miners)

            section_time_sum = 0
            for section_data in section_breakup:
                start_sb = tm.time()
                miner_details_query = (
                    select(
                        Miner.id.label("minerId"),
                        func.concat(Miner.first_name, " ", Miner.last_name).label("minerName"),
                        MinerStatus.section_arrive.label("arriveTime"),
                        MinerStatus.section_left.label("leftTime"),
                        MinerStatus.travel_to.label("travelTo"),
                        MinerStatus.travel_from.label("travelFrom"),
                        MinerStatus.on_section.label("onSection"),
                    )
                    .distinct()
                    .select_from(MinerStatus)
                    .join(Miner, MinerStatus.miner_id == Miner.id)
                    .join(Section, or_(MinerStatus.section_id == Section.id, MinerStatus.last_section == Section.id))
                    .join(
                        latest_timestamp_subquery,
                        and_(
                            latest_timestamp_subquery.c.miner_id == MinerStatus.miner_id,
                            latest_timestamp_subquery.c.latest_timestamp == MinerStatus.msg_timestamp
                        )
                    )
                    .filter(Miner.mine_id == mine_id)
                    .filter(MinerStatus.is_active == 1)
                    .filter(Section.ext_section_name == section_data["sectionName"])
                    .filter(MinerStatus.shift_id == shift_id)
                    .filter(apply_epoch_range_conditions(MinerStatus.msg_timestamp, start_of_day, end_of_day))
                )

                miner_details = self.get_execute_all(miner_details_query)
                section_time_sum +=  tm.time()-start_sb
                logger.info("query-section time %s",section_time_sum)
                # logger.info("sql %s", miner_details_query.compile(dialect=mssql.dialect(),
                #                                compile_kwargs={"literal_binds": True}))
                if miner_details:
                    miner_details_dict = [dict(zip(miner_details.keys(), row)) for row in miner_details]
                    sort_field = "arriveTime" if sort_by == "first_arrival" else "leftTime"
                    all_values_not_none = all(miner.get(sort_field) is not None for miner in miner_details_dict)
                    if all_values_not_none:
                        miner_details_dict.sort(key=itemgetter(sort_field, "minerName"))

                miners_count_breakup = []
                for miner_detail in miner_details_dict:
                    # is_watched = check_if_miner_watched(LocationDataManager_obj, miner_detail["minerId"], user_id)
                    # watchlist_id = get_watchlist_id(LocationDataManager_obj, miner_detail["minerId"], user_id)

                    watchlist_id = watched_miners.get(miner_detail["minerId"], None)
                    is_watched = True if watchlist_id is not None else False

                    miner_breakup_data = {
                        "minerId":miner_detail["minerId"],
                        "minerName": miner_detail["minerName"],
                        "arriveTime": convert_dt_to_tz(miner_detail["arriveTime"], tz).strftime('%I:%M %p').lower() if miner_detail["arriveTime"] is not None else None, 
                        "leftTime": convert_dt_to_tz(miner_detail["leftTime"], tz).strftime('%I:%M %p').lower() if miner_detail["leftTime"] is not None else None,
                        "travelTo": miner_detail["travelTo"].strftime("%H:%M") if miner_detail["travelTo"] is not None else None,
                        "travelFrom": miner_detail["travelFrom"].strftime("%H:%M") if miner_detail["travelFrom"] is not None else None,
                        "onSection": miner_detail["onSection"].strftime("%H:%M") if miner_detail["onSection"] is not None else None,
                        "arriveTimeCC": None,
                        "leftTimeCC": None,
                        "travelToCC": None,
                        "travelFromCC": None,
                        "onSectionCC": None,
                        "isWatched": is_watched,
                        "watchlistId": watchlist_id # watchlist_id if watchlist_id else None
                    }
                    miners_count_breakup.append(miner_breakup_data)

                final_miners_count_breakup = update_CC(miners_count_breakup, 'arriveTime')
                section_data["miners"] = final_miners_count_breakup
            logger.debug(f"sort_by :{sort_by}")

            def safe_datetime(value):
                try:
                    # logger.debug(f"Value from safe_datetime:{value} : {datetime.strptime(value, '%I:%M %p') if value is not None else datetime.max}")
                    return (
                        datetime.strptime(value, "%I:%M %p")
                        if value is not None
                        else datetime.max
                    )
                except ValueError:
                    # logger.debug(f"Value from safe_datetime:{value} : {datetime.strptime(value, '%I:%M %p') if value is not None else datetime.max}")
                    return datetime.max

            if sort_by == "first_arrival":
                section_breakup.sort(key=lambda x: (safe_datetime(x["firstArrival"]), x["sectionName"]))
            else:
                section_breakup.sort(key=lambda x: (safe_datetime(x["firstDeparture"]), x["sectionName"]))

            logger.info("get_miner_data_sectionwise time %s", tm.time()-start_func)

            return section_breakup

        except Exception as e:
            logger.error(f"Error in get_miner_data_sectionwise: {e}", exc_info=True)

    def get_watchlist_data(self, mine_id: int, user_id: int) -> WatchlistResponseSchema:

        try:
            location_data_manager_obj = LocationDataManager(self.session)

            mine_dict = get_mine_with_timezone(location_data_manager_obj, mine_id)
            # Mine check
            if mine_dict is None :
                logger.error("Error in get_watchlist_data: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")


            # Mine's Timezone related code
            mine_timezone = mine_dict.get("tz_code") or pytz.utc
            mine_current_datetime = datetime.now(mine_timezone)
            mine_current_date = mine_current_datetime.date()
            formatted_mine_current_datetime= mine_current_datetime.isoformat(timespec='milliseconds')
            logger.debug(f"Mine's Current_datetime  : {mine_current_datetime}, tz : {mine_timezone}")

            epoch_range = get_epoch_range_using_tz(mine_current_date, mine_timezone)
    
            # Main response fetching            
            watchlist_miners = self.get_miners_from_watchlist(mine_id, user_id, epoch_range=epoch_range, mine_dict=mine_dict) or []

            # Response building
            watchlist_response = WatchlistResponseSchema(
                lastUpdatedTs=formatted_mine_current_datetime,
                watchlist=watchlist_miners
            )
            return watchlist_response,None

        except Exception as e:
            logger.error(f"Getting Error while fetching watchlist_data: {e}", exc_info=True)

    def get_miners_from_watchlist(self, mine_id: int, user_id: int,  epoch_range: tuple, mine_dict:dict) -> list[Watchlist]:
        try:
            start_of_day, end_of_day = epoch_range
            results = []
            watchlist = []
            tz = mine_dict.get("tz_code") or pytz.utc
           


           
            latest_timestamp_subquery = generate_latest_timestamp_subquery_v2( 1, start_of_day, end_of_day)
            stmt = (
                select(
                    Miner.id.label("minerId"),
                    func.concat(Miner.first_name, " ", Miner.last_name).label("minerName"),
                    func.min(MinerStatus.went_ug).label("ug"),                     
                    MinerStatus.last_ug.label("lastUG"),
                    MinerStatus.on_section.label("onSection"),
                    Section.ext_section_name.label("section"),
                    Watchlist.id.label("watchlistId")
                )
                .distinct()
                .select_from(Watchlist)
                .join(Miner, Watchlist.miner_id == Miner.id)
                .join(MinerStatus, Watchlist.miner_id == MinerStatus.miner_id)
                .join(
                        latest_timestamp_subquery,
                        and_(
                            latest_timestamp_subquery.c.miner_id == MinerStatus.miner_id,
                            latest_timestamp_subquery.c.latest_timestamp == MinerStatus.msg_timestamp
                        )
                    )
                .outerjoin(Section, or_(MinerStatus.section_id == Section.id, MinerStatus.last_section == Section.id))
                .filter(Watchlist.user_id == user_id)
                .filter(Miner.mine_id == mine_id)
                # .filter(Section.mine_id == mine_id)
                .filter(MinerStatus.is_active==1)
                .filter(apply_epoch_range_conditions(MinerStatus.msg_timestamp, start_of_day, end_of_day))
                # .filter(MinerStatus.shift_id == shift_id)
            )

            results = self.get_execute_all(stmt)

            for result in results:
                went_ug = result.ug
                last_ug = result.lastUG
                total_ug_hrs = timedelta_to_hh_mm(last_ug,went_ug)
                miner_data = {
                    "minerId": result.minerId,
                    "minerName": result.minerName,
                    "ug": convert_dt_to_tz(result.ug, tz).strftime('%I:%M %p').lower() if result.ug else None,
                    "ugHrs": total_ug_hrs,
                    "onSection": result.onSection.strftime("%H:%M") if result.onSection else None,
                    "section": result.section,
                    "ugCC": None,
                    "ugHrsCC": None,
                    "onSectionCC": None,
                    "isWatched": True,
                    "watchlistId": result.watchlistId if result.watchlistId else None 
                }

                watchlist.append(miner_data)

            
            # Extracting `minerIds` from the live_watchlist
            miner_ids = {result["minerId"] for result in watchlist}
            # Creating a modified query to fetch miners from Watchlist not present in live_watchlist
            offline_miners_query = (
                select(
                    Miner.id.label("minerId"),
                    func.concat(Miner.first_name, " ", Miner.last_name).label("minerName"),
                    Watchlist.id.label("watchlistId")
                )
                .select_from(Watchlist)
                .join(Miner, Watchlist.miner_id == Miner.id)
                .filter(Watchlist.user_id == user_id)
                .filter(Miner.mine_id == mine_id)
                .filter(Miner.id.notin_(miner_ids))
            )

            # Executing the modified query to retrieve miners in Watchlist but not in live_watchlist
            absent_miners_result = self.get_execute_all(offline_miners_query)
            
            
            absent_watchlist = [
                {                    
                    "minerId": result.minerId,
                    "minerName": result.minerName,
                    "ug": None,
                    "ugHrs": None,
                    "onSection": None,
                    "section": None,
                    "ugCC": None,
                    "ugHrsCC": None,
                    "onSectionCC": None,
                    "isWatched": True,
                    "watchlistId": result.watchlistId if result.watchlistId else None
                }
                for result in absent_miners_result
            ]
            
            # Merging live_watchlist and absent_watchlist while ensuring uniqueness based on minerId
            miner_dict = {}
            for item in watchlist + absent_watchlist:
                miner_id = item["minerId"]
                if miner_id not in miner_dict:
                    miner_dict[miner_id] = item

            # Creating a list of merged results
            merged_wachlists = list(miner_dict.values())
            
            final_watchlist = update_CC(merged_wachlists, 'ug')

            return final_watchlist

        except Exception as e:
            logger.error(f"Getting Error while fetching watchlist_data: {e}", exc_info=True)
            return []


    def get_sections_report(self, mine_id: int, user_id: int, report_date: date, section_id: Optional[int] = None) -> Tuple[ReportSectionsResponseSchema, APIError]:
        try:
            location_data_manager_obj = LocationDataManager(self.session)

            mine_dict = get_mine_with_timezone(location_data_manager_obj, mine_id)
            # Mine check
            if mine_dict is None :
                logger.error("Error in get_sections_report: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")


            # Mine's Timezone related code
            mine_timezone = mine_dict.get("tz_code") or pytz.utc
            mine_current_datetime = datetime.now(mine_timezone)
            logger.debug(f"Mine's Current_datetime  : {mine_current_datetime}, tz : {mine_timezone}")

            # Future date check using mine date
            if report_date > mine_current_datetime.date():
                return {"sectionBreakup": []}, None


            # Finding the start and end of the day using given date - USING MINE'S TZ
            start_of_day_mine_tz, end_of_day_mine_tz = get_epoch_range_using_tz(report_date, mine_timezone)

            start_of_day = start_of_day_mine_tz
            end_of_day = end_of_day_mine_tz

            latest_timestamp_subquery = generate_latest_timestamp_subquery(start_of_day, end_of_day)

            stmt = (
                select(
                    Section.ext_section_name.label("sectionName"),
                    func.count(func.distinct(MinerStatus.miner_id)).label("totalNodes"),
                    func.min(MinerStatus.section_arrive).label("firstArrival"),
                    func.min(MinerStatus.section_left).label("firstDeparture"),
                )
                .select_from(MinerStatus)
                .join(Miner)
                .join(
                        latest_timestamp_subquery,
                        and_(
                            latest_timestamp_subquery.c.miner_id == MinerStatus.miner_id,
                            latest_timestamp_subquery.c.latest_timestamp == MinerStatus.msg_timestamp
                        )
                    )
                .join(
                    Section, 
                    or_(
                        MinerStatus.section_id == Section.id, 
                        MinerStatus.last_section == Section.id
                    )
                )
                .filter(Miner.mine_id == mine_id)
                .filter(Section.mine_id == mine_id)
                .filter(MinerStatus.is_active == 1)

                .filter(and_(MinerStatus.msg_timestamp >= start_of_day, MinerStatus.msg_timestamp <= end_of_day))

                .group_by(Section.ext_section_name)
            )

            if section_id is not None:
                # stmt = stmt.filter(MinerStatus.last_section == section_id)
                stmt = stmt.filter(
                    or_(
                        MinerStatus.last_section == section_id,
                        MinerStatus.section_id == section_id
                    )
                )
            results = self.get_execute_all(stmt)

            section_breakup = []
            for result in results:
                section_data = {
                    "sectionName": result.sectionName,
                    "totalNodes": result.totalNodes,
                    "firstArrival": convert_dt_to_tz(result.firstArrival, mine_timezone).strftime('%I:%M %p').lower() if result.firstArrival else None,
                    "firstDeparture": convert_dt_to_tz(result.firstDeparture, mine_timezone).strftime('%I:%M %p').lower() if result.firstDeparture else None,
                    "totalNodesBreakup": []  
                }
                section_breakup.append(section_data)

            # Load user watchlist
            watched_miners = {}
            user_watchlist = get_user_watchlist(location_data_manager_obj, user_id)
            if user_watchlist is not None:
                for watchlist_id, _, m_id in user_watchlist:
                    watched_miners[m_id] = watchlist_id
            logger.info("watched_miners %s", watched_miners)

            for section_data in section_breakup:

                miner_details_query = (
                    select(
                        Miner.id.label("minerId"),
                        func.concat(Miner.first_name, " ", Miner.last_name).label("minerName"),
                        MinerStatus.section_arrive.label("arriveTime"),
                        MinerStatus.section_left.label("leftTime"),
                        MinerStatus.travel_to.label("travelTo"),
                        MinerStatus.travel_from.label("travelFrom"),
                        MinerStatus.on_section.label("onSection"),
                        MinerStatus.shift_id.label("shiftId"),
                    )
                    .select_from(MinerStatus)
                    .join(Miner, MinerStatus.miner_id == Miner.id)
                    .join(Section, or_(MinerStatus.section_id == Section.id, MinerStatus.last_section == Section.id))
                    .join(
                        latest_timestamp_subquery,
                        and_(
                            latest_timestamp_subquery.c.miner_id == MinerStatus.miner_id,
                            latest_timestamp_subquery.c.latest_timestamp == MinerStatus.msg_timestamp
                        )
                    )
                    .filter(Miner.mine_id == mine_id)
                    .filter(MinerStatus.is_active == 1)
                    .filter(Section.ext_section_name == section_data["sectionName"])
                    .filter(and_(MinerStatus.msg_timestamp >= start_of_day, MinerStatus.msg_timestamp <= end_of_day))
                    .distinct()
                )

                miner_details = self.get_execute_all(miner_details_query)
                if miner_details:  
                    miner_details_dict = [dict(zip(miner_details.keys(), row)) for row in miner_details]
                #     sort_field = "arriveTime" if sort_by == "first_arrival" else "leftTime"
                #     miner_details_dict.sort(key=itemgetter(sort_field, "minerName"))

                shift_id_wise_lists = {}
                # miners_count_breakup = []
                for miner_detail in miner_details_dict:
                    
                    shift_id = miner_detail.get("shiftId")

                    # Creating a new list for the shiftId if it doesn't exist
                    if shift_id not in shift_id_wise_lists:
                        shift_id_wise_lists[shift_id] = []

                    watchlist_id = watched_miners.get(miner_detail["minerId"], None)
                    is_watched = True if watchlist_id is not None else False

                    miner_breakup_data = {
                        "minerId":miner_detail["minerId"],
                        "minerName": miner_detail["minerName"],
                        "arriveTime": convert_dt_to_tz(miner_detail["arriveTime"], mine_timezone).strftime('%I:%M %p').lower() if miner_detail["arriveTime"] is not None else None, 
                        "leftTime": convert_dt_to_tz(miner_detail["leftTime"], mine_timezone).strftime('%I:%M %p').lower() if miner_detail["leftTime"] is not None else None,
                        "travelTo": miner_detail["travelTo"].strftime("%H:%M") if miner_detail["travelTo"] is not None else None,
                        "travelFrom": miner_detail["travelFrom"].strftime("%H:%M") if miner_detail["travelFrom"] is not None else None,
                        "onSection": miner_detail["onSection"].strftime("%H:%M") if miner_detail["onSection"] is not None else None,
                        "arriveTimeCC": None,
                        "leftTimeCC": None,
                        "travelToCC": None,
                        "travelFromCC": None,
                        "onSectionCC": None,
                        "isWatched":is_watched,
                        "watchlistId": watchlist_id, # watchlist_id if watchlist_id else None,
                        "shiftId" : miner_detail["shiftId"],
                    }

                    # miners_count_breakup.append(miner_breakup_data)
                    
                    shift_id_wise_lists[shift_id].append(miner_breakup_data)
                
                final_miners_count_breakup=[]
                for shift_id, shift_wise_list in shift_id_wise_lists.items():
                    final_miners_count_breakup += update_CC(shift_wise_list, 'arriveTime')

                section_data["totalNodesBreakup"] = [{key: value for key, value in miner_data.items() if key != 'shiftId'}
                                                     for miner_data in final_miners_count_breakup]

            sections_report_response = ReportSectionsResponseSchema(
                sectionBreakup=section_breakup
            )
            return sections_report_response, None

        except Exception as e:
            logger.error(f"Error in get_sections_report: {e}", exc_info=True)


    def get_sections_report_v2(
            self,
            mine_id: int,
            user_id: int,
            report_date: date,
            section_id: Optional[int] = None,
        ) -> Tuple[Optional[ReportSectionsResponseSchema], Optional[APIError]]:
        try:
            start_func = tm.time()
            logger.debug("get_sections_report started")
            location_data_manager_obj = LocationDataManager(self.session)

            mine_dict = get_mine_with_timezone(location_data_manager_obj, mine_id)
            # Mine check
            if mine_dict is None :
                logger.error("Error in get_sections_report: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")

            # Mine's Timezone related code
            mine_timezone = mine_dict.get("tz_code") or pytz.utc
            mine_current_datetime = datetime.now(mine_timezone)
            logger.debug(f"Mine's Current_datetime: {mine_current_datetime}, tz : {mine_timezone}")

            # Future date check using mine date
            if report_date > mine_current_datetime.date():
                logger.debug("Future date is provided.")
                return ReportSectionsResponseSchema(sectionBreakup=[]), None

            # Finding the start and end of the day using given date - USING MINE'S TZ
            start_of_day, end_of_day = get_epoch_range_using_tz(report_date, mine_timezone)
            # Getting subquery
            latest_timestamp_subquery = generate_latest_timestamp_subquery(start_of_day, end_of_day)

            stmt = (
                select(
                    MinerStatus.id,
                    Section.ext_section_name.label("sectionName"),
                    Miner.id.label("minerId"),
                    func.concat(Miner.first_name, " ", Miner.last_name).label("minerName"),
                    MinerStatus.section_arrive.label("arriveTime"),
                    MinerStatus.section_left.label("leftTime"),
                    MinerStatus.travel_to.label("travelTo"),
                    MinerStatus.travel_from.label("travelFrom"),
                    MinerStatus.on_section.label("onSection"),
                    MinerStatus.shift_id.label("shiftId"),
                )
                .select_from(MinerStatus)
                .join(Miner)
                .join(
                        latest_timestamp_subquery,
                        and_(
                            latest_timestamp_subquery.c.miner_id
                                == MinerStatus.miner_id,
                            latest_timestamp_subquery.c.latest_timestamp
                                == MinerStatus.msg_timestamp
                        )
                    )
                .join(
                    Section, MinerStatus.last_section == Section.id
                )
                .filter(Miner.mine_id == mine_id)
                # .filter(Section.mine_id == mine_id)
                .filter(MinerStatus.is_active == 1)
                .filter(and_(MinerStatus.msg_timestamp >= start_of_day,
                             MinerStatus.msg_timestamp <= end_of_day))
            )

            if section_id is not None:
                stmt = stmt.filter(MinerStatus.last_section == section_id)

            start_query = tm.time()
            results = self.fetch_all(stmt)
            logger.debug("query time %s", tm.time()-start_query)
            # logger.info("sql %s", stmt.compile(dialect=mssql.dialect(),
            #                                    compile_kwargs={"literal_binds": True}))
            results.sort(key=lambda x: (-x.id))

            # Fetching user watchlist
            start_query = tm.time()
            watched_miners = {}
            user_watchlist = get_user_watchlist(location_data_manager_obj, user_id)
            logger.debug("get_user_watchlist time %s", tm.time()-start_query)

            if user_watchlist is not None:
                for watchlist_entry in user_watchlist:
                    # Accessing Watchlist attributes
                    watchlist_id = watchlist_entry.id  # Watchlist entry's ID
                    m_id = watchlist_entry.miner_id    # Associated miner's ID

                    # Adding miner ID to the watched_miners dictionary,
                    # using watchlist_id as the value
                    watched_miners[m_id] = watchlist_id
            logger.debug("watched_miners %s", watched_miners)

            seen_ids = set()
            section_breakup = {}
            if results:
                for result in results:
                    if result.minerId in seen_ids:
                        continue

                    section_name = result.sectionName
                    if section_name not in section_breakup:
                        section_breakup[section_name]  = {
                            "sectionName": section_name,
                            "totalNodes": 0,
                            "totalNodesBreakup": []  
                        }

                    # Updating the section data
                    section_data = section_breakup.get(section_name, {})

                    # Incrementing miner count
                    total_nodes = section_data.get("totalNodes")
                    if total_nodes is not None:
                        section_data["totalNodes"] += 1

                    watchlist_id = watched_miners.get(result.minerId, None)
                    is_watched = True if watchlist_id is not None else False

                    # arriveTime conversion
                    arrive_time_converted = (convert_dt_to_tz(result.arriveTime, mine_timezone)
                                                if result.arriveTime is not None else None)
                    arrive_time_str = (arrive_time_converted.strftime('%I:%M %p').lower()
                                        if arrive_time_converted is not None else None)

                    # leftTime conversion
                    left_time_converted = (convert_dt_to_tz(result.leftTime, mine_timezone)
                                            if result.leftTime is not None else None)
                    left_time_str = (left_time_converted.strftime('%I:%M %p').lower()
                                        if left_time_converted is not None else None)

                    # building totalNodesBreakup
                    miners_data = section_data.get("totalNodesBreakup", [])
                    miner_breakup_data = {
                        "minerId":result.minerId,
                        "minerName": result.minerName,
                        "arriveTime": arrive_time_str, 
                        "leftTime": left_time_str,
                        "travelTo": (result.travelTo.strftime("%H:%M") 
                                        if result.travelTo is not None else None),
                        "travelFrom": (result.travelFrom.strftime("%H:%M") 
                                        if result.travelFrom is not None else None),
                        "onSection": (result.onSection.strftime("%H:%M") 
                                        if result.onSection is not None else None),
                        "arriveTimeCC": None,
                        "leftTimeCC": None,
                        "travelToCC": None,
                        "travelFromCC": None,
                        "onSectionCC": None,
                        "isWatched": is_watched,
                        "watchlistId": watchlist_id,
                        "shiftId" : result.shiftId,
                    }
                    miners_data.append(miner_breakup_data)
                    seen_ids.add(result.minerId)

                for section_data in section_breakup.values():
                    total_nodes_breakup = section_data.get("totalNodesBreakup")
                    total_nodes_breakup.sort(key=lambda x: x.get('shiftId'))

                    shift_id_wise_dict = {}
                    for node in total_nodes_breakup:
                        shift_id = node.get("shiftId")
                        if shift_id:
                            # Creating a new list for the shiftId if it doesn't exist
                            if shift_id not in shift_id_wise_dict:
                                shift_id_wise_dict[shift_id] = []

                            shift_id_list = shift_id_wise_dict.get(shift_id)
                            if shift_id_list is not None:
                                shift_id_list.append(node)

                    final_miners_count_breakup=[]
                    for shift_id, shift_wise_list in shift_id_wise_dict.items():
                        final_miners_count_breakup += update_CC(shift_wise_list, 'arriveTime')

                    section_data["totalNodesBreakup"]=[
                        {key: value for key, value in miner_data.items()
                        if key != 'shiftId'}
                        for miner_data in final_miners_count_breakup
                    ]

            section_breakup = list(section_breakup.values())
            sections_report_response = ReportSectionsResponseSchema(
                sectionBreakup=section_breakup
            )
            logger.debug("get_sections_report_v2 time %s", tm.time()-start_func)
            return sections_report_response, None

        except Exception as e:
            logger.error(f"Error in get_sections_report_v2: {e}", exc_info=True)
            return None, APIError(
                            500, APIErrorType.API_ERROR, f"Error {e} in get_sections_report_v2"
                        )

    def get_personnel_report_data(self, mine_id:int, miner_id:int, from_date:date, to_date:date) -> Tuple[ReportPersonnelSchema, APIError]:
        """Get Personnel Report data for the specified mine ID and date."""
        try:
            location_data_manager_obj = LocationDataManager(self.session)
            current_datetime_utc = datetime.now(timezone.utc)

            mine_check = check_mine_exists(location_data_manager_obj, mine_id)
            if not mine_check:
                logger.error(f"Error in get_checkins_data: Mine check failed: {not mine_check}")            
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")
            
            miner_check = check_miner_exists(location_data_manager_obj, miner_id)
            if not miner_check:
                logger.error(f"Error in get_checkins_data: Miner check failed: {not miner_check}")            
                return None, APIError(404, APIErrorType.MINER_NOT_FOUND, "Miner not found")

            if from_date > to_date or from_date > current_datetime_utc.date():                
                logger.error("No data available for the specified dates.")
                return get_empty_schema("reports/personnel",None)

            # personnel_miner_data = self.get_personnel_miner_data(mine_id, miner_id, from_date, to_date)

            # Loop through each day within the specified interval
            miners_data = []

            total_converted_went_ug = 0
            went_ug_count = 0
            total_converted_went_ag = 0
            went_ag_count = 0
            total_time_ug_count = 0

            went_ug_is_am = None
            went_ag_is_am = None
            

            from_date_time = datetime.combine(from_date, datetime.min.time(), tzinfo=timezone.utc)
            to_date_time = datetime.combine(to_date, datetime.min.time(), tzinfo=timezone.utc)
            days_difference = (to_date_time - from_date_time).days
            miner_name = None
            shift_set = set()
            # current_date = from_date
            # while current_date <= to_date:
            # Looping over
            for day in range(days_difference + 1):
                current_date = from_date + timedelta(days=day)
                from_date_time = datetime.combine(current_date, datetime.min.time(), tzinfo=timezone.utc)
                to_date_time = datetime.combine(current_date + timedelta(days=1), datetime.min.time(), tzinfo=timezone.utc)
                from_day_utc = int(from_date_time.timestamp())
                to_day_utc = int(to_date_time.timestamp()) - 1  # Subtracting 1 second to include the whole day

                start = tm.time()
                stmt = (
                    select(
                        func.concat(Miner.first_name, " ", Miner.last_name).label("miner_name"),
                        MinerStatus.went_ug, 
                        MinerStatus.went_ag,
                        MinerStatus.shift_id
                    )
                    .join(Miner, MinerStatus.miner_id == Miner.id)
                    .select_from(MinerStatus)
                    .filter(
                        Miner.mine_id == mine_id,
                        MinerStatus.miner_id == miner_id,
                        MinerStatus.is_active == 1,
                        and_(MinerStatus.msg_timestamp >= from_day_utc , MinerStatus.msg_timestamp <= to_day_utc)
                    )
                    .order_by(desc(MinerStatus.msg_timestamp)) 
                )

                stmt = stmt.limit(1)
                result = self.get_execute_all(stmt)

                logger.info("query exec time %s", tm.time() - start)

                # Process the result for the current day
                for miner_name, went_ug, went_ag, shift_id in result:
                    
                    total_time_ug =timedelta_to_hh_mm(went_ug,went_ag)
                    
                    if total_time_ug is not None:
                        hours, minutes = map(int, total_time_ug.split(':'))
                        formatted_total_time_ug = f"{hours} hrs {minutes} min"
                        total_time_ug_count += 1
                    else:
                        formatted_total_time_ug = None
                        
                    miners_data.append(
                        {
                            "date": current_date.strftime("%Y-%m-%d"),
                            "dayOfWeek": current_date.strftime("%A"),
                            "enterUG": went_ug.strftime("%I:%M %p").lower() if went_ug else None,
                            "leaveUG": went_ag.strftime("%I:%M %p").lower() if went_ag else None,
                            "totalTimeUG": formatted_total_time_ug,
                            "enterUGCC": None,
                            "leaveUGCC": None,
                            "totalTimeUGCC": None
                        }
                    )
                    updated_miners_data=[]
                    if len(miners_data)>0 or miners_data is not None:
                        updated_miners_data+= update_CC(miners_data, 'enterUG')

                    # went_ug_values.append(went_ug.hour * 60 + went_ug.minute)
                    # went_ag_values.append(went_ag.hour * 60 + went_ag.minute)

                    if went_ug is not None :
                        converted_went_ug = went_ug.hour * 60 + went_ug.minute
                        total_converted_went_ug += converted_went_ug
                        went_ug_count += 1
                    
                    if went_ag is not None:    
                        converted_went_ag = went_ag.hour * 60 + went_ag.minute
                        total_converted_went_ag += converted_went_ag
                        went_ag_count += 1   

                    
                    if shift_id is not None:    
                        shift_set.add(shift_id)
                            
    
            total_hours_ug = sum(int(miner['totalTimeUG'].split(' ')[0]) for miner in miners_data if miner['totalTimeUG'])
            total_minutes_ug = sum(int(miner['totalTimeUG'].split(' ')[2]) for miner in miners_data if miner['totalTimeUG'])
            total_miners = len(miners_data)

            if total_miners > 0:
                
                if total_converted_went_ug != 0:
                    avg_went_ug_minutes = total_converted_went_ug/ went_ug_count
                    avg_went_ug_time = minutes_to_datetime(avg_went_ug_minutes)
                    avg_went_ug_str = avg_went_ug_time.strftime("%I:%M %p") if avg_went_ug_time else None
                else:
                    avg_went_ug_str= None
                
                if total_converted_went_ag != 0:
                    avg_went_ag_minutes = total_converted_went_ag/ went_ag_count
                    avg_went_ag_time = minutes_to_datetime(avg_went_ag_minutes)
                    avg_went_ag_str = avg_went_ag_time.strftime("%I:%M %p") if avg_went_ag_time else None
                else:
                    avg_went_ag_str= None
                
                if total_hours_ug >= 0 and total_minutes_ug > 0 :   
                    avg_total_ug_minutes = (total_hours_ug * 60 + total_minutes_ug) / total_time_ug_count
                    avg_time_ug_hours, avg_time_ug_minutes = divmod(avg_total_ug_minutes, 60)
                    avg_time_ug_str = f"{int(avg_time_ug_hours)}.{int(avg_time_ug_minutes)} hrs"
                else:
                    avg_time_ug_str= None

            else:
                avg_went_ug_str = None
                avg_went_ag_str = None
                avg_time_ug_str = None

            shifts = []
            
            if len(shift_set) != 0:
                shifts = get_shifts_by_ids(location_data_manager_obj, mine_id, shift_set)
            
            if miner_name is None:
                logger.error("No data available for the specified miner.")
                return get_empty_schema("reports/personnel",None)
            
            personnel_report_data = ReportPersonnelSchema(
                    minerId= miner_id,
                    minerName= miner_name,
                    avgEnterUG= avg_went_ug_str, #.strftime("%I:%M")+went_ug_is_am if avg_went_ug_str else None,
                    avgLeaveUG= avg_went_ag_str, #.strftime("%I:%M")+went_ag_is_am if avg_went_ag_str else None,
                    avgTimeUG= avg_time_ug_str if avg_time_ug_str else None,
                    reportData= miners_data,
                    shifts= shifts if len(shift_set) > 0 else []
            )
            return personnel_report_data, None

        except Exception as e:
            logger.error(f"Error in get_personnel_report_data: {e}", exc_info=True)


    def get_personnel_report_data_v2(self, mine_id:int, miner_id:int, from_date:date,
        to_date:date) -> Tuple[Optional[ReportPersonnelSchema], Optional[APIError]]:
        """Get Personnel Report data for the specified mine ID and date."""
        try:
            location_data_manager_obj = LocationDataManager(self.session)

            mine_dict = get_mine_with_timezone(location_data_manager_obj, mine_id)
            # Mine check
            if mine_dict is None :
                logger.error("Error in get_personnel_report_data_v2: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")


            miner_check = check_miner_exists(location_data_manager_obj, miner_id)
            if not miner_check:
                logger.error(
                    f"Error in get_personnel_report_data_v2:Miner check failed:{not miner_check}"
                )
                return None, APIError(404, APIErrorType.MINER_NOT_FOUND, "Miner not found")

            # Mine's Timezone related code
            mine_timezone = mine_dict.get("tz_code") or pytz.utc
            mine_current_datetime = datetime.now(mine_timezone)
            mine_current_date = mine_current_datetime.date()
            logger.debug(f"Mine's Current_datetime:{mine_current_datetime},tz:{mine_timezone}")

            empty_response : ReportPersonnelSchema = ReportPersonnelSchema(
                    minerId = None,
                    minerName = None,
                    avgEnterUG = None,
                    avgLeaveUG = None,
                    avgTimeUG = None,
                    reportData = [],
                    shifts= []
            )

            # Future Date check using mine date
            if from_date > to_date or from_date > mine_current_datetime.date():
                logger.error("No data available for the specified dates.")
                return empty_response, None

            # Main logic
            logger.info("from %s to %s", from_date, to_date)
            miners_data = []

            total_converted_went_ug = 0
            went_ug_count = 0
            total_converted_went_ag = 0
            went_ag_count = 0
            total_time_ug_count = 0

            next_to_date = to_date + timedelta(days=1)
            from_date_ts = get_min_epoch_using_tz(from_date, mine_timezone)
            to_date_ts = get_min_epoch_using_tz(next_to_date, mine_timezone)


            logger.info("from date ts %s to date_ts %s", from_date_ts, to_date_ts)

            miner_name = None
            shift_set = set()
            windows_tz = mine_dict.get("windows_tz", "UTC")
            logger.debug("windows_tz:%s",windows_tz)

            subquery = (
                select (
                    MinerStatus.id,
                    MinerStatus.miner_id,
                    MinerStatus.went_ug,
                    MinerStatus.went_ag,
                    MinerStatus.shift_id,
                    MinerStatus.msg_timestamp,
                    MinerStatus.is_active,
                    func.row_number().over(
                        partition_by=[
                            func.convert(text('DATE'),
                                func.dateadd(
                                    text('SECOND'),
                                    MinerStatus.msg_timestamp,
                                    '1970-01-01'
                                ).op('AT TIME ZONE')('UTC').op('AT TIME ZONE')(windows_tz)
                            )
                        ],
                        order_by=[MinerStatus.msg_timestamp.desc(), MinerStatus.id.desc()]
                    ).label('rn')
                )
                .filter(MinerStatus.miner_id == miner_id)
                .filter(MinerStatus.msg_timestamp >= from_date_ts)
                .filter(MinerStatus.msg_timestamp < to_date_ts)
                .filter(MinerStatus.is_active == 1)
                .subquery()
            )

            # Define the main query
            stmt = (
                select (
                    subquery.c.id,
                    func.concat(Miner.first_name, ' ', Miner.last_name).label('miner_name'),
                    subquery.c.miner_id,
                    subquery.c.went_ug,
                    subquery.c.went_ag,
                    subquery.c.shift_id,
                    subquery.c.msg_timestamp,
                    subquery.c.is_active,
                    func.convert(
                        text('DATE'),
                        func.dateadd(
                            text('SECOND'),
                            subquery.c.msg_timestamp,
                            '1970-01-01'
                        ).op('AT TIME ZONE')('UTC').op('AT TIME ZONE')(windows_tz)
                    ).label('ms_day')
                )
                .join(Miner, subquery.c.miner_id == Miner.id)
                .filter(Miner.mine_id == mine_id)
                .filter(subquery.c.rn == 1)
                .filter(subquery.c.miner_id == miner_id)
                .filter(subquery.c.msg_timestamp >= from_date_ts)
                .filter(subquery.c.msg_timestamp < to_date_ts)
                .filter(subquery.c.is_active == 1)
                .order_by('ms_day')
            )

            # logger.info("sql %s", stmt.compile(dialect=mssql.dialect(),
            #                                    compile_kwargs={"literal_binds": True}))

            start = tm.time()
            result = self.get_execute_all(stmt)
            logger.info("query exec time %s", tm.time() - start)

            if result is None:
                return empty_response, None

            # Process the result for the current day
            for id, miner_name, _, went_ug, went_ag, shift_id, msg_ts, _, ms_day in result:
                # logger.debug(f"id = {id}, miner_name = {miner_name},
                # went_ug = {went_ug}, went_ag = {went_ag},
                # shift_id = {shift_id}, msg_ts = {msg_ts}, ms_day = {ms_day}")
                total_time_ug =timedelta_to_hh_mm(went_ug, went_ag)

                if total_time_ug is not None:
                    hours, minutes = map(int, total_time_ug.split(':'))
                    formatted_total_time_ug = f"{hours} hrs {minutes} min"
                    total_time_ug_count += 1
                else:
                    formatted_total_time_ug = None

                msg_dt = datetime.fromtimestamp(msg_ts, tz=timezone.utc)
                msg_dt = (convert_dt_to_tz(msg_dt.replace(tzinfo=None), mine_timezone)).date()

                # logger.debug(f"ms_day: {msg_ts} {msg_dt}")

                miners_data.append(
                    {
                        "date": msg_dt.strftime("%Y-%m-%d"),
                        "dayOfWeek": msg_dt.strftime("%A"),
                        "enterUG": (convert_dt_to_tz(went_ug, mine_timezone)
                                    .strftime("%I:%M %p").lower() if went_ug else None),
                        "leaveUG": (convert_dt_to_tz(went_ag, mine_timezone)
                                    .strftime("%I:%M %p").lower() if went_ag else None),
                        "totalTimeUG": formatted_total_time_ug,
                        "enterUGCC": None,
                        "leaveUGCC": None,
                        "totalTimeUGCC": None
                    }
                )

                if went_ug is not None:
                    converted_went_ug = went_ug.hour * 60 + went_ug.minute
                    total_converted_went_ug += converted_went_ug
                    went_ug_count += 1

                if went_ag is not None:
                    converted_went_ag = went_ag.hour * 60 + went_ag.minute
                    total_converted_went_ag += converted_went_ag
                    went_ag_count += 1

                if shift_id is not None:
                    shift_set.add(shift_id)

            update_CC(miners_data, 'enterUG')

            total_hours_ug = sum(int(miner['totalTimeUG'].split(' ')[0]) for miner in miners_data if miner['totalTimeUG'])
            total_minutes_ug = sum(int(miner['totalTimeUG'].split(' ')[2]) for miner in miners_data if miner['totalTimeUG'])
            total_miners = len(miners_data)

            # Extracting enterUG times, excluding None values
            went_ug_times = [entry['enterUG'] for entry in miners_data if entry['enterUG'] is not None]

            # Extracting leaveUG times, excluding None values
            went_ag_times = [entry['leaveUG'] for entry in miners_data if entry['leaveUG'] is not None]

            logger.debug(f"enter_ug_times : {went_ug_times}, leave_ug_times : {went_ag_times} ")

            if total_miners > 0:

                if total_converted_went_ug > 0 or len(went_ug_times) > 0 :
                    avg_went_ug_time_obj = get_average_time(went_ug_times)
                    avg_went_ug_time = datetime.combine(mine_current_date, avg_went_ug_time_obj)
                    avg_went_ug_str = avg_went_ug_time.strftime("%I:%M %p") if avg_went_ug_time else None
                else:
                    avg_went_ug_str= None

                if total_converted_went_ag > 0 or len(went_ag_times) > 0 :
                    avg_went_ag_time_obj = get_average_time(went_ag_times)
                    avg_went_ag_time = datetime.combine(mine_current_date, avg_went_ag_time_obj)
                    avg_went_ag_str = avg_went_ag_time.strftime("%I:%M %p") if avg_went_ag_time else None
                else:
                    avg_went_ag_str= None

                logger.debug(f"total_hours_ug : {total_hours_ug}, total_minutes_ug : {total_minutes_ug}, total_time_ug_count : {total_time_ug_count}")
                if total_hours_ug >= 0 and total_minutes_ug >= 0:
                    try:
                        avg_total_ug_minutes = (total_hours_ug * 60 + total_minutes_ug) / total_time_ug_count
                        logger.debug(f"avg_total_ug_minutes : {avg_total_ug_minutes}")
                        avg_time_ug_hours, avg_time_ug_minutes = divmod(avg_total_ug_minutes, 60)
                        avg_time_ug_str = f"{int(avg_time_ug_hours)}.{int(avg_time_ug_minutes)} hrs"
                    except Exception as e:
                        logger.debug(f"Exception in avgTimeUG calculation : {e}")
                        avg_time_ug_str = None
                else:
                    avg_time_ug_str= None

            else:
                avg_went_ug_str = None
                avg_went_ag_str = None
                avg_time_ug_str = None

            shifts = []

            if len(shift_set) != 0:
                shifts = get_shifts_by_ids(location_data_manager_obj, mine_id, shift_set)

            if miner_name is None:
                logger.error("No data available for the specified miner.")
                return empty_response, None

            personnel_report_data = ReportPersonnelSchema(
                    minerId = miner_id,
                    minerName = miner_name,
                    avgEnterUG = avg_went_ug_str,
                    avgLeaveUG = avg_went_ag_str,
                    avgTimeUG = avg_time_ug_str if avg_time_ug_str else None,
                    reportData = miners_data,
                    shifts= shifts if len(shift_set) > 0 else []
            )
            return personnel_report_data, None

        except Exception as e:
            logger.error(f"Error in get_personnel_report_data_v2: {e}", exc_info=True)

        return empty_response, None


    def get_report_checkins_data(
        self, mine_id: int, date: date
    ) -> Tuple[ReportCheckinsSchema, APIError]:
        """Get Report checkins data for the specified mine ID and date."""

        location_data_manager_obj = LocationDataManager(self.session)

        mine_dict = get_mine_with_timezone(location_data_manager_obj, mine_id)
        # Mine check
        if mine_dict is None :
            logger.error("Error in get_report_checkins_data: Mine not found.")
            return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")

        # Mine's Timezone related code
        mine_timezone = mine_dict.get("tz_code") or pytz.utc
        mine_current_datetime = datetime.now(mine_timezone)
        logger.debug(f"Mine's Current_datetime  : {mine_current_datetime}, tz : {mine_timezone}")

        # Future date check using mine date
        if date > mine_current_datetime.date():
            logger.error("Given date is a future date.")
            return {"minersData": []}, None

        report_checkins_miners_data = self.get_report_checkins_miners_data(mine_id, date, mine_dict)

        r_checkins_response = ReportCheckinsSchema(minersData=report_checkins_miners_data)
        return r_checkins_response, None


    def get_report_checkins_miners_data(self, mine_id: int, report_date: date, mine_dict:dict):
        """Function to get the checkins data of miners which returns a list"""

        try:
            mine_timezone = mine_dict.get("tz_code") or pytz.utc

            # Finding the start and end of the day using given date - USING MINE'S TZ
            start_of_day_mine_tz, end_of_day_mine_tz = get_epoch_range_using_tz(report_date, mine_timezone)

            start_of_day = start_of_day_mine_tz
            end_of_day = end_of_day_mine_tz

            latest_timestamp_subquery = generate_latest_timestamp_subquery(start_of_day, end_of_day)

            stmt = (
                select(
                    MinerStatus.id,
                    Miner.id.label("miner_id"),
                    func.concat(Miner.first_name, " ", Miner.last_name).label(
                        "miner_name"
                    ),
                    MinerStatus.check_in,
                    MinerStatus.went_ug,
                    MinerStatus.last_ug,
                    Section.ext_section_name,
                    MinerStatus.msg_timestamp
                )
                .join(Miner, Miner.id == MinerStatus.miner_id)
                .outerjoin(
                    Section, 
                    or_(
                        MinerStatus.section_id == Section.id, 
                        MinerStatus.last_section == Section.id
                    )
                )
                .join(
                    latest_timestamp_subquery,
                    and_(
                        latest_timestamp_subquery.c.miner_id == MinerStatus.miner_id,
                        latest_timestamp_subquery.c.latest_timestamp
                        == MinerStatus.msg_timestamp,
                    ),
                )
                .filter(
                    Miner.mine_id == mine_id,
                    MinerStatus.is_active == 1,
                    and_(MinerStatus.msg_timestamp >= start_of_day,
                    MinerStatus.msg_timestamp <= end_of_day)
                )
            ).order_by(desc(MinerStatus.id))
            start = tm.time()
            result = self.get_execute_all(stmt)
            logger.debug("query time %s", tm.time() - start)

            seen_ids = set()
            miners_data = []
            for (
                id,
                miner_id,
                miner_name,
                check_in,
                went_ug,
                last_ug,
                ext_section_name,
                msg_timestamp
            ) in result:
                if miner_id not in seen_ids:
                    # Check if check_in datetime's date matches the report_date
                    checked_in_time = None
                    if check_in:
                        check_in_mine_tz = convert_dt_to_tz(check_in, mine_timezone)
                        if check_in_mine_tz.date() == report_date:
                            checked_in_time = check_in_mine_tz.strftime("%I:%M %p").lower()

                    miners_data.append({
                        "minerId": miner_id,
                        "minerName": miner_name,
                        "checkedInTime": checked_in_time,
                        "firstUG": convert_dt_to_tz(went_ug, mine_timezone).strftime("%I:%M %p").lower() if went_ug else None,
                        "lastUG": convert_dt_to_tz(last_ug, mine_timezone).strftime("%I:%M %p").lower() if last_ug else None,
                        "lastSection": ext_section_name if ext_section_name else None,
                    })
                    seen_ids.add(miner_id)
            return miners_data

        except Exception as e:
            logger.error(f"Error in get_ug_breakup_for_checkins: {e}", exc_info=True)
            return []
