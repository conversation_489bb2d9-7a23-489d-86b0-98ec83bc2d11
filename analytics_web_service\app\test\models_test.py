import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
# from app.models.base import SQLModel
import datetime
from ..models.location import Mine, Miner, Section, Shift, MinerActivity
from ..models.base import SQLModel
from app_loggers.loggers_config import get_logger 

logger= get_logger(__name__)

@pytest.fixture
def db_session():
    engine = create_engine("mysql+pymysql://iwt:iwt2024@localhost:3306/iwt_db")  
    SQLModel.metadata.create_all(engine)
    Session = sessionmaker(bind=engine)
    session = Session()
    yield session
    session.close()
    

def test_mines_model(db_session):
    try:
        mines = Mine(
            id=1,
            name="mine1",
            location="5YH",
            code="AB",
            is_active=True,
            company_id=1,
            created_at=datetime.datetime.now(),
            created_by=1,
            updated_at=datetime.datetime.now(),
            updated_by=1
        )
        db_session.add(mines)
        db_session.commit()

        retrieved_mines = db_session.query(Mine).filter_by(id=mines.id).first()
        assert retrieved_mines.name == "mine1"
        assert retrieved_mines.code == "AB"
        assert retrieved_mines.is_active == True

    except Exception as e:
        logger.error(f"Error in test_mines_model: {e}", exc_info=True)
        raise


def test_miner_model(db_session):
    try:
        miner = Miner(
            mine_id=1,
            ext_miner_id=1,
            first_name="Glen",
            last_name="Maxwell",
            ext_node_id=1,
            occupation_id=1,
            is_active=True,
            created_at=datetime.datetime.now(),
            updated_at=datetime.datetime.now()
        )
        db_session.add(miner)
        db_session.commit()

        retrieved_miner = db_session.query(Miner).filter_by(id=miner.id).first()
        assert retrieved_miner.first_name == "Glen"
        assert retrieved_miner.last_name == "Maxwell"
        assert retrieved_miner.is_active == True
        
    except Exception as e:
        logger.error(f"Error in test_miner_model: {e}", exc_info=True)
        raise


def test_section_model(db_session):
    try:
        section = Section(
            mine_id=1,
            ext_section_id=1,
            ext_section_name="Section A",
            is_active=True,
            created_at=datetime.datetime.now(),
            updated_at=datetime.datetime.now()
        )
        db_session.add(section)
        db_session.commit()

        retrieved_section = db_session.query(Section).filter_by(id=section.id).first()
        assert retrieved_section.section_name == "Section A"
        assert retrieved_section.is_active == True
    
    except Exception as e:
        logger.error(f"Error in test_section_model: {e}", exc_info=True)
        raise


def test_shift_model(db_session):
    try:
        shift = Shift(
            name="Day",
            mine_id=1,
            start_time=datetime.datetime.now(),
            end_time=datetime.datetime.now(),
            is_active=True,
            created_at=datetime.datetime.now(),
            updated_at=datetime.datetime.now()
        )
        db_session.add(shift)
        db_session.commit()

        retrieved_shift = db_session.query(Shift).filter_by(id=shift.id).first()
        assert retrieved_shift.id == 1
        assert retrieved_shift.is_active == True
        
    except Exception as e:
        logger.error(f"Error in test_shift_model: {e}", exc_info=True)
        raise


def test_miner_activity_model(db_session):
    try:
        miner_activity = MinerActivity(
            miner_id=1,
            section_id=1,
            shift_id=1,
            ground_status="AG",
            comm_status=1,
            last_heard=1704120950,
            version_stamp=1704120950,
            created_at=datetime.datetime.now(),
            updated_at=datetime.datetime.now(),
            is_active=True
        )
        db_session.add(miner_activity)
        db_session.commit()

        retrieved_miner_activity = db_session.query(MinerActivity).filter_by(id=miner_activity.id).first()
        assert retrieved_miner_activity.ground_status == "AG"
        
    except Exception as e:
        logger.error(f"Error in test_miner_activity_model: {e}", exc_info=True)
        raise

