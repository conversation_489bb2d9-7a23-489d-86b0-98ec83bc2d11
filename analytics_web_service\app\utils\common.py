from app_loggers.loggers_config import get_logger
logger = get_logger(__name__)

def int_to_hex(number: int, include_prefix: bool = False) -> str:
    """
    Converts an integer to a hexadecimal string representation.
    
    Args:
        number (int): The integer to convert to hexadecimal
        include_prefix (bool, optional): Whether to include the '0x' prefix. Defaults to False .
        
    Returns:
        str: The hexadecimal representation of the input number as a string
    
    Examples:
        >>> int_to_hex(10, include_prefix=True)
        '0xa'
        >>> int_to_hex(255)
        'ff'
    """
    try:
        if not isinstance(number, int):
            logger.error(f"int_to_hex: Input must be an integer, got {type(number)}")
            return "0x0" if include_prefix else "0"
        
        hex_string = hex(number)
        
        if not include_prefix:
            hex_string = hex_string[2:]  # Remove '0x' prefix
            
        return hex_string.upper()
    except Exception as e:
        logger.error(f"Error in int_to_hex: {e}", exc_info=True)
        return "0x0" if include_prefix else "0"
    


def CelsiusToFahrenheit(celsius):
    return round ((celsius * 9/5) + 32,2)
