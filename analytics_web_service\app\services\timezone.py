from datetime import date, datetime, time
from app_loggers.loggers_config import get_logger
from models.location import Timezone
from sqlalchemy import select, func
from services.base import BaseDataManager
import pytz

logger = get_logger(__name__)


def get_timezone(data_manager: BaseDataManager, timezone_id: int) -> dict:
    """
    Retrieves a timezone record from the database by its ID.

    Args:
        data_manager (BaseDataManager): The data manager instance used to query the database.
        timezone_id (int): The ID of the timezone to retrieve. Must be a positive integer.

    Returns:
        dict: A dictionary representing the timezone record if found and active; otherwise, None.

    Notes:
        If `timezone_id` is invalid or an error occurs during the query,
        this function will log the error and return None.

    Raises:
        Exception: For any errors encountered during the database query.
    """
    try:
        if not isinstance(timezone_id, int) or timezone_id <= 0:
            logger.error("From get_timezone - Invalid timezone_id: %s", timezone_id)
            return None

        stmt = select(Timezone).where(
            Timezone.id == timezone_id,
            # Timezone.is_active == 1
        )

        timezone = data_manager.get_one(stmt)
        logger.debug("Timezone : %s", timezone)

        if timezone is None:
            return None

        # Converting the timezone object to a dictionary
        # timezone_dict = table_record_to_dict(timezone)
        timezone_dict = timezone.to_dict()
        return timezone_dict

    except Exception as e:
        logger.error(f"Error in get_timezone: {e}", exc_info=True)
        return None


def get_timezone_by_mine_dict(mine_dict: dict):
    """
    Retrieves a timezone object based on a dictionary containing timezone information.

    Args:
        mine_dict (dict): A dictionary expected to contain
        a key 'tz_code' with a timezone string value.

    Returns:
        pytz.timezone: The timezone object corresponding to the 'tz_code' if valid;
        otherwise, UTC timezone.

    Notes:
        If 'tz_code' is missing or not a string,
        or if any error occurs during timezone retrieval,
        this function will log the error and return UTC.
    """
    try:
        # Validating mine_obj contains 'tz_code' key and that it maps to a valid timezone string
        if "tz_code" not in mine_dict or not isinstance(mine_dict["tz_code"], str):
            logger.error(
                "From get_timezone_by_mine_dict - Timezone is invalid or missing"
            )
            return pytz.utc

        mine_tz = mine_dict.get("tz_code")
        logger.debug("Mine's timezone : %s", mine_tz)

        return pytz.timezone(mine_tz)

    except Exception as e:
        logger.error(
            "From get_timezone_by_mine_dict - Timezone is invalid or missing: %s",
            e,
            exc_info=True,
        )
        return pytz.utc




def convert_dt_to_tz(this_datetime: datetime, tz: pytz.BaseTzInfo):
    """
    Convert a datetime object to a specified timezone using a pytz timezone object.

    Parameters:
    this_datetime (datetime): The datetime object to be converted.
    tz (pytz.BaseTzInfo): The target timezone as a pytz timezone object.

    Returns:
    datetime: The datetime object converted to the target timezone, 
    or None if invalid input is provided.
    """
    try:
        # Checking if the provided datetime is a valid datetime object
        if not isinstance(this_datetime, datetime):
            logger.error("Invalid datetime object provided.")
            return None

        # Checking if the provided timezone is a valid pytz timezone object
        if not isinstance(tz, pytz.BaseTzInfo):
            logger.error("Invalid timezone object provided.")
            return None
        # Converting the datetime to the target timezone
        source_tz = pytz.timezone('UTC')  # source timezone
        localized_dt = source_tz.localize(this_datetime)
        converted_datetime = localized_dt.astimezone(tz)
        return converted_datetime

    except Exception as e:
        logger.error(f"Exception occurred in convert_timezone: {e}", exc_info=True)
        return None



def get_epoch_range_using_tz(current_date: date, tz: pytz.BaseTzInfo) -> tuple:
    """ Function to convert a date to a start and end epoch tuple in mine's timezone"""
    try:

        # Guard: To ensure current_date is a date instance
        if not isinstance(current_date, date):
            logger.error("From get_epoch_range_using_tz - current_date must be a datetime.date instance")
            return (0, 0)

        # Guard: To ensure tz is a valid pytz timezone instance
        if not isinstance(tz, pytz.BaseTzInfo):
            tz = pytz.utc

        # Creating naive datetime objects for the start and end of the day
        start_of_day = datetime.combine(current_date, time.min)
        end_of_day = datetime.combine(current_date, time.max)

        # Localize the naive datetime objects to the mine's tz
        start_of_day = tz.localize(start_of_day)
        end_of_day = tz.localize(end_of_day)

        # Converting the datetime objects to epoch timestamps
        start_of_day_epoch = int(start_of_day.timestamp())
        end_of_day_epoch = int(end_of_day.timestamp())

        logger.debug(f"start_of_day_epoch: {start_of_day_epoch} : {start_of_day}: {tz}")
        logger.debug(f"end_of_day_epoch: {end_of_day_epoch} : {end_of_day}: {tz}")

        return start_of_day_epoch, end_of_day_epoch
    except Exception as e:
        logger.error(f"Error in get_epoch_range_using_tz : {e}", exc_info=True)
        return (0, 0)


def get_min_epoch_using_tz(current_date: date, tz: pytz.BaseTzInfo):
    """ Function to convert a date to a start and end epoch tuple in mine's timezone"""
    try:

        # Guard: To ensure current_date is a date instance
        if not isinstance(current_date, date):
            logger.error("From get_min_epoch_using_tz - current_date must be a datetime.date instance")
            return (0, 0)

        # Guard: To ensure tz is a valid pytz timezone instance
        if not isinstance(tz, pytz.BaseTzInfo):
            tz = pytz.utc

        # Creating naive datetime objects for the start of the day
        start_of_day = datetime.combine(current_date, time.min)

        # Localize the naive datetime objects to the mine's tz
        start_of_day = tz.localize(start_of_day)

        # Converting the datetime object to epoch timestamp
        start_of_day_epoch = int(start_of_day.timestamp())

        logger.debug(f"start_of_day_epoch: {start_of_day_epoch} : {start_of_day}: {tz}")

        return start_of_day_epoch
    except Exception as e:
        logger.error(f"Error in get_min_epoch_using_tz : {e}", exc_info=True)
        return 0


def get_day_range_utc_from_mine_tz(current_date: date, mine_tz: pytz.BaseTzInfo) -> tuple:
    """
    Function to get start and end of day in UTC from mine's timezone.

    Args:
        current_date (date): The date for which to get the day range
        mine_tz (pytz.BaseTzInfo): The mine's timezone object

    Returns:
        tuple: (start_of_day_utc, end_of_day_utc) as datetime objects in UTC
    """
    try:
        # Guard: To ensure current_date is a date instance
        if not isinstance(current_date, date):
            logger.error("From get_day_range_utc_from_mine_tz - current_date must be a datetime.date instance")
            return (None, None)

        # Guard: To ensure mine_tz is a valid pytz timezone instance
        if not isinstance(mine_tz, pytz.BaseTzInfo):
            logger.warning("From get_day_range_utc_from_mine_tz - Invalid timezone, defaulting to UTC")
            mine_tz = pytz.utc

        # Creating naive datetime objects for the start and end of the day
        start_of_day = datetime.combine(current_date, time.min)
        end_of_day = datetime.combine(current_date, time.max)

        # Localize the naive datetime objects to the mine's timezone
        start_of_day = mine_tz.localize(start_of_day)
        end_of_day = mine_tz.localize(end_of_day)

        # Convert to UTC
        start_of_day_utc = start_of_day.astimezone(pytz.UTC)
        end_of_day_utc = end_of_day.astimezone(pytz.UTC)

        logger.debug(f"start_of_day_utc: {start_of_day_utc} (from mine_tz: {mine_tz})")
        logger.debug(f"end_of_day_utc: {end_of_day_utc} (from mine_tz: {mine_tz})")

        return start_of_day_utc, end_of_day_utc

    except Exception as e:
        logger.error(f"Error in get_day_range_utc_from_mine_tz: {e}", exc_info=True)
        return (None, None)