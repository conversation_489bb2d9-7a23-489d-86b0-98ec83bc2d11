import pytz
from sqlalchemy import TIME, case, cast, distinct, false, null, select, func, and_, text, true, and_, or_
from datetime import date, datetime, timedelta, timezone, time
from services.mine import get_mine_with_timezone
from schemas.location import ShiftSchema
from models.location import Mine, Miner, MinerActivity, MinerStatus, Watchlist, Shift
from app_loggers.loggers_config import get_logger 
from services.base import (
    BaseDataManager,
)
from sqlalchemy.dialects import mssql
import time as tm
logger= get_logger(__name__)

def get_shift_times(date, shift) -> tuple:

    shift_start_time = datetime.combine(date, shift["start_time"], tzinfo=timezone.utc)
    shift_end_time = datetime.combine(date, shift["end_time"], tzinfo=timezone.utc)
    
    shift_start_epoch = int(shift_start_time.timestamp())
    shift_end_epoch = int(shift_end_time.timestamp())
    
    # Returning a tuple containing the shift start and end times
    return (shift_start_epoch, shift_end_epoch)

def get_epoch_range(current_date: date):
    """ Function to convert a date to a start and end epoch tuple in UTC"""
    try:
        # Guard: To ensure current_date is a date instance
        if not isinstance(current_date, date):
            logger.error("From get_epoch_range - current_date must be a datetime.date instance")
            return (0, 0)

        # Creating datetime objects for the start and end of the day in UTC
        start_of_day = datetime.combine(current_date, time.min, tzinfo=timezone.utc)
        end_of_day = datetime.combine(current_date, time.max, tzinfo=timezone.utc)

        # Converting the datetime objects to epoch timestamps
        start_of_day_epoch = int(start_of_day.timestamp())
        end_of_day_epoch = int(end_of_day.timestamp())

        logger.debug(f"start_of_day_epoch: {start_of_day_epoch} : {start_of_day}: {start_of_day.tzinfo}")
        logger.debug(f"end_of_day_epoch: {end_of_day_epoch} : {end_of_day}: {end_of_day.tzinfo}")

        return start_of_day_epoch, end_of_day_epoch
    except Exception as e:
        logger.error(f"Error in get_epoch_range : {e}", exc_info=True)
        return (0, 0)


def apply_epoch_range_conditions(column_epoch, start_epoch, end_epoch):
    """
    Returns SQLAlchemy conditions to check if a given epoch column falls within an epoch range.

    Parameters:
    - column_epoch: The epoch column to be checked 
        (e.g., MinerActivity.last_heard , MinerStatus.msg_timestamp).
    - start_epoch: The start of the epoch range.
    - end_epoch: The end of the epoch range.

    Returns:
    - SQLAlchemy conditions for the epoch column being within the epoch range.
    """
    return and_(
        column_epoch >= start_epoch,
        column_epoch <= end_epoch
    )

def get_largest_versionstamp_from_miner_activity(data_manager: BaseDataManager, desired_table, desired_field, mine_id: int, shift_id: int) -> int:
    try:
        stmt = (
            select(func.max(getattr(desired_table, desired_field)))
            .join(Miner)
            .filter(
                Miner.mine_id == mine_id,
                MinerActivity.is_active == 1,
                MinerActivity.shift_id == shift_id
            )
        )
        latest_last_heard = data_manager.get_one(stmt)
        if latest_last_heard is None:
            raise ValueError("Failed to retrieve last version stamp.")
        return latest_last_heard

    except Exception as e:
        logger.error(f"Error in get_largest_version_stamp: {e}", exc_info=True)
        # raise e


def get_largest_versionstamp_from_miner_status(data_manager: BaseDataManager, desired_table, desire_field, mine_id: int, shift_id:int) -> int:
    try:
        stmt = (
            select(func.max(getattr(desired_table, desire_field)))
            .join(Miner)
            .filter(
                Miner.mine_id == mine_id,
                MinerStatus.is_active == 1,
                MinerStatus.shift_id == shift_id
            )
        )
        largest_version_stamp = data_manager.get_one(stmt)
        if largest_version_stamp is None:
            raise ValueError("Failed to retrieve last version stamp.")
        return largest_version_stamp

    except Exception as e:
        logger.error(f"Error in get_largest_version_stamp: {e}", exc_info=True)
        # raise e


def get_total_miners(
    data_manager: BaseDataManager, mine_id: int, shift_id:int, epoch_range: tuple
) -> int:
    try:
        start_of_day, end_of_day = epoch_range
        latest_lastheard_subquery = generate_latest_lastheard_subquery_v2( 1, start_of_day, end_of_day)
        stmt = (
            select(Miner)
            .join(MinerActivity, MinerActivity.miner_id == Miner.id)
            .join(
                latest_lastheard_subquery,
                and_(
                    latest_lastheard_subquery.c.miner_id == MinerActivity.miner_id,
                    latest_lastheard_subquery.c.latest_last_heard
                    == MinerActivity.last_heard,
                ),
            )
            .filter(
                Miner.mine_id == mine_id,
                MinerActivity.is_active == 1,
                MinerActivity.shift_id == shift_id,
                apply_epoch_range_conditions(MinerActivity.last_heard, start_of_day, end_of_day),
            )
            .distinct()
        )
       
        miners = data_manager.get_all(stmt)
        return miners

    except Exception as e:
        logger.error(f"Error in get_total_miners: {e}", exc_info=True)
        # raise e


def get_miners_by_ground_status(
    data_manager: BaseDataManager, mine_id: int, ground_status: str, shift_id:int, epoch_range: tuple
):

    try:
        start_of_day, end_of_day = epoch_range
        latest_lastheard_subquery = generate_latest_lastheard_subquery_v2( 1, start_of_day, end_of_day)

        stmt = (
            select(Miner)
            .join(MinerActivity, MinerActivity.miner_id == Miner.id)
            .join(
                latest_lastheard_subquery,
                and_(
                    latest_lastheard_subquery.c.miner_id == MinerActivity.miner_id,
                    latest_lastheard_subquery.c.latest_last_heard
                    == MinerActivity.last_heard,
                ),
            )
            .filter(
                Miner.mine_id == mine_id,
                MinerActivity.is_active == 1,
                MinerActivity.ground_status == ground_status,
                MinerActivity.shift_id == shift_id,
                apply_epoch_range_conditions(MinerActivity.last_heard, start_of_day, end_of_day),
            )
            .distinct()
        )
        miners = data_manager.get_all(stmt)
        return miners

    except Exception as e:
        logger.error(f"Error in get_miners_by_ground_status: {e}", exc_info=True)
        # raise e


def get_miners_by_comm_status(
    data_manager: BaseDataManager, mine_id: int, comm_status: int, shift_id:int, epoch_range: tuple
):
    try:
        start_of_day, end_of_day = epoch_range
        latest_lastheard_subquery = generate_latest_lastheard_subquery_v2( 1, start_of_day, end_of_day)

        stmt = (
            select(Miner.id)
            .join(MinerActivity, MinerActivity.miner_id == Miner.id)
            .join(
                latest_lastheard_subquery,
                and_(
                    latest_lastheard_subquery.c.miner_id == MinerActivity.miner_id,
                    latest_lastheard_subquery.c.latest_last_heard
                    == MinerActivity.last_heard,
                ),
            )
            .filter(
                Miner.mine_id == mine_id,
                MinerActivity.is_active == 1,
                MinerActivity.comm_status == comm_status,
                MinerActivity.ground_status == "UG",
                MinerActivity.shift_id == shift_id,
                apply_epoch_range_conditions(MinerActivity.last_heard, start_of_day, end_of_day),
            )
            .distinct()
        )
        miners = data_manager.get_all(stmt)

        return miners

    except Exception as e:
        logger.error(f"Error in get_miners_by_comm_status: {e}", exc_info=True)
        # raise e


def get_miner_stats(
    data_manager: BaseDataManager, mine_id: int,  epoch_range: tuple
):
    try:
        start_of_day, end_of_day = epoch_range
        latest_lastheard_subquery = generate_latest_lastheard_subquery_v2(1, start_of_day, end_of_day)
        
        stmt = (
            select(
                func.count(Miner.id).label("total_miners"),
                func.coalesce(
                    func.sum(case((MinerActivity.ground_status == "AG", 1), else_=0)), 0
                ).label("total_ag_miners"),
                func.coalesce(
                    func.sum(case((MinerActivity.ground_status == "UG", 1), else_=0)), 0
                ).label("total_ug_miners"),
                func.coalesce(
                    func.sum(case((and_(MinerActivity.ground_status == "UG",MinerActivity.comm_status == 0,),1,),else_=0,)),0,
                ).label("total_offline_miners"),
            )
            .select_from(Miner)
            .join(MinerActivity, MinerActivity.miner_id == Miner.id)
            .join(
                latest_lastheard_subquery,
                and_(
                    latest_lastheard_subquery.c.miner_id == MinerActivity.miner_id,
                    latest_lastheard_subquery.c.latest_last_heard == MinerActivity.last_heard,
                ),
            )
            .filter(
                Miner.mine_id == mine_id,
                MinerActivity.is_active == 1,               
                apply_epoch_range_conditions(MinerActivity.last_heard, start_of_day, end_of_day),
            )
        )

        # start = tm.time()
        results = data_manager.get_execute_one(stmt)
        # logger.debug("get_miner_stats query time %s", tm.time() - start)
        # sql_query = stmt.compile(dialect=mssql.dialect(), compile_kwargs={"literal_binds": True})
        # logger.info("SQL Query: %s", sql_query)
        if results is None:
            return {
                "total_miners": 0,
                "total_ag_miners": 0,
                "total_ug_miners": 0,
                "total_offline_miners": 0,
            }
        return {
            "total_miners": results.get("total_miners", 0),
            "total_ag_miners": results.get("total_ag_miners", 0),
            "total_ug_miners": results.get("total_ug_miners", 0),
            "total_offline_miners": results.get("total_offline_miners", 0),
        }

    except Exception as e:
        logger.error(f"Error in get_miner_stats: {e}", exc_info=True)
        return {
                "total_miners": 0,
                "total_ag_miners": 0,
                "total_ug_miners": 0,
                "total_offline_miners": 0,
            }

def get_utc_isoformat(timestamp: int) -> str:
    """Get the timestamp in UTC ISO 8601 format."""
    dt_obj = datetime.fromtimestamp(timestamp, tz=timezone.utc)
    utc_isoformat = dt_obj.isoformat(timespec='milliseconds')
    return utc_isoformat


def check_if_miner_watched(data_manager: BaseDataManager, miner_id: int, user_id: int) -> bool:
    try:
        stmt = (
            select(func.count())
            .where(and_(Watchlist.miner_id == miner_id, Watchlist.user_id == user_id))
        )
        result = data_manager.get_one(stmt)

        if result == 1:
            return True  # Miner is watched by the user
        else:
            return False  # Miner is not watched by the user

    except Exception as e:
        logger.error(f"Error while checking if miner is watched: {e}",exc_info=True)
        return False  # Return False if an error occurs


def get_watchlist_id(data_manager: BaseDataManager, miner_id: int, user_id: int) -> int:
    try:
        stmt = (
            select(Watchlist.id)
            .where(and_(Watchlist.miner_id == miner_id, Watchlist.user_id == user_id))
        )
        watchlist_id = data_manager.get_one(stmt)

        return watchlist_id

    except Exception as e:
        logger.error(f"Error get_watchlist_id(): {e}",exc_info=True)
        return None


def get_user_watchlist(data_manager: BaseDataManager, user_id: int) -> list[Watchlist]:
    try:
        stmt = (
            select(Watchlist.id, Watchlist.user_id, Watchlist.miner_id)
            .where(Watchlist.user_id == user_id)
        )
        watchlist= data_manager.get_execute_all(stmt)

        return watchlist

    except Exception as e:
        logger.error(f"Error in get_user_watchlist: {e}",exc_info=True)
        return None


def update_CC(miners_list: list, field: str) -> list:
    try:
        if not miners_list:
            return []
        
        # If there's only one miner in the list, assign white color to all attributes
        if len(miners_list)==1:
            return set_default_white_color(miners_list)
        
        if field == "arriveTime":
            if miners_list[-1][field] is None:
                miners_list[-1][f"{field}CC"] ='W'
                # return update_next_field(miners_list, 'leftTime')
            # if miners_list[-1][field] is None:
            #     for miner in miners_list:
            #         miner[f"{field}CC"] = 'W'
            #     return update_next_field(miners_list, 'leftTime')
            
            max_value, min_value = get_min_and_max_value(miners_list,field)
            updated_arrive_cc_list = calculate_arrive_cc(miners_list, field, max_value, min_value)
            return update_next_field(updated_arrive_cc_list, 'leftTime')

        elif field == "leftTime":
            if miners_list[-1][field] is None:
                miners_list[-1][f"{field}CC"] ='W'
                # return update_next_field(miners_list, 'travelTo')
            # if miners_list[-1][field] is None:
            #     for miner in miners_list:
            #         miner[f"{field}CC"] = 'W'
            #     return update_next_field(miners_list, 'travelTo')
            
            max_value, min_value = get_min_and_max_value(miners_list,field)
            updated_left_cc_list = calculate_left_cc(miners_list, field, max_value, min_value)   
            return update_next_field(updated_left_cc_list, 'travelTo')

        elif field == "travelTo":
            if miners_list[-1][field] is None:
                miners_list[-1][f"{field}CC"] ='W'
                # return update_next_field(miners_list, 'travelFrom')
            # if miners_list[-1][field] is None:
            #     for miner in miners_list:
            #         miner[f"{field}CC"] = 'W'
            #     return update_next_field(miners_list, 'travelFrom')
            
            max_value, min_value = get_min_and_max_value(miners_list,field)
            updated_travel_to_cc_list = calculate_travel_to_cc(miners_list, field, max_value, min_value)     
            return update_next_field(updated_travel_to_cc_list, 'travelFrom')

        elif field == "travelFrom":
            if miners_list[-1][field] is None:
                miners_list[-1][f"{field}CC"] = 'W'
                # return update_next_field(miners_list, 'onSection')
            # if miners_list[-1][field] is None:
            #     for miner in miners_list:
            #         miner[f"{field}CC"] = 'W'
            #     return update_next_field(miners_list, 'onSection')
            
            max_value, min_value = get_min_and_max_value(miners_list,field)
            updated_travel_from_cc_list = calculate_travel_from_cc(miners_list, field, max_value, min_value)
            return update_next_field(updated_travel_from_cc_list, 'onSection')

        elif field == "onSection":
            if miners_list[-1][field] is None:
                miners_list[-1][f"{field}CC"] ='W'
            # if miners_list[-1][field] is None:
            #     for miner in miners_list:
            #         miner[f"{field}CC"] = 'W'
            #     return miners_list
            
            max_value, min_value = get_min_and_max_value(miners_list,field)
            updated_on_section_cc_list = calculate_on_section_cc(miners_list, field, max_value, min_value)
            return updated_on_section_cc_list
        
        elif field == "ug":
            if miners_list[-1][field] is None:
                miners_list[-1][f"{field}CC"] ='W'
            # if miners_list[-1][field] is None:
            #     for miner in miners_list:
            #         miner[f"{field}CC"] = 'W'
            #     return update_next_field(miners_list, 'ugHrs')
            
            max_value, min_value = get_min_and_max_value(miners_list,field)
            updated_ug_cc_list = calculate_ug_cc(miners_list, field, max_value, min_value)
            return update_next_field(updated_ug_cc_list, 'ugHrs')

        elif field == "ugHrs":
            if miners_list[-1][field] is None:
                miners_list[-1][f"{field}CC"] ='W'
            # if miners_list[-1][field] is None:
            #     for miner in miners_list:
            #         miner[f"{field}CC"] = 'W'
            #     return update_next_field(miners_list, 'onSection')
            
            max_value, min_value = get_min_and_max_value(miners_list,field)
            updated_ughrs_cc_list = calculate_ugHrs_cc(miners_list, field, max_value, min_value)
            return update_next_field(updated_ughrs_cc_list, 'onSection')
        
        elif field == "enterUG":
            if miners_list[-1][field] is None:
                miners_list[-1][f"{field}CC"] ='W'
                # return update_next_field(miners_list, 'leaveUG')
            
            max_value, min_value = get_min_and_max_value(miners_list,field)
            updated_list = calculate_enterUgCC(miners_list, field, max_value, min_value)
            return update_next_field(updated_list, 'leaveUG')
        
        elif field == "leaveUG":
            if miners_list[-1][field] is None:
                miners_list[-1][f"{field}CC"] ='W'
                # for miner in miners_list:
                #     miner[f"{field}CC"] = "W"
                # return update_next_field(miners_list, 'totalTimeUG')
            
            max_value, min_value = get_min_and_max_value(miners_list,field)
            updated_list = calculate_leaveUgCC(miners_list, field, max_value, min_value)
            return update_next_field(updated_list, 'totalTimeUG')
        
        elif field == "totalTimeUG":
            if miners_list[-1][field] is None:
                miners_list[-1][f"{field}CC"] ='W'
                # return update_next_field(miners_list, 'totalTimeUG')
                
            max_value, min_value = get_min_and_max_value(miners_list,field)
            updated_list = calculate_totalUgCC(miners_list, field, max_value, min_value)
            return updated_list

    except Exception as e:
        logger.error(f"Failed to Update Color Code: {e}", exc_info=True)

def get_time_in_minutes(time_str):
    """Converts a string of format 'X hrs Y min' to total minutes."""
    parts = time_str.split()
    hours = int(parts[0]) if 'hrs' in time_str else 0
    minutes = int(parts[2]) if 'min' in time_str else 0
    return hours * 60 + minutes

def get_min_and_max_value(miners_list: list, field: str) -> str:
    time_formats = ["%I:%M %p", "%H:%M"]
    max_value = None
    min_value = None
    
    for fmt in time_formats:
        try:
            # Filtering out entries that are None or empty for the specified field    
            filtered_list = [x for x in miners_list if x[field] is not None and x[field] != '']
            if not filtered_list:
                logger.debug("From get_min_and_max_value: Filtered list is empty.")
                return None, None
            field_list = ["ugHrs", "onSection", "travelTo", "travelFrom"]
            if field in field_list:
                max_value = max(filtered_list, key=lambda x: x[field])[field]
                min_value = min(filtered_list, key=lambda x: x[field])[field]
            elif field == "totalTimeUG":
                max_value = max(filtered_list, key=lambda x: get_time_in_minutes(x[field]))[field]
                min_value = min(filtered_list, key=lambda x: get_time_in_minutes(x[field]))[field]
            else: 
                max_value = max(filtered_list, key=lambda x: datetime.strptime(x[field], fmt))[field]
                min_value = min(filtered_list, key=lambda x: datetime.strptime(x[field], fmt))[field]
            break  # Stop the loop if successful parsing
        
        except Exception as e:
            logger.error(f"Error in get_min_and_max_value: {e}", field, exc_info=True)
            continue  # Try next format if parsing fails
    return max_value, min_value


def update_next_field(miners_list: list, next_field: str) -> list:
    return update_CC(miners_list, next_field)


def set_default_white_color(miners_list):
   
    miner = miners_list[-1]
    for key in miner.keys():
        if 'CC' in key:
            miner[key] = 'W'
    return miners_list


def calculate_arrive_cc(miners_list: list,field: str, max_value: str, min_value: str) -> list:
    
    if max_value is None and min_value is None:
        for miner in miners_list:
            miner[f"{field}CC"] = "W"
        return miners_list
    
    for miner in miners_list:
        if miner[field] == max_value and min_value != max_value:
            miner[f"{field}CC"] = "R"
        elif miner[field] == min_value and min_value != max_value:
            miner[f"{field}CC"] = "G"
        else:
            miner[f"{field}CC"] = "W"

    return miners_list


def calculate_left_cc(miners_list: list, field: str, max_value: str, min_value: str) -> list:
    
    if max_value is None and min_value is None:
        for miner in miners_list:
            miner[f"{field}CC"] = "W"
        return miners_list
           
    for miner in miners_list:
        if miner[field] == max_value and min_value != max_value:
            miner[f"{field}CC"] = "G"
        elif miner[field] == min_value and min_value != max_value:
            miner[f"{field}CC"] = "R"
        else:
            miner[f"{field}CC"] = "W"

    return miners_list


def calculate_travel_to_cc(miners_list: list, field: str, max_value: str, min_value: str) -> list:

    if max_value is None and min_value is None:
        
        for miner in miners_list:
            miner[f"{field}CC"] = "W"
        return miners_list

    for miner in miners_list:
        if miner[field] == max_value and min_value != max_value:
            miner[f"{field}CC"] = "R"
        elif miner[field] == min_value and min_value != max_value:
            miner[f"{field}CC"] = "G"
        else:
            miner[f"{field}CC"] = "W"

    return miners_list


def calculate_travel_from_cc(miners_list: list, field: str, max_value: str, min_value: str) -> list:

    if max_value is None and min_value is None:
        for miner in miners_list:
            miner[f"{field}CC"] = "W"
        return miners_list

    for miner in miners_list:
        if miner[field] == max_value and min_value != max_value:
            miner[f"{field}CC"] = "R"
        elif miner[field] == min_value and min_value != max_value:
            miner[f"{field}CC"] = "G"
        else:
            miner[f"{field}CC"] = "W"

    return miners_list


def calculate_on_section_cc(miners_list: list, field: str, max_value: str, min_value: str) -> list:

    if max_value is None and min_value is None:
        for miner in miners_list:
            miner[f"{field}CC"] = "W"
        return miners_list

    for miner in miners_list:
        if miner[field] == max_value and min_value != max_value:
            miner[f"{field}CC"] = "G"
        elif miner[field] == min_value and min_value != max_value:
            miner[f"{field}CC"] = "R"
        else:
            miner[f"{field}CC"] = "W"

    return miners_list


def calculate_ug_cc(miners_list: list,field: str, max_value: str, min_value: str) -> list:

    if max_value is None and min_value is None:
        for miner in miners_list:
            miner[f"{field}CC"] = "W"
        return miners_list

    for miner in miners_list:
        if miner[field] == max_value and min_value != max_value:
            miner[f"{field}CC"] = "R"
        elif miner[field] == min_value and min_value != max_value:
            miner[f"{field}CC"] = "G"
        else:
            miner[f"{field}CC"] = "W"

    return miners_list


def calculate_ugHrs_cc(miners_list: list,field: str, max_value: str, min_value: str) -> list:

    if max_value is None and min_value is None:
        for miner in miners_list:
            miner[f"{field}CC"] = "W"
        return miners_list

    for miner in miners_list:
        if miner[field] == max_value and min_value != max_value:
            miner[f"{field}CC"] = "G"
        elif miner[field] == min_value and min_value != max_value:
            miner[f"{field}CC"] = "R"
        else:
            miner[f"{field}CC"] = "W"

    return miners_list

def calculate_enterUgCC(miners_list: list,field: str, max_value: str, min_value: str) -> list:

    if max_value is None and min_value is None:
        for miner in miners_list:
            miner[f"{field}CC"] = "W"
        return miners_list

    for miner in miners_list:
        if miner[field] == max_value and min_value != max_value:
            miner[f"{field}CC"] = "R"
        elif miner[field] == min_value and min_value != max_value:
            miner[f"{field}CC"] = "G"
        else:
            miner[f"{field}CC"] = "W"

    return miners_list

def calculate_leaveUgCC(miners_list: list,field: str, max_value: str, min_value: str) -> list:

    if max_value is None and min_value is None:
        for miner in miners_list:
            miner[f"{field}CC"] = "W"
        return miners_list

    for miner in miners_list:
        if miner[field] == max_value and min_value != max_value:
            miner[f"{field}CC"] = "G"
        elif miner[field] == min_value and min_value != max_value:
            miner[f"{field}CC"] = "R"
        else:
            miner[f"{field}CC"] = "W"

    return miners_list

def calculate_totalUgCC(miners_list: list,field: str, max_value: str, min_value: str) -> list:

    if max_value is None and min_value is None:
        for miner in miners_list:
            miner[f"{field}CC"] = "W"
        return miners_list
    
    for miner in miners_list:
        if miner[field] == max_value and min_value != max_value:
            miner[f"{field}CC"] = "G"
        elif miner[field] == min_value and min_value != max_value:
            miner[f"{field}CC"] = "R"
        else:
            miner[f"{field}CC"] = "W"

    return miners_list

def timedelta_to_hh_mm(end_time,start_time):
    # Handling the case where either end_time or start_time is None
    if end_time is None or start_time is None or end_time == start_time:
        return None

    duration = abs(end_time - start_time)   
    hours = duration.seconds // 3600
    minutes = (duration.seconds // 60) - (hours * 60)    
    # Format hours and minutes into hh:mm format
    return '{:02}:{:02}'.format(int(hours), int(minutes))


mines = [
    {
    "id": 1,
    "name": "mine1",
    "location": "New York",
    "code": "NY001",
    "is_active": true,
    "company_id": 100,
    "created_at": "2024-03-31T08:15:00",
    "created_by": 101,
    "updated_at": "2024-03-31T08:15:00",
    "updated_by": 101
    }
]

# def check_mine_exists(self, mine_id: int) -> bool:
#     """Check if a mine with the given ID exists."""
#     try :
#         stmt = (select(func.count(Miner.mine_id)).where(Miner.mine_id == mine_id))
#         mine_count = self.get_one(stmt)
#         return mine_count > 0

#     except Exception as e:
#         logger.error(f"Error in check_mine_exists: {e}",exc_info=True)

def check_mine_exists(self,mine_id: int) -> bool:
    """Check if a mine with the given ID exists."""
    try:
        # # Check if mine id exists in mines list
        # if any(mine["id"] == mine_id for mine in mines):
        #     # If mine id exists in mines list, proceed to check in Miner table
        #     stmt = select(func.count(Miner.mine_id)).where(Miner.mine_id == mine_id)
        #     mine_count = self.get_one(stmt)
        #     return mine_count > 0
        # else:
        #     # If mine id does not exist in mines list, return False
        #     return False

        stmt = select(func.count(Mine.id)).where(Mine.id == mine_id, Mine.is_active == 1)
        mine_count = self.get_one(stmt)
        return mine_count > 0

    except Exception as e:
        logger.error(f"Error in check_mine_exists: {e}", exc_info=True)


def check_miner_exists(self, miner_id: int) -> bool:
    """Check if a miner with the given ID exists."""
    try:
        stmt = select(func.count(Miner.id)).where(Miner.id == miner_id, Miner.is_active == 1)
        miner_count = self.get_one(stmt)
        return miner_count > 0
    except Exception as e:
        logger.error(f"Error in check_miner_exists: {e}", exc_info=True)


# shifts = [
#     {
#         "id": 1,
#         "mine_id": 1,
#         "start": time(00, 00, 00),
#         "end": time(12, 00, 00)
#     },
#     {
#         "id": 2,
#         "mine_id": 1,
#         "start": time(12, 00, 00),
#         "end": time(23, 59, 59)
#     }
# ]

# def get_current_shift(mine_id:int, current_timestamp:datetime):
#     """Returns current shift  for a mine"""
#     current_time = current_timestamp.time()
#     for shift in shifts:
#         if shift.get('mine_id') == mine_id:
#             if shift.get("start") <= current_time <= shift.get("end"):
#                 return shift
#     return shift


def shift_time_conditions(current_time):
    """ Returns the SQLAlchemy conditions for the current time being within a shift period."""
    return or_(
        and_(
            Shift.start_time <= Shift.end_time,
            current_time >= Shift.start_time,
            current_time < Shift.end_time
        ),
        and_(
            Shift.start_time > Shift.end_time,
            or_(
                current_time >= Shift.start_time,
                current_time < Shift.end_time
            )
        )
    )

def get_current_shift(data_manager: BaseDataManager, mine_id: int, current_timestamp: datetime) -> dict:
    """Returns current shift for a mine as a dictionary"""
    current_time = current_timestamp.time()
    # Select query to fetch shifts for the given mine_id and current timestamp
    query = (
        select(Shift)
        .where(
            and_(
                Shift.mine_id == mine_id,
                Shift.is_active == 1,
                shift_time_conditions(current_time),
            )
        )
        .order_by(Shift.start_time)
    )
    # Executeing the query
    model = data_manager.get_one(query)
    if model:
        shift_dict = dict(model.to_dict())
        return shift_dict
    else:
        return None


latest_timestamp_subquery = (
    select(
        MinerStatus.miner_id,
        func.max(MinerStatus.msg_timestamp).label("latest_timestamp"),
    )
    .group_by(MinerStatus.miner_id)
    .alias("latest_timestamp_subquery")
)


def generate_latest_timestamp_subquery(start_of_day_utc, end_of_day_utc):
    """
    Function to create the latest timestamp subquery based on the specified date range.
    """    
    subquery = (
        select(
            MinerStatus.miner_id,
            func.max(MinerStatus.msg_timestamp).label("latest_timestamp"),
        )
        .where(
            and_(
                MinerStatus.msg_timestamp >= start_of_day_utc,
                MinerStatus.msg_timestamp <= end_of_day_utc
            ),
            MinerStatus.is_active == 1,

        )
        .group_by(MinerStatus.miner_id)
        .alias("latest_timestamp_subquery")
    )

    return subquery





def generate_latest_timestamp_subquery_v2( is_active, start_of_day_utc, end_of_day_utc):
    """
    Function to create the latest timestamp subquery based on the specified date range.
    """    
    subquery = (
        select(
            MinerStatus.miner_id,
            func.max(MinerStatus.msg_timestamp).label("latest_timestamp"),
        )
        .where(
            and_(
                MinerStatus.msg_timestamp >= start_of_day_utc,
                MinerStatus.msg_timestamp <= end_of_day_utc
            ),
            MinerStatus.is_active == is_active
            
        )
        .group_by(MinerStatus.miner_id)
        .alias("latest_timestamp_subquery")
    )

    return subquery


def generate_latest_lastheard_subquery():
    """
    Function to get the latest last_heard subquery
    """
    subquery = (
        select(
            MinerActivity.miner_id,
            func.max(MinerActivity.last_heard).label("latest_last_heard"),
        )
        .group_by(MinerActivity.miner_id)
        .alias("latest_lastheard_subquery")
    )

    return subquery

def generate_latest_lastheard_subquery_v2( is_active, start_of_day_utc, end_of_day_utc):
    """
    Function to get the latest last_heard subquery
    """
    subquery = (
        select(
            MinerActivity.miner_id,
            func.max(MinerActivity.last_heard).label("latest_last_heard"),
        )
        .where(
            and_(
                MinerActivity.last_heard >= start_of_day_utc,
                MinerActivity.last_heard <= end_of_day_utc
            ),
            MinerActivity.is_active == is_active,            
        )
        .group_by(MinerActivity.miner_id)
        .alias("latest_lastheard_subquery")
    )

    return subquery


def minutes_to_datetime(minutes):
    return datetime.min + timedelta(minutes=minutes)


def get_empty_schema(apiName: str, formatted_current_datetime=None):
    if apiName == "live/dashboard":
        return {
            "lastUpdatedTs": formatted_current_datetime,
            "totalMiners": 0,
            "totalAG": 0,
            "totalUG": 0,
            "totalOffline": 0,
            "ugBreakup": [],
        }, None

    if apiName == "live/checkins":
        return {
            "lastUpdatedTs": formatted_current_datetime,
            "totalAG": 0,
            "totalUG": 0,
            "totalOffline": 0,
            "agBreakup": [],
            "ugBreakup": [],
        }, None

    if apiName == "live/sections":
        return {"lastUpdatedTs": formatted_current_datetime, "sectionBreakup": []}, None

    if apiName == "live/watchlist":
        return {"lastUpdatedTs": formatted_current_datetime, "watchlist": []}, None

    if apiName == "reports/personnel":
        return {
            "minerId": None,
            "minerName": None,
            "avgEnterUG": None,
            "avgLeaveUG": None,
            "avgTimeUG": None,
            "reportData": [],
            "shifts": [],
        }, None

def get_shifts_by_ids(data_manager: BaseDataManager, mine_id: int, shift_ids: set[int]) -> list[dict]:
    """Returns a list of shift dictionaries with startTime and endTime for given shift IDs within a specific mine"""
    shifts_list = []

    if not shift_ids:
        return shifts_list

    query = select(Shift).where(
        and_(
            Shift.id.in_(shift_ids),
            Shift.mine_id == mine_id,
        )
    )

    # Execute the query
    shifts = data_manager.get_all(query)

    for shift in shifts:
        shift_dict = {
            "shiftId": shift.id,
            "startTime": shift.start_time.strftime("%H:%M:%S"),
            "endTime": shift.end_time.strftime("%H:%M:%S")
        }
        shifts_list.append(shift_dict)

    return shifts_list


def get_average_time(time_strings: list) -> datetime.time:
    """
    Calculate the average time from a list of time strings.

    Args:
        time_strings (list): A non-empty list of time strings in the format "%I:%M %p".

    Returns:
        datetime.time: The average time in hours and minutes.
    """
    # Checking if the input is a non-empty list
    if not isinstance(time_strings, list) or not time_strings:
        logger.error(f"From average_time - Input must be a non-empty list of time strings.")
        return None

    try:
        # Converting time strings to datetime objects
        times = [datetime.strptime(time, "%I:%M %p") for time in time_strings]

        # Calculating total seconds for each time
        total_seconds = sum(time.hour * 3600 + time.minute * 60 for time in times)

        # Calculating average time in seconds
        average_seconds = total_seconds / len(times)

        # Converting average seconds back to hours and minutes
        average_hour = int(average_seconds // 3600)
        average_minute = int((average_seconds % 3600) // 60)

        # Returning a time object for the average time
        return datetime.strptime(f"{average_hour}:{average_minute}", "%H:%M").time()

    except Exception as e:
        logger.error(f"Error in average_time: {e}")
        return None

