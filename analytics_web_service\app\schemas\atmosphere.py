from pydantic import BaseModel
from datetime import datetime,date
from typing import Optional, List, Dict


class SensorModuleStateSchema(BaseModel):
    id: int
    value: int
    description: str
    display_name: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool


class SensorCategorySchema(BaseModel):
    id: int
    name: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool

class DeviceTypesSchema(BaseModel):
    id: int
    name: str
    value:int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool


class SensorModuleTypeSchema(BaseModel):
    id: int
    value: int
    description: str
    unit: str
    category_id: int
    display_name: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool


class SensorDeviceSchema(BaseModel):
    id: int
    nid: int
    label: str
    mine_id: int
    section_id: int
    ground_status: int
    comm_status: int
    last_heard: int
    battery_status: int
    lh_datetime: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool


class SensorRawDataSchema(BaseModel):
    id: int
    sensor_device_id: int
    timestamp: int
    index: int
    module_type_id: int
    value: float
    state: int
    module_fault: str
    category_id: int
    ts_day: int
    ts_month: int
    ts_year: int
    ts_week: int
    ts_datetime: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool


class CompensatedAtmosphericDataSchema(BaseModel):
    id: int
    sensor_device_id: int
    timestamp: int
    elevation: float
    absolute_pressure: float
    compensated_pressure: float
    category_id: int
    ts_day: int
    ts_month: int
    ts_year: int
    ts_week: int
    ts_datetime: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool


class CalculatedAtmosphericDataSchema(BaseModel):
    id: int
    sensor_device_id: int
    timestamp: int
    aperture_size: float
    diff_pressure: float
    air_velocity: float
    air_quantity: float
    category_id: int
    ts_day: int
    ts_month: int
    ts_year: int
    ts_week: int
    ts_datetime: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool


class SensorDataAggrDailySchema(BaseModel):
    id: int
    sensor_device_id: int
    module_type_id: int
    mine_date: datetime
    utc_date: datetime
    min_value: float
    max_value: float
    avg_value: float
    category_id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool


class SensorDataAggrHourlySchema(BaseModel):
    id: int
    sensor_device_id: int
    module_type_id: int
    mine_date: datetime
    utc_date: datetime
    hour: int
    min_value: float
    max_value: float
    avg_value: float
    category_id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool


# Response schemas for API endpoints
class SensorDeviceResponse(BaseModel):
    id: int
    nid: int
    label: str
    mine_id: int
    section_id: int
    ground_status: int
    comm_status: int
    last_heard: int
    battery_status: int
    lh_datetime: str
    is_active: bool


class SensorReadingResponse(BaseModel):
    timestamp: int
    value: float
    module_type: str
    unit: str
    ts_datetime: str


class SensorReadingsResponse(BaseModel):
    device: SensorDeviceResponse
    readings: List[SensorReadingResponse]


class AtmosphericDataOverviewSchema(BaseModel):
    lastUpdatedTs: str
    totalSensors: int
    activeSensors: int
    inactiveSensors: int
    sensorsByState: Dict[str, int]    


class SensorDataPointSchema(BaseModel):
    timestamp: str
    value: float

class StateDataSchema(BaseModel):
    moduleStateId: int
    moduleStateName: str
    count: int

class SensorTimeSeriesSchema(BaseModel):
    sensorId: int
    sensorLabel: str
    status: int
    moduleType: str
    unit: str
    timestamp: str
    value: float  
    isCompliance: bool
    moduleStateId:int
    sectionName:str
    nid:str
    categoryId:int



class SensorTimeSeriesResponseSchema(BaseModel):
    lastUpdatedTs: str
    timeSeriesData: List[SensorTimeSeriesSchema]
    stateData: List[StateDataSchema]


class SensorAlertSchema(BaseModel):
    sensorId: int
    sensorLabel: str
    moduleType: str
    alertType: str
    value: float
    threshold: float
    timestamp: str
    status: str


class SensorAlertsResponseSchema(BaseModel):
    lastUpdatedTs: str
    alerts: List[SensorAlertSchema]