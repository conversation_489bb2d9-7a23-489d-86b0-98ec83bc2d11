# import json
# from operator import itemgetter
from typing import Tuple
from itertools import zip_longest

import pytz
from sqlalchemy import case, literal_column, desc, select, func, and_, TIME, or_, cast, Date

from datetime import datetime, time, timedelta, timezone, date

from services.timezone import convert_dt_to_tz
from services.mine import get_mine_with_timezone
from utils.production_utils import (
    extract_shift_hours,
    generate_trendline_cuts_data,
    generate_trendline_cuts_data_v2,
    get_best_day_and_feet_mined,
    get_cuts_line_data_for_graph,
    # get_cuts_downevents_summary,
    # get_downtime_in_single_day,
    get_empty_schema_prod,
    get_end_date_time,
    get_feet_mined_by_interval,
    # get_feet_mined_in_day,
    get_last_down_time_by_interval,
    get_last_saved_or_submitted_ts,
    get_mine_goal_from_portal,
    get_no_of_cuts_by_interval,
    get_on_section_by_interval,
    get_raw_shifts,
    get_section_coordinates,
    # get_section_graph_points,
    get_sections_from_cuts,
    get_shifts_in_day_from_shifts,
    get_shifts_in_particular_day,
    # get_start_date_time,
    get_total_downtime_by_interval,
    get_total_hours_by_ts,
    hours_between_intervals,
    hours_since_given_date,
    renumber_ids,
    working_hours_in_a_day,
    get_shifts_include_maintenance,
    check_data_present_in_shift
)
from utils.activity_utils import get_activity_metrics,calculate_shift_goal,get_mine_goal_from_mine

from backend.api_errors import APIErrorType, APIError
from models.location import Miner, MinerActivity, Section, MinerStatus, Watchlist
from models.production import Cut
from utils.production_utils import (get_feet_mined_in_day, get_downtime_in_single_day,hours_since_given_date, get_section_wise_feetmined,
                                    get_section_graph_points,get_sections,get_report_shift_metrics,get_most_downtime )
from schemas.production import OverviewSchema, ReportMineSchema,ReportMineCompareSchema,ReportShiftSchema, LiveSectionsSchema
# , ReportPersonnelSchema
from services.base import (
    BaseDataManager,
    BaseService,
)
from services.location import LocationDataManager
from app_loggers.loggers_config import get_logger
from utils.locations_utils import (
    check_miner_exists,
    generate_latest_timestamp_subquery,
    get_current_shift,
    get_largest_versionstamp_from_miner_activity,
    get_largest_versionstamp_from_miner_status,
    get_shift_times,
    get_utc_isoformat,
    get_miners_by_ground_status,
    get_total_miners,
    get_miners_by_comm_status,
    check_if_miner_watched,
    get_watchlist_id,
    timedelta_to_hh_mm,
    update_CC,
    check_mine_exists,
    latest_timestamp_subquery,
    minutes_to_datetime,
    get_empty_schema,
)

from const import SECONDS_IN_HOUR ,MINUTES_IN_HOUR ,SHIFT_TYPE_PRODUCTION

logger = get_logger(__name__)


class ProductionService(BaseService):
    """Class for production API service"""

    def get_overview_data(self, mine_id: int) -> Tuple[OverviewSchema, APIError]:
        """Get Overview Data."""

        return ProductionDataManager(self.session).get_overview_data(mine_id)

    def get_live_sections_data(
        self, mine_id: int, section_id: int
    ) -> Tuple[LiveSectionsSchema, APIError]:
        """Get Overview Data."""

        return ProductionDataManager(self.session).get_live_sections_data(
            mine_id, section_id
        )


    def get_report_mine_data(self, mine_id,from_date, to_date) -> Tuple[ReportMineSchema, APIError]:
        """Get Mine Report Data."""

        return ProductionDataManager(self.session).get_report_mine_data(mine_id,from_date, to_date)

    def get_compare_report_mine_data(self, mine_id,from_date1, to_date1, from_date2,to_date2) -> Tuple[ReportMineSchema, APIError]:
        """Get Mine Report Data."""

        return ProductionDataManager(self.session).get_compare_report_mine_data(mine_id,from_date1, to_date1, from_date2,to_date2)
    
    def get_report_shift_data(self,mine_id,date) -> Tuple[ReportMineSchema, APIError]:
        """Get Mine Report Data."""

        return ProductionDataManager(self.session).get_report_shift_data(mine_id,date)

class ProductionDataManager(BaseDataManager):
    """Class for production APIs"""

    # productions/live/overview
    def get_overview_data(self, mine_id: int) -> Tuple[OverviewSchema, APIError]:
        """Get overview data for the specified mine ID."""

        production_data_manager_obj = ProductionDataManager(self.session)

        mine_dict = get_mine_with_timezone(production_data_manager_obj, mine_id)
        if mine_dict is None :
            logger.error("Error in get_overview_data: Mine not found.")
            return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")

        # Mine's Timezone related code
        mine_timezone = mine_dict.get("tz_code") or pytz.utc
        mine_current_datetime = datetime.now(mine_timezone)
        current_date = mine_current_datetime.date()
        formatted_mine_current_datetime = mine_current_datetime.isoformat(timespec='milliseconds')
        logger.debug(f"Mine's Current_datetime  : {mine_current_datetime}, tz : {mine_timezone}")

        start_time = mine_timezone.localize(datetime.combine(current_date, time.min))
        tz_code = mine_dict.get("tz_code_str")
        tz_abbreviation = mine_current_datetime.strftime('%Z')


        last_saved_or_submitted_ts = get_last_saved_or_submitted_ts(
            production_data_manager_obj, mine_id
            # current_date
        )

        if last_saved_or_submitted_ts is None:
            logger.error("There are no cuts and downevents records in the database.")
            # return get_empty_schema_prod(
            #     "live/overview", formatted_mine_current_datetime, None
            # )

        last_saved_or_submitted_ts = (
            (convert_dt_to_tz(last_saved_or_submitted_ts, mine_timezone)).replace(
                tzinfo=None
            )
            if last_saved_or_submitted_ts
            else None
        )
        logger.info("last_saved_or_submitted_ts: %s", last_saved_or_submitted_ts)

        sections = (
            get_sections_from_cuts(
                production_data_manager_obj, mine_id, start_time, mine_current_datetime
            )
            or []
        )
        # logger.debug("sections: %s\n", sections)

        # if len(sections) == 0 or sections is None:
        #     logger.error("Sections Records not found for this day.")
        #     return get_empty_schema_prod(
        #         "live/sections", formatted_mine_current_datetime, last_saved_or_submitted_ts)

        live_shifts = get_shifts_in_particular_day(
            production_data_manager_obj, mine_id, start_time, mine_current_datetime
        )

        shifts = live_shifts
        # logger.debug("shifts:\n %s \n ", "\n".join(map(str, shifts)))

        # getting all processed shifts irrespective of their type
        all_shifts_in_day = get_shifts_in_day_from_shifts(
            get_raw_shifts(production_data_manager_obj, mine_id),
            for_graph=True
        )

        production_shifts = [
            shift
            for shift in all_shifts_in_day
            if shift["shiftType"] == SHIFT_TYPE_PRODUCTION
        ]

        # logger.debug("all_shifts_in_day:\n %s \n ", "\n".join(map(str, all_shifts_in_day)))
        # if len(shifts) == 0 or shifts is None:
        #     logger.error("Shift Records not found for this day.")
        #     return get_empty_schema_prod(
        #         "live/overview", formatted_mine_current_datetime, last_saved_or_submitted_ts)


        total_hours= 0

        section_list = []
        shift_list = []
        section_object, shift_object  = get_sections(production_data_manager_obj,mine_id)

        for section in section_object:
            section_id = section.get("sectionId")
            last_saved_or_submitted_ts_from_section = get_last_saved_or_submitted_ts(
                production_data_manager_obj,
                mine_id,
                section.get("sectionId"),
                current_date
            )
            last_saved_or_submitted_ts_from_section = (
                (convert_dt_to_tz(last_saved_or_submitted_ts_from_section, mine_timezone)).replace(
                    tzinfo=None
                )
                if last_saved_or_submitted_ts_from_section
                else None
            )

            section_hours = get_total_hours_by_ts(
                production_data_manager_obj, mine_id, last_saved_or_submitted_ts_from_section
            )
            logger.debug("section_hours:%s, last_saved_or_submitted_ts_from_section:%s",
                        section_hours, last_saved_or_submitted_ts_from_section)
            if section_hours:
                total_hours += section_hours

            section_list.append(section_id)

        for shift in shift_object:
            shift_id = shift.get("shiftId")
            shift_list.append(shift_id)

        logger.debug("total_hours:%s", total_hours)

        # feet_mined_graph_points = []
        total_feet_mined = 0
        total_downtime = 0
        feet_mined_per_hour = 0.0

        total_feet_mined = get_feet_mined_by_interval(
            production_data_manager_obj, mine_id, start_time, mine_current_datetime
        )
        # logger.info("total_feet_mined from overview: %s\n", total_feet_mined)
        total_downtime = (
            get_total_downtime_by_interval(
                production_data_manager_obj, mine_id, start_time, mine_current_datetime
            )
            / 60
        )

        last_down_time = get_last_down_time_by_interval(
            production_data_manager_obj, mine_id, start_time, mine_current_datetime
        )

        if last_down_time is not None:
            last_downtime_start_time = last_down_time.strftime("%I:%M %p")
            last_downtime_started, last_downtime_started_postfix = (
                last_downtime_start_time.split()
            )
            last_downtime_started_postfix = last_downtime_started_postfix.lower()
        else:
            last_downtime_started = None
            last_downtime_started_postfix = None

        if total_feet_mined is None or total_feet_mined == 0:
            feet_mined_per_hour = None
        else:
            try:
                feet_mined_per_hour = total_feet_mined / total_hours
            except Exception as e:
                logger.debug(f"Exception while calculating feet_mined_per_hour : {e}")
                feet_mined_per_hour = None

        # cuts_graph_data
        cuts_graph_data = {}

        yesterday_start_time = (
           mine_timezone.localize(datetime.combine(current_date, time.min))
            ) - timedelta(days=1)
        yesterday_end_time = mine_timezone.localize(datetime.combine(yesterday_start_time.date(), time.max))

        # logger.debug(f"yesterday_start_time :{yesterday_start_time}, yesterday_end_time: {yesterday_end_time}")

        yesterday_shifts = get_shifts_in_particular_day(
            production_data_manager_obj,
            mine_id,
            yesterday_start_time,
            yesterday_end_time,
        )
        yesterday_cuts_data = get_cuts_line_data_for_graph(
            production_data_manager_obj,
            mine_id,
            yesterday_start_time,
            yesterday_end_time,
            yesterday_shifts
        ) or {"coordinates": []}

        # logger.debug("yesterday_cuts_data :-----\n%s\n", yesterday_cuts_data)

        shift_hours_for_current_day = extract_shift_hours(shifts)
        shift_hours_for_yesterday = extract_shift_hours(yesterday_shifts)

        # logger.debug("shift_hours_for_current_day: %s", shift_hours_for_current_day)
        # logger.debug("shift_hours_for_yesterday: %s", shift_hours_for_yesterday)

        cuts_graph_x_axis_points = shift_hours_for_current_day

        if cuts_graph_x_axis_points and shift_hours_for_yesterday:
            # Checking if the first element of shift_hours_for_yesterday
            # is less than or equal to the first element of cuts_graph_x_axis_points
            if shift_hours_for_yesterday[0] <= cuts_graph_x_axis_points[0]:
                cuts_graph_x_axis_points = shift_hours_for_yesterday

            if len(shifts)<len(yesterday_shifts):

                # Comparing the start times of the first elements of shifts and yesterday_shifts
                if shifts[0]["startTime"] < yesterday_shifts[0]["startTime"]:
                    # Adding yesterday_shifts to shifts
                    shifts = renumber_ids(shifts + yesterday_shifts)
                    # print("Yesterday shift added to shifts list variable", shifts)
                else:
                    # Assigning yesterday_shifts to shifts
                    shifts = yesterday_shifts
                    # print("Yesterday shifts assigned to shifts list variable1", shifts)

            else:
                if shifts and yesterday_shifts:
                    # Checking if shifts has more than one element and yesterday_shifts has exactly one
                    # and if the start time of the first element
                    # of shifts is greater than that of yesterday_shifts
                    if (
                        shifts[0]["startTime"] > yesterday_shifts[0]["startTime"]
                    ):
                        # Adding yesterday_shifts to the beginning of shifts
                        shifts = renumber_ids(yesterday_shifts + shifts)
                        # print("Yesterday shift added to shifts list variable1", shifts)
        else:
            # Checking if cuts_graph_x_axis_points is empty
            if not cuts_graph_x_axis_points:
                cuts_graph_x_axis_points = shift_hours_for_yesterday
            # print("Shifts comparison:",shifts , yesterday_shifts)
            if len(shifts)<len(yesterday_shifts):

                if shifts and yesterday_shifts:
                    # Comparing the start times of the first elements of shifts and yesterday_shifts
                    if shifts[0]["startTime"] < yesterday_shifts[0]["startTime"]:
                        # Adding yesterday_shifts to shifts
                        shifts = renumber_ids(shifts + yesterday_shifts)
                        # print("Yesterday shift added to shifts list variable", shifts)
                else:
                    # Assigning yesterday_shifts to shifts
                    shifts = yesterday_shifts
                    # print("Yesterday shifts assigned to shifts list variable2", shifts)
            else:
                if shifts and yesterday_shifts:
                    # Checking if shifts has more than one element and yesterday_shifts has exactly one
                    # and if the start time of the first element
                    # of shifts is greater than that of yesterday_shifts
                    if (
                        shifts[0]["startTime"] > yesterday_shifts[0]["startTime"]
                    ):
                        # Adding yesterday_shifts to the beginning of shifts
                        shifts = renumber_ids(yesterday_shifts + shifts)
                        # print("Yesterday shift added to shifts list variable2", shifts)

        best_day, feet_mined = get_best_day_and_feet_mined(
            production_data_manager_obj, mine_id, current_date, mine_current_datetime
        ) or [None, 0]

        best_day_cuts_data = []

        if best_day is not None:
            # print("\n","best_day, feet_mined :", best_day, feet_mined)
            best_day_start_time = mine_timezone.localize(datetime.combine(best_day, time.min))
            best_day_end_time = mine_timezone.localize(datetime.combine(best_day, time.max))
            # logger.debug(f"Bestday start and end :-----{best_day_start_time},{best_day_end_time}")

            if best_day == current_date:
                best_day_end_time = mine_current_datetime

            shift_hours_for_best_day = []
            bestday_shifts = get_shifts_in_particular_day(
                production_data_manager_obj,
                mine_id,
                best_day_start_time,
                best_day_end_time,
            )
            shift_hours_for_best_day = extract_shift_hours(bestday_shifts)
            # print("shift_hours_for_best_day",shift_hours_for_best_day)

            if cuts_graph_x_axis_points and shift_hours_for_best_day:
                # Checking if the first element of shift_hours_for_best_day
                # is less than or equal to the first element of cuts_graph_x_axis_points
                if shift_hours_for_best_day[0] <= cuts_graph_x_axis_points[0]:
                    cuts_graph_x_axis_points = shift_hours_for_best_day

                if len(shifts)<len(bestday_shifts):

                    # Comparing the start times of the first elements of shifts and bestday_shifts
                    if shifts[0]["startTime"] < bestday_shifts[0]["startTime"]:
                        # Adding bestday_shifts to shifts
                        shifts = renumber_ids(shifts + bestday_shifts)
                        # print("bestday_shifts added to shifts list variable", shifts)
                    else:
                        # Assigning bestday_shifts to shifts
                        shifts = bestday_shifts
                        # print("bestday_shifts assigned to shifts list variable (1)", shifts)

                else:
                    if shifts and bestday_shifts:
                        # Checking if shifts has more than one element and bestday_shifts has
                        # exactly one and if the start time of the first element of
                        # shifts is greater than that of bestday_shifts
                        if (
                            shifts[0]["startTime"] > bestday_shifts[0]["startTime"]
                        ):
                            # Adding bestday_shifts to the beginning of shifts
                            shifts = renumber_ids(bestday_shifts + shifts)
                            # print("bestday shift added to shifts list variable1", shifts)
            else:
                # Checking if cuts_graph_x_axis_points is empty
                if not cuts_graph_x_axis_points:
                    cuts_graph_x_axis_points = shift_hours_for_best_day
                # print("Shifts comparison:", shifts, bestday_shifts)
                if len(shifts)<len(bestday_shifts):

                    if shifts and bestday_shifts:
                        # Comparing the start times of the first elements 
                        # of shifts and bestday_shifts
                        if shifts[0]["startTime"] < bestday_shifts[0]["startTime"]:
                            # Adding bestday_shifts to shifts
                            shifts = renumber_ids(shifts + bestday_shifts)
                            # print("bestday_shifts added to shifts list variable", shifts)
                    else:
                        # Assigning bestday_shifts to shifts
                        shifts = bestday_shifts
                        # print("bestday_shifts assigned to shifts list variable (2)", shifts)

                else:
                    if shifts and bestday_shifts:
                        # Checking if shifts has more than one element and
                        # bestday_shifts has exactly one and if the start time of the
                        # first element of shifts is greater than that of bestday_shifts
                        if (
                            shifts[0]["startTime"] > bestday_shifts[0]["startTime"]
                        ):
                            # Adding bestday_shifts to the beginning of shifts
                            shifts = renumber_ids(bestday_shifts + shifts)
                            # print("Bestday shift added to shifts list variable2", shifts)

                if len(shifts)<len(bestday_shifts):

                    if shifts and bestday_shifts:
                        # Comparing the start times of the first elements 
                        # of shifts and bestday_shifts
                        if shifts[0]["startTime"] < bestday_shifts[0]["startTime"]:
                            # Adding bestday_shifts to shifts
                            shifts = renumber_ids(shifts + bestday_shifts)
                            # print("bestday_shifts added to shifts list variable", shifts)
                    else:
                        # Assigning bestday_shifts to shifts
                        shifts = bestday_shifts
                        # print("bestday_shifts assigned to shifts list variable (2)", shifts)

                else:
                    # Checking if shifts has more than one element and
                    # bestday_shifts has exactly one and if the start time of the
                    # first element of shifts is greater than that of bestday_shifts
                    if (
                        shifts[0]["startTime"] > bestday_shifts[0]["startTime"]
                    ):
                        # Adding bestday_shifts to the beginning of shifts
                        shifts = renumber_ids(bestday_shifts + shifts)
                        # print("Bestday shift added to shifts list variable2", shifts)

            best_day_cuts_data = get_cuts_line_data_for_graph(
                production_data_manager_obj,
                mine_id,
                best_day_start_time,
                best_day_end_time,
                bestday_shifts
            ) or {"coordinates": []}

            # logger.debug("bestDayCutsData :-----\n%s\n", best_day_cuts_data)

        else:
            best_day_cuts_data = {"coordinates": []}


        x_axis_points = cuts_graph_x_axis_points or []

        #activity Calculation
        activity_object = get_activity_metrics(production_data_manager_obj, mine_id,current_date,current_date,section_list,set(shift_list),total_feet_mined)
        inspection_downtime = {"feets":activity_object.get("inspection_downtime",0)}
        planned_downtime = {"feets":activity_object.get("planned_downtime",0)}
        unplanned_downtime = {"feets":activity_object.get("unplanned_downtime",0)}
        # on_time = {"feets":activity_object.get("on_time",0)}
        total_goal = activity_object.get("total_goal",0)
        sections, shifts  = get_sections(production_data_manager_obj,mine_id)
        working_hours = working_hours_in_a_day(shifts) * len(section_list)

        goal_fmph = 0
        if working_hours is not None and working_hours != 0:
            goal_fmph = total_goal/working_hours
        expected_feet_mined = goal_fmph * total_hours
        on_time_feet = total_feet_mined - expected_feet_mined
        on_time = {"feets":round(on_time_feet,2)}



        # logger.info(f"activity object : {activity_object}")

        activity_metrics = {
            "lastReport": last_saved_or_submitted_ts,
            "tzCode": tz_code,
            "tzAbbreviation": tz_abbreviation,
            "inspectionDowntime": inspection_downtime,
            "plannedDowntime": planned_downtime,
            "unplannedDowntime": unplanned_downtime,
            "onTime": on_time,
        }

        mine_goal = total_goal


        live_cuts_data = get_cuts_line_data_for_graph(
            production_data_manager_obj, mine_id, start_time, mine_current_datetime, live_shifts
        ) or {"coordinates": []}


        # logger.debug("liveCutsData :-----\n%s\n", live_cuts_data)

        trendline_cuts_data=  {"coordinates": []}
        if production_shifts:
            trendline_cuts_data = generate_trendline_cuts_data_v2(
                feet_mined_per_hour=feet_mined_per_hour,
                shifts=production_shifts,
                mine_goal=mine_goal
            ) or {"coordinates": []}

        cuts_graph_data = {
            "xAxisPoints": list(range(25)), # x_axis_points,
            "mineGoal": mine_goal,
            "trendlineCutsData": trendline_cuts_data,
            "liveCutsData": live_cuts_data,
            "bestDayCutsData": best_day_cuts_data,
            "yesterdayCutsData": yesterday_cuts_data,
        }

        # section_wise_graph_data
        section_wise_graph_data = {}

        sections_graph_x_axis_points = [
            section.get("sectionName")
            for section in sections
            if section.get("sectionName")
        ]
        # print("sections_graph_x_axis_points", sections_graph_x_axis_points, "\n")

        section_wise_graph_data["xAxisPoints"] = sections_graph_x_axis_points

        section_coordinates = get_section_coordinates(
            production_data_manager_obj,
            mine_id,
            mine_current_datetime,
            sections,
            live_shifts,
            all_shifts_in_day
        )

        section_wise_graph_data["coordinates"] = section_coordinates

        # logger.debug("section_coordinates :-----%s", section_coordinates)


        overview_response = OverviewSchema(
            lastUpdatedTs=formatted_mine_current_datetime,
            lastSavedOrSubmittedTs=last_saved_or_submitted_ts,
            tzCode= tz_code,
            tzAbbreviation= tz_abbreviation,
            mined=format(total_feet_mined, ".2f"),
            minedUnit="ft",
            feetMinedPerHour=(
                format(feet_mined_per_hour, ".2f") if feet_mined_per_hour else None
            ),
            feetMinedPerHourUnit="ft",
            totalDowntime=total_downtime,
            totalDowntimeUnit="mins",
            lastDowntimeStarted=last_downtime_started,
            lastDowntimeStartedPostfix=last_downtime_started_postfix,
            shifts=all_shifts_in_day,
            cutsGraphData=cuts_graph_data,
            sectionwiseGraphData=section_wise_graph_data,
            averageGoal = total_goal,
            activityMetrics=activity_metrics  # need to handle
        )
        return overview_response, None

    def get_live_sections_data(
        self, mine_id: int, section_id: int
    ) -> Tuple[LiveSectionsSchema, APIError]:
        """Get live sections data for the specified mine ID."""

        production_data_manager_obj = ProductionDataManager(self.session)

        mine_dict = get_mine_with_timezone(production_data_manager_obj, mine_id)
        if mine_dict is None :
            logger.error("Error in get_live_sections_data: Mine not found.")
            return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")

        # Mine's Timezone related code
        mine_timezone = mine_dict.get("tz_code") or pytz.utc
        mine_current_datetime = datetime.now(mine_timezone)
        formatted_mine_current_datetime= mine_current_datetime.isoformat(timespec='milliseconds')
        logger.debug(f"Mine's Current_datetime  : {mine_current_datetime}, tz : {mine_timezone}")

        tz_code = mine_dict.get("tz_code_str")
        tz_abbreviation = mine_current_datetime.strftime('%Z')

        current_date = mine_current_datetime.date()
        start_time = mine_timezone.localize(datetime.combine(current_date, time.min))

        last_saved_or_submitted_ts = get_last_saved_or_submitted_ts(
            production_data_manager_obj, mine_id,
        )

        if last_saved_or_submitted_ts is None:
            logger.error("There are no cuts and downevents records in the database.")
            # return get_empty_schema_prod(
            #     "live/sections", formatted_mine_current_datetime, None
            # )

        last_saved_or_submitted_ts = (
            (convert_dt_to_tz(last_saved_or_submitted_ts, mine_timezone)).replace(
                tzinfo=None
            )
            if last_saved_or_submitted_ts
            else None
        )

        logger.info("last_saved_or_submitted_ts: %s", last_saved_or_submitted_ts)

        section_object, shift_object  = get_sections(production_data_manager_obj,mine_id)
        sections = section_object
        # (
        #     get_sections_from_cuts(
        #         production_data_manager_obj, mine_id, start_time, current_datetime
        #     )
        #     or []
        # )
        logger.debug("sections:\n%s\n", "\n".join(map(str, sections)))

        if len(sections) == 0 or sections is None:
            logger.error("Sections Records not found for this day.")
            # return get_empty_schema_prod(
            #     "live/sections", formatted_mine_current_datetime, last_saved_or_submitted_ts
            # )


        max_shifts =[]
         # getting all processed shifts irrespective of their type
        all_shifts_in_day = get_shifts_in_day_from_shifts(
            get_raw_shifts(production_data_manager_obj, mine_id),
            for_graph=True
        )

        production_shifts = [
            shift
            for shift in all_shifts_in_day
            if shift["shiftType"] == SHIFT_TYPE_PRODUCTION
        ]

        # logger.debug("all_shifts_in_day:\n %s \n ", "\n".join(map(str, all_shifts_in_day)))

        # if len(shifts) == 0 or shifts is None:
        #     logger.error("Shift Records not found for this day.")
        #     return get_empty_schema_prod(
        #         "live/overview", formatted_mine_current_datetime, last_saved_or_submitted_ts)

        sections_data = []

        # total_hours = hours_since_given_date(current_date)     
        # total_hours = hours_between_intervals(start_time, current_datetime)

        if section_id is not None:
            sections = [
                section for section in sections if section["sectionId"] == section_id
            ]

        for section in sections:
            # logger.info(
            #     "------------------------------------------------------------------------------"
            # )
            # logger.info("Section :-{} and {}".format(section["sectionId"], 
            #             section["sectionName"]))
            # logger.info(
            #     "------------------------------------------------------------------------------\n"
            # )

            live_shifts = get_shifts_in_particular_day(
                production_data_manager_obj, mine_id, start_time, mine_current_datetime
            )

            shifts = live_shifts
            # print("shifts", shifts, len(shifts), "\n")

            # feet_mined_graph_points = []
            total_feet_mined = 0
            total_downtime = 0
            feet_mined_per_hour = 0.0

            total_feet_mined = get_feet_mined_by_interval(
                production_data_manager_obj,
                mine_id,
                start_time,
                mine_current_datetime,
                section.get("sectionId"),
            )

            total_downtime = (
                get_total_downtime_by_interval(
                    production_data_manager_obj,
                    mine_id,
                    start_time,
                    mine_current_datetime,
                    section.get("sectionId"),
                )
                / 60
            )

            last_down_time = get_last_down_time_by_interval(
                production_data_manager_obj,
                mine_id,
                start_time,
                mine_current_datetime,
                section.get("sectionId"),
            )

            if last_down_time is not None:
                last_downtime_start_time = last_down_time.strftime("%I:%M %p")
                last_downtime_started, last_downtime_started_postfix = (
                    last_downtime_start_time.split()
                )
                last_downtime_started_postfix = last_downtime_started_postfix.lower()
            else:
                last_downtime_started = None
                last_downtime_started_postfix = None

            last_saved_or_submitted_ts_from_section = get_last_saved_or_submitted_ts(
                production_data_manager_obj,
                mine_id,
                section.get("sectionId"),
                current_date
            )

            last_saved_or_submitted_ts_from_section = (
                (convert_dt_to_tz(last_saved_or_submitted_ts_from_section, mine_timezone)).replace(
                    tzinfo=None
                )
                if last_saved_or_submitted_ts_from_section
                else None
            )

            total_hours = get_total_hours_by_ts(
                production_data_manager_obj, mine_id, last_saved_or_submitted_ts_from_section
            )
            logger.info("total_hours:%s, last_saved_or_submitted_ts_from_section:%s",
                        total_hours, last_saved_or_submitted_ts_from_section)

            if total_feet_mined is None or total_feet_mined == 0:
                feet_mined_per_hour = None
            else:
                try:
                    feet_mined_per_hour = total_feet_mined / total_hours
                except Exception as e:
                    logger.debug(f"Exception while calculating feet_mined_per_hour : {e}")
                    feet_mined_per_hour = None

            # cuts_graph_data
            cuts_graph_data = {}

            yesterday_start_time = (
                mine_timezone.localize(datetime.combine(current_date, time.min))
            ) - timedelta(days=1)
            yesterday_end_time = mine_timezone.localize(datetime.combine(yesterday_start_time.date(), time.max))

            # logger.debug(f"yesterday_start_time :{yesterday_start_time}, yesterday_end_time: {yesterday_end_time}")

            yesterday_shifts = get_shifts_in_particular_day(
                production_data_manager_obj,
                mine_id,
                yesterday_start_time,
                yesterday_end_time,
            )
            yesterday_cuts_data = get_cuts_line_data_for_graph(
                production_data_manager_obj,
                mine_id,
                yesterday_start_time,
                yesterday_end_time,
                yesterday_shifts,
                section.get("sectionId"),
            ) or {"coordinates": []}

            shift_hours_for_current_day = extract_shift_hours(shifts)
            shift_hours_for_yesterday = extract_shift_hours(yesterday_shifts)
            # print("Shifts and yesterday_shifts :- ", shifts, yesterday_shifts)
            cuts_graph_x_axis_points = shift_hours_for_current_day

            # print("hours_comparison" ,shift_hours_for_current_day, shift_hours_for_yesterday)

            if cuts_graph_x_axis_points and shift_hours_for_yesterday:
                # Checking if the first element of shift_hours_for_yesterday
                # is less than or equal to the first element of cuts_graph_x_axis_points
                if shift_hours_for_yesterday[0] <= cuts_graph_x_axis_points[0]:
                    cuts_graph_x_axis_points = shift_hours_for_yesterday

                if len(shifts)<len(yesterday_shifts):

                    # Comparing the start times of the first elements of shifts and yesterday_shifts
                    if shifts[0]["startTime"] < yesterday_shifts[0]["startTime"]:
                        # Adding yesterday_shifts to shifts
                        shifts = renumber_ids(shifts + yesterday_shifts)
                        # print("Yesterday shift added to shifts list variable", shifts)
                    else:
                        # Assigning yesterday_shifts to shifts
                        shifts = yesterday_shifts
                        # print("Yesterday shifts assigned to shifts list variable1", shifts)

                else:
                    if shifts and yesterday_shifts:
                        # Checking if shifts has more than one element and yesterday_shifts has exactly one
                        # and if the start time of the first element
                        # of shifts is greater than that of yesterday_shifts
                        if (
                            shifts[0]["startTime"] > yesterday_shifts[0]["startTime"]
                        ):
                            # Adding yesterday_shifts to the beginning of shifts
                            shifts = renumber_ids(yesterday_shifts + shifts)
                            # print("Yesterday shift added to shifts list variable1", shifts)
            else:
                # Checking if cuts_graph_x_axis_points is empty
                if not cuts_graph_x_axis_points:
                    cuts_graph_x_axis_points = shift_hours_for_yesterday
                # print("Shifts comparison:",shifts , yesterday_shifts)
                if len(shifts)<len(yesterday_shifts):

                    if shifts and yesterday_shifts:
                        # Comparing the start times of the first elements of shifts and yesterday_shifts
                        if shifts[0]["startTime"] < yesterday_shifts[0]["startTime"]:
                            # Adding yesterday_shifts to shifts
                            shifts = renumber_ids(shifts + yesterday_shifts)
                            # print("Yesterday shift added to shifts list variable", shifts)
                    else:
                        # Assigning yesterday_shifts to shifts
                        shifts = yesterday_shifts
                        # print("Yesterday shifts assigned to shifts list variable2", shifts)
                else:
                    if shifts and yesterday_shifts:
                        # Checking if shifts has more than one element and yesterday_shifts has exactly one
                        # and if the start time of the first element
                        # of shifts is greater than that of yesterday_shifts
                        if (
                            shifts[0]["startTime"] > yesterday_shifts[0]["startTime"]
                        ):
                            # Adding yesterday_shifts to the beginning of shifts
                            shifts = renumber_ids(yesterday_shifts + shifts)
                            # print("Yesterday shift added to shifts list variable2", shifts)

            # logger.debug("yesterday_cuts_data :-----\n%s\n", yesterday_cuts_data)

            best_day, feet_mined = get_best_day_and_feet_mined(
                production_data_manager_obj,
                mine_id,
                current_date,
                mine_current_datetime,
                section.get("sectionId"),
            ) or [None, 0]

            best_day_cuts_data = []

            if best_day is not None:
                # logger.info("best_day, feet_mined: %s, %s", best_day, feet_mined)
                best_day_start_time = mine_timezone.localize(datetime.combine(best_day, time.min))
                best_day_end_time = mine_timezone.localize(datetime.combine(best_day, time.max))

                # logger.debug(f"Bestday start and end :-----{best_day_start_time},{best_day_end_time}")


                if best_day == current_date:
                    best_day_end_time = mine_current_datetime

                shift_hours_for_best_day = []
                bestday_shifts = get_shifts_in_particular_day(
                    production_data_manager_obj,
                    mine_id,
                    best_day_start_time,
                    best_day_end_time,
                )
                # print("bestday_shifts :- ",bestday_shifts)
                shift_hours_for_best_day = extract_shift_hours(bestday_shifts)
                # print("shift_hours_for_best_day",shift_hours_for_best_day)

                if cuts_graph_x_axis_points and shift_hours_for_best_day:
                    # Checking if the first element of shift_hours_for_best_day
                    # is less than or equal to the first element of cuts_graph_x_axis_points
                    if shift_hours_for_best_day[0] <= cuts_graph_x_axis_points[0]:
                        cuts_graph_x_axis_points = shift_hours_for_best_day

                    if len(shifts)<len(bestday_shifts):

                        # Comparing the start times of the first elements of shifts and bestday_shifts
                        if shifts[0]["startTime"] < bestday_shifts[0]["startTime"]:
                            # Adding bestday_shifts to shifts
                            shifts = renumber_ids(shifts + bestday_shifts)
                            # print("bestday_shifts added to shifts list variable", shifts)
                        else:
                            # Assigning bestday_shifts to shifts
                            shifts = bestday_shifts
                            # print("bestday_shifts assigned to shifts list variable (1)", shifts)

                    else:
                        if shifts and bestday_shifts:
                            # Checking if shifts has more than one element and bestday_shifts has
                            # exactly one and if the start time of the first element of
                            # shifts is greater than that of bestday_shifts
                            if (
                                shifts[0]["startTime"] > bestday_shifts[0]["startTime"]
                            ):
                                # Adding bestday_shifts to the beginning of shifts
                                shifts = renumber_ids(bestday_shifts + shifts)
                                # print("bestday shift added to shifts list variable1", shifts)
                else:
                    # Checking if cuts_graph_x_axis_points is empty
                    if not cuts_graph_x_axis_points:
                        cuts_graph_x_axis_points = shift_hours_for_best_day
                    # print("bestday_shifts issue:", shifts, bestday_shifts)
                    if len(shifts)<len(bestday_shifts):

                        if shifts and bestday_shifts:
                            # Comparing the start times of the first elements
                            # of shifts and bestday_shifts
                            if shifts[0]["startTime"] < bestday_shifts[0]["startTime"]:
                                # Adding bestday_shifts to shifts
                                shifts = renumber_ids(shifts + bestday_shifts)
                                # print("bestday_shifts added to shifts list variable", shifts)
                        else:
                            # Assigning bestday_shifts to shifts
                            shifts = bestday_shifts
                            # print("bestday_shifts assigned to shifts list variable (2)", shifts)

                    else:
                        if shifts and bestday_shifts:
                            # Checking if shifts has more than one element and
                            # bestday_shifts has exactly one and if the start time of the
                            # first element of shifts is greater than that of bestday_shifts
                            if (
                                shifts[0]["startTime"] > bestday_shifts[0]["startTime"]
                            ):
                                # Adding bestday_shifts to the beginning of shifts
                                shifts = renumber_ids(bestday_shifts + shifts)
                                # print("Bestday shift added to shifts list variable2", shifts)

                best_day_cuts_data = get_cuts_line_data_for_graph(
                    production_data_manager_obj,
                    mine_id,
                    best_day_start_time,
                    best_day_end_time,
                    bestday_shifts,
                    section.get("sectionId"),
                ) or {"coordinates": []}

                # logger.debug("bestDayCutsData :-----\n%s\n", best_day_cuts_data)

            else:
                cuts_graph_data["bestDayCutsData"] = {"coordinates": []}

            x_axis_points = cuts_graph_x_axis_points or []
            mine_goal = get_mine_goal_from_mine(
                production_data_manager_obj,
                mine_id,
                section.get("sectionId")
            )

            live_cuts_data = get_cuts_line_data_for_graph(
                production_data_manager_obj,
                mine_id,
                start_time,
                mine_current_datetime,
                live_shifts,
                section.get("sectionId"),
            ) or {"coordinates": []}

            trendline_cuts_data=  {"coordinates": []}
            if production_shifts:
                trendline_cuts_data = generate_trendline_cuts_data_v2(
                    feet_mined_per_hour=feet_mined_per_hour,
                    shifts=production_shifts,
                    mine_goal=mine_goal
                ) or {"coordinates": []}

            # logger.debug("liveCutsData :-----\n%s\n", live_cuts_data)

            cuts_graph_data = {
                "xAxisPoints": list(range(25)), # x_axis_points,
                "mineGoal": mine_goal,
                "trendlineCutsData": trendline_cuts_data,
                "liveCutsData": live_cuts_data,
                "bestDayCutsData": best_day_cuts_data,
                "yesterdayCutsData": yesterday_cuts_data,
            }

            on_section = get_on_section_by_interval(
                production_data_manager_obj,
                mine_id,
                start_time,
                mine_current_datetime,
                section.get("sectionId"),
            )

            no_of_cuts = get_no_of_cuts_by_interval(
                production_data_manager_obj,
                mine_id,
                start_time,
                mine_current_datetime,
                section.get("sectionId"),
            )
            max_shifts = shifts
            section_data = {
                "sectionId": section.get("sectionId"),
                "mined": format(total_feet_mined, ".2f"),
                "minedUnit": "ft",
                "feetMinedPerHour": (
                    format(feet_mined_per_hour, ".2f") if feet_mined_per_hour else None
                ),
                "feetMinedPerHourUnit": "ft",
                "totalDowntime": total_downtime,
                "totalDowntimeUnit": "mins",
                "lastDowntimeStarted": last_downtime_started,
                "lastDowntimeStartedPostfix": (
                    last_downtime_started_postfix.lower()
                    if last_downtime_started_postfix
                    else None
                ),
                "cutsGraphData": cuts_graph_data,
                "onSection": on_section,
                "cuts": no_of_cuts,
            }
            sections_data.append(section_data)

        live_sections_response = LiveSectionsSchema(
            lastUpdatedTs=formatted_mine_current_datetime,
            lastSavedOrSubmittedTs=last_saved_or_submitted_ts,
            tzCode= tz_code,
            tzAbbreviation= tz_abbreviation,
            shifts=all_shifts_in_day,
            sections=sections,
            sectionsData=sections_data,
        )
        return live_sections_response, None

    # def get_report_mine_data(self, mine_id,from_date, to_date) -> tuple[ReportMineSchema, APIError]:
    #     """_summary_ : Returns details regarding mine data in given time range

    #     Args:
    #         mine_id (_int_): mine id 
    #         from_date (_datetime.date_): Starting date
    #         to_date (_datetime.datetime_): To date

    #     Returns:
    #         _tuple_: returns ReportMineSchema 
    #     """
    #     try:
    #         productionDataManager_obj = ProductionDataManager(self.session)

            # mine_dict = get_mine_with_timezone(productionDataManager_obj, mine_id)
            # logger.info(f"mine_id: {mine_id}")
            # if mine_dict is None :
            #     logger.error("Error in get_report_mine_data: Mine not found.")
            #     return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")

    #         # Mine's Timezone related code
    #         mine_timezone = mine_dict.get("tz_code") or pytz.utc
    #         mine_current_datetime = datetime.now(mine_timezone)
    #         logger.debug(f"Mine's Current_datetime  : {mine_current_datetime}, tz : {mine_timezone}")

    #         tz_code = mine_dict.get("tz_code_str")
    #         tz_abbreviation = mine_current_datetime.strftime('%Z')

    #         feet_mined_graph_points = []
    #         total_feet_mined = 0
    #         current_date = from_date
    #         total_hours = hours_since_given_date(from_date)
    #         end_date = to_date
    #         total_downtime = 0
    #         most_downtime_array = []
    #         downtime_window_dict = {}

    #         logger.info(f"mine_id: {mine_id}, from_date: {from_date}, to_date: {to_date}")
    #         while current_date <= end_date:
    #             coordinate =  get_feet_mined_in_day(productionDataManager_obj,mine_id,current_date)
    #             total_downtime += get_downtime_in_single_day(productionDataManager_obj,mine_id,current_date)
    #             most_downtime = get_most_downtime(productionDataManager_obj,mine_id,current_date)
    #             most_downtime_array.append(most_downtime)
    #             current_date += timedelta(days=1)
    #             total_feet_mined += coordinate[1]
    #             feet_mined_graph_points.append({"date": coordinate[0], "feetMined": coordinate[1]})

    #         logger.debug(f"total_downtime: {total_downtime},feet_mined_graph_points: {feet_mined_graph_points}")

    #         sectionwiseGraphData  = {}
    #         section_wise_coordinates = []
    #         sections, shifts  = get_sections(productionDataManager_obj,mine_id)
    #         logger.info(f"sections: {sections},")
    #         logger.info(f"shifts: {shifts}")

    #         section_list = []
    #         shift_list = []

    #         for section in sections:
    #             section_dict = {}
    #             section_id = section.get("sectionId")
    #             section_list.append(section_id)
    #             section_dict["sectionId"] = section_id
    #             section_name = section.get("sectionName")
    #             section_dict["sectionName"] = section_name

    #             for shift in shifts:
    #                 shift_name = shift.get("shiftName")
    #                 # shift_name_list.append(shift_name)
    #                 section_dict[shift_name] = 0

    #             total_feet = 0

    #             for shift in shifts:
    #                 shift_id = shift.get("shiftId")
    #                 shift_list.append(shift_id)
    #                 shift_name = shift.get("shiftName")
    #                 feet_mined = get_section_wise_feetmined(productionDataManager_obj,mine_id, section_id, shift_id, from_date,to_date)

    #                 if feet_mined > 0:
    #                     section_dict["sectionId"] = section_id 
    #                     section_dict["sectionName"] = section_name
    #                     if shift_name in section_dict.keys():
    #                         section_dict[shift_name] +=  feet_mined
    #                     else:
    #                         section_dict[shift_name] =  feet_mined

    #                 elif feet_mined == 0:
    #                     section_dict["sectionName"] = section_name
    #                     section_dict["sectionId"] = section_id
    #                     section_dict[shift_name] = feet_mined

    #             for key, value in section_dict.items():
    #                 if key not in ["sectionName","sectionId"] :
    #                     total_feet += value

    #             section_dict["totalMined"] = total_feet
    #             section_wise_coordinates.append(section_dict)

    #         no_of_days = (abs((to_date - from_date).days)) + 1
    #         working_hours = working_hours_in_a_day(shifts) * len(section_list) * no_of_days

    #         shift_name_list = []
    #         shifts = sorted(shifts, key=lambda x: x['startTime'])
    #         for shift in shifts:
    #             shift_name = shift.get("shiftName")
    #             shift_name_list.append(shift_name)
    #         sectionwiseGraphData["shiftNameList"] = shift_name_list

    #         sectionwiseGraphData["coordinates"] = section_wise_coordinates
    #         sectionwiseGraphData["xAxisPoints"] = [section.get("sectionName") for section in sections]
    #         feet_mined_graph_data = {"coordinates": feet_mined_graph_points}
    #         total_downtime_mins = round(total_downtime/ SECONDS_IN_HOUR,2)
    #         logger.debug("total_feet_mined, working_hours %s %s", total_feet_mined, working_hours)

    #         feet_mined_per_hour = 0
    #         if working_hours is not None and working_hours != 0:
    #             feet_mined_per_hour = total_feet_mined / working_hours

    #         for d in most_downtime_array:
    #             for key, value in d.items():
    #                 downtime_window_dict[key] = downtime_window_dict.get(key, 0) + value

    #         if not all(not d for d in most_downtime_array)>0:

    #             max_key = max(downtime_window_dict, key=downtime_window_dict.get)
    #             max_key_index = int(max_key[-1])
    #             most_downtime_window_start = (max_key_index*2)-2
    #             most_downtime_window_end = max_key_index*2
    #             most_downtime_meridiem = "AM"

    #             if most_downtime_window_start > 12:
    #                 most_downtime_window_start -= 12
    #                 most_downtime_meridiem = "PM"

    #             if most_downtime_window_end > 12:
    #                 most_downtime_window_end -= 12
    #                 most_downtime_meridiem = "PM"

    #             most_downtime_window = f"{most_downtime_window_start}-{most_downtime_window_end}"

    #         else:
    #             most_downtime_window = None
    #             most_downtime_meridiem = None

    #         activity_object = get_activity_metrics(productionDataManager_obj,mine_id, from_date, to_date, section_list,set(shift_list),total_feet_mined)
    #         inspection_downtime = {"feets":activity_object.get("inspection_downtime",0)}
    #         planned_downtime = {"feets":activity_object.get("planned_downtime",0)}
    #         unplanned_downtime = {"feets":activity_object.get("unplanned_downtime",0)}
    #         on_time = {"feets":activity_object.get("on_time",0)}
    #         average_goal = activity_object.get("average_goal",0)
    #         last_report = activity_object.get("last_report",None)

    #         last_report = (
    #             (convert_dt_to_tz(last_report, mine_timezone)).replace(
    #                 tzinfo=None
    #             )
    #             if last_report
    #             else None
    #         )

    #         logger.info(f"activity object : {activity_object}")

    #         activity_metrics = {
    #             "lastReport": last_report,
    #             "tzCode": tz_code,
    #             "tzAbbreviation": tz_abbreviation,
    #             "inspectionDowntime": inspection_downtime,
    #             "plannedDowntime": planned_downtime,
    #             "unplannedDowntime": unplanned_downtime,
    #             "onTime": on_time,
    #         }

    #         result = ReportMineSchema(feetMinedGraphData= feet_mined_graph_data,totalDowntime=total_downtime_mins, mostDowntime = most_downtime_window,
    #                                 mostDowntimeMeridiem=most_downtime_meridiem,  totalDowntimeUnit= "hrs", totalFeetMined=total_feet_mined,
    #                                 feetMinedPerHour=round(feet_mined_per_hour,2), feetMinedPerHourUnit = "ft", totalFeetMinedUnit = "ft", sectionwiseGraphData= sectionwiseGraphData, 
    #                                 averageGoal= average_goal,activityMetrics=activity_metrics)

    #         return result, None

    #     except Exception as e:
    #         logger.error(f"Error in get_report_mine_data: {e}", exc_info=True)
    #         return None, APIError(500, APIErrorType.API_ERROR, f"Error {e} in get_report_mine_data")



    def get_compare_report_mine_data(self, mine_id,from_date1, to_date1, from_date2, to_date2) -> tuple[ReportMineCompareSchema, APIError]:
        """_summary_: Returns data for two date ranges and comparative metrics

        Args:
            mine_id (_int_): id of the mine
            from_date1 (_datetime.date_): start date of date range 1
            to_date1 (_datetime.date_): end date of date range 1
            from_date2 (_datetime.date_): start date of date range 1
            to_date2 (_datetime.date_): end date of date range 1

        Returns:
            _dict_: data related to both time range and comparative metrics
        """
        try:
            productionDataManager_obj = ProductionDataManager(self.session)
            logger.info(f"mine_id: {mine_id}, from_date1: {from_date1}, to_date1: {to_date1},from_date2: {from_date2},to_date2 : {to_date2}")


            mine_dict = get_mine_with_timezone(productionDataManager_obj, mine_id)
            logger.info(f"mine_id: {mine_id}")
            if mine_dict is None :
                logger.error("Error in get_compare_report_mine_data: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")


            # Mine's Timezone related code
            mine_timezone = mine_dict.get("tz_code") or pytz.utc
            mine_current_datetime = datetime.now(mine_timezone)
            logger.debug(f"Mine's Current_datetime  : {mine_current_datetime}, tz : {mine_timezone}")

            tz_code = mine_dict.get("tz_code_str")
            tz_abbreviation = mine_current_datetime.strftime('%Z')


            result1 = self.get_report_mine_data(mine_id,from_date1,to_date1)
            result2 = self.get_report_mine_data(mine_id,from_date2,to_date2,)
            section_mined_date_range1 = get_section_graph_points(productionDataManager_obj,mine_id,from_date1,to_date1)
            section_mined_date_range2 = get_section_graph_points(productionDataManager_obj,mine_id,from_date2,to_date2)
            section_mined_graph_points = []

            most_downtime1 = dict(result1[0]).get("mostDowntime")
            most_downtime_meridiem1 = dict(result1[0]).get("mostDowntimeMeridiem")
            most_downtime2 = dict(result2[0]).get("mostDowntime")
            most_downtime_meridiem2 = dict(result2[0]).get("mostDowntimeMeridiem")

            most_downtime = {"most_downtime1" : most_downtime1, "most_downtime_meridiem1":most_downtime_meridiem1,
                             "most_downtime2" : most_downtime2, "most_downtime_meridiem2":most_downtime_meridiem2}

            for entry1, entry2 in zip_longest(section_mined_date_range1, section_mined_date_range2, fillvalue={}):
                section_name = entry1.get('section_name', entry2.get('section_name'))
                section_id = entry1.get('section_id')

                if not section_id:
                    section_id = entry2.get('section_id')

                section_mined_graph_points.append({
                    "sectionId" : section_id,
                    "sectionName" : section_name,
                    'feetMinedDataRange1': entry1.get('total_feet_mined', 0),
                    'feetMinedDataRange2': entry2.get('total_feet_mined', 0)
                })

            total_feet_mined1 = dict(result1[0]).get("totalFeetMined",0)
            total_feet_mined2 = dict(result2[0]).get("totalFeetMined",0)
            feet_mined_delta = total_feet_mined2 - total_feet_mined1
            if total_feet_mined1 > 0 :
                feet_mined_delta_percentage = (feet_mined_delta/total_feet_mined1)*100
                feet_mined_delta_percentage = round(feet_mined_delta_percentage,2)
            else:
                feet_mined_delta_percentage = None

            feet_mined_per_hour1  = dict(result1[0]).get("feetMinedPerHour",0)
            feet_mined_per_hour2  = dict(result2[0]).get("feetMinedPerHour",0)
            feet_mined_per_hour_delta = feet_mined_per_hour2 - feet_mined_per_hour1
            if feet_mined_per_hour1 > 0:
                feet_mined_per_hour_delta_percentage = (feet_mined_per_hour_delta/feet_mined_per_hour1)*100
                feet_mined_per_hour_delta_percentage = round(feet_mined_per_hour_delta_percentage,2)
            else:
                feet_mined_per_hour_delta_percentage = None

            total_downtime1 = dict(result1[0]).get("totalDowntime",0)
            total_downtime2 = dict(result2[0]).get("totalDowntime",0)
            downtime_delta = total_downtime2 - total_downtime1
            if total_downtime1 > 0 :
                downtime_delta_percentage = (downtime_delta/total_downtime1)*100
                downtime_delta_percentage = round(downtime_delta_percentage,2)
            else:
                downtime_delta_percentage = None

            feet_mined_graph_data1 = dict(result1[0]).get("feetMinedGraphData")
            feet_mined_graph_data2 = dict(result2[0]).get("feetMinedGraphData")

            mined = { "totalFeetMined1" : total_feet_mined1, "totalFeetMined2" : total_feet_mined2,"delta": feet_mined_delta_percentage, "minedUnit":"ft" }
            logger.info(f"mined: {mined}")

            feet_mined_per_hour = {"feetMinedPerHour1":round(feet_mined_per_hour1,2), "feetMinedPerHour2":round(feet_mined_per_hour2,2), "delta":feet_mined_per_hour_delta_percentage,
                                    "feetMinedPerHourUnit" : "FMPH"}
            logger.info(f"feet_mined_per_hour: {feet_mined_per_hour}")

            total_downtime = {"total_downtime1":total_downtime1, "total_downtime2":total_downtime2,
                            "delta":downtime_delta_percentage,"totalDowntimeUnit" : "hrs"}
            logger.info(f"total_downtime: {total_downtime}")

            feet_mined_graph_data = {"feetMinedGraphData1": feet_mined_graph_data1, "feetMinedGraphData2":feet_mined_graph_data2}

            #activity Calculations
            section_list = []
            shift_list = []
            sections, shifts  = get_sections(productionDataManager_obj,mine_id)

            for section in sections:
                section_id = section.get("sectionId")
                section_list.append(section_id)

            for shift in shifts:
                shift_id = shift.get("shiftId")
                shift_list.append(shift_id)

            activity_object1 = get_activity_metrics(productionDataManager_obj,mine_id, from_date1, to_date1, section_list,shift_list,total_feet_mined1)
            inspection_downtime1 = {"feets":activity_object1.get("inspection_downtime",0)}
            planned_downtime1 = {"feets":activity_object1.get("planned_downtime",0)}
            unplanned_downtime1 = {"feets":activity_object1.get("unplanned_downtime",0)}
            on_time1 = {"feets":activity_object1.get("on_time",0)}
            average_goal1 = activity_object1.get("average_goal",0)
            last_report1 = activity_object1.get("last_report",None)
            
            last_report1 = (
                (convert_dt_to_tz(last_report1, mine_timezone)).replace(
                    tzinfo=None
                )
                if last_report1
                else None
            )

            activity_object2 = get_activity_metrics(productionDataManager_obj,mine_id, from_date2, to_date2, section_list,shift_list,total_feet_mined2)
            inspection_downtime2 = {"feets":activity_object2.get("inspection_downtime",0)}
            planned_downtime2 = {"feets":activity_object2.get("planned_downtime",0)}
            unplanned_downtime2 = {"feets":activity_object2.get("unplanned_downtime",0)}
            on_time2 = {"feets":activity_object2.get("on_time",0)}
            average_goal2 = activity_object2.get("average_goal",0)
            last_report2 = activity_object2.get("last_report",None)

            last_report2 = (
                (convert_dt_to_tz(last_report2, mine_timezone)).replace(
                    tzinfo=None
                )
                if last_report2
                else None
            )

            activity_metrics1 = {"lastReport":last_report1, "tzCode": tz_code, "tzAbbreviation": tz_abbreviation, "inspectionDowntime":inspection_downtime1,"plannedDowntime": planned_downtime1, "unplannedDowntime":unplanned_downtime1,"onTime":on_time1 }
            activity_metrics2 = {"lastReport":last_report2, "tzCode": tz_code, "tzAbbreviation": tz_abbreviation, "inspectionDowntime":inspection_downtime2,"plannedDowntime": planned_downtime2, "unplannedDowntime":unplanned_downtime2,"onTime":on_time2 }

            result = ReportMineCompareSchema(mined= mined,feetMinedPerHour= feet_mined_per_hour,totalDowntime=total_downtime,feetMinedGraphData=feet_mined_graph_data,sectionMinedGraphData=section_mined_graph_points,
                                             mostDowntime=most_downtime,averageGoal1 = average_goal1,averageGoal2 = average_goal2,activityMetrics1 = activity_metrics1,activityMetrics2 = activity_metrics2)

            return result, None

        except Exception as e:
            logger.error(f"Error in get_compare_report_mine_data: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.API_ERROR, f"Error {e} in get_compare_report_mine_data")

    def get_report_mine_data(self, mine_id,from_date, to_date) -> tuple[ReportMineSchema, APIError]:
        """_summary_ : Returns details regarding mine data in given time range

        Args:
            mine_id (_int_): mine id 
            from_date (_datetime.date_): Starting date
            to_date (_datetime.datetime_): To date

        Returns:
            _tuple_: returns ReportMineSchema 
        """
        try:
            productionDataManager_obj = ProductionDataManager(self.session)

            mine_dict = get_mine_with_timezone(productionDataManager_obj, mine_id)
            logger.info(f"mine_id: {mine_id}")
            if mine_dict is None :
                logger.error("Error in get_report_mine_data: Mine not found.")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")

            # Mine's Timezone related code
            mine_timezone = mine_dict.get("tz_code") or pytz.utc
            mine_current_datetime = datetime.now(mine_timezone)
            logger.debug(f"Mine's Current_datetime  : {mine_current_datetime}, tz : {mine_timezone}")

            tz_code = mine_dict.get("tz_code_str")
            tz_abbreviation = mine_current_datetime.strftime('%Z')

            feet_mined_graph_points = []
            total_feet_mined = 0
            current_date = from_date
            total_hours = hours_since_given_date(from_date)
            end_date = to_date
            total_downtime = 0
            most_downtime_array = []
            downtime_window_dict = {}

            logger.info(f"mine_id: {mine_id}, from_date: {from_date}, to_date: {to_date}")
            while current_date <= end_date:
                coordinate =  get_feet_mined_in_day(productionDataManager_obj,mine_id,current_date)
                total_downtime += get_downtime_in_single_day(productionDataManager_obj,mine_id,current_date)
                most_downtime = get_most_downtime(productionDataManager_obj,mine_id,current_date)
                most_downtime_array.append(most_downtime)
                current_date += timedelta(days=1)
                total_feet_mined += coordinate[1]
                feet_mined_graph_points.append({"date": coordinate[0], "feetMined": coordinate[1]})

            logger.debug(f"total_downtime: {total_downtime},feet_mined_graph_points: {feet_mined_graph_points}")

            sectionwiseGraphData  = {}
            section_wise_coordinates = []
            sections, shifts  = get_sections(productionDataManager_obj,mine_id)
            # logger.info(f"sections: {sections},")
            # logger.info(f"shifts: {shifts}")

            section_list = []
            shift_list = []

            for section in sections:
                section_dict = {}
                section_id = section.get("sectionId")
                section_list.append(section_id)
                section_dict["sectionId"] = section_id
                section_name = section.get("sectionName")
                section_dict["sectionName"] = section_name
                data = []

                for shift in shifts:
                    shift_id = shift.get("shiftId")
                    shift_list.append(shift_id)
                    feet_mined = get_section_wise_feetmined(productionDataManager_obj,mine_id, section_id, shift_id, from_date,to_date)
                    if feet_mined > 0 :
                        shift_data = {}
                        shift_data["shiftId"] = shift_id
                        shift_data["shiftData"] = feet_mined
                        data.append(shift_data)

                section_dict["totalMined"] = sum(item["shiftData"] for item in data)
                section_dict["data"] = data
                section_wise_coordinates.append(section_dict)

            no_of_days = (abs((to_date - from_date).days)) + 1
            working_hours = working_hours_in_a_day(shifts) * len(section_list) * no_of_days

            all_shift_with_data = []
            for coordinate in section_wise_coordinates:
                shift_with_data = [item["shiftId"] for item in coordinate["data"] if item["shiftData"] > 0]
                all_shift_with_data.extend(shift_with_data)
            shift_with_no_data = set(shift_list) - set(all_shift_with_data)

            logger.debug("All shifts list :  %s , Shifts with no data:  %s, Shifts with data:  %s", set(shift_list), shift_with_no_data, set(all_shift_with_data))

            shift_name_list = []
            shifts = sorted(shifts, key=lambda x: x['startTime'])
            for shift in shifts:
                shift_obj = {}
                shift_obj["shiftName"] = shift.get("shiftName")
                shift_obj["shiftId"] = shift.get("shiftId")
                if shift.get("shiftId") in all_shift_with_data and shift_obj not in shift_name_list:
                    shift_name_list.append(shift_obj)

            sectionwiseGraphData["shiftNameList"] = shift_name_list

            logger.debug("shift_name_list :  %s ", shift_name_list)

            # shift_data_list = [shift for shift in shift_name_list if shift["shiftId"] not in shift_with_no_data]

            sectionwiseGraphData["coordinates"] = section_wise_coordinates
            sectionwiseGraphData["xAxisPoints"] = [section.get("sectionName") for section in sections]
            feet_mined_graph_data = {"coordinates": feet_mined_graph_points}
            total_downtime_mins = round(total_downtime/ SECONDS_IN_HOUR,2)
            logger.debug("total_feet_mined, working_hours %s %s", total_feet_mined, working_hours)

            feet_mined_per_hour = 0
            if working_hours is not None and working_hours != 0:
                feet_mined_per_hour = total_feet_mined / working_hours

            for d in most_downtime_array:
                for key, value in d.items():
                    downtime_window_dict[key] = downtime_window_dict.get(key, 0) + value

            if not all(not d for d in most_downtime_array)>0:

                max_key = max(downtime_window_dict, key=downtime_window_dict.get)
                max_key_index = int(max_key[-1])
                most_downtime_window_start = (max_key_index*2)-2
                most_downtime_window_end = max_key_index*2
                most_downtime_meridiem = "AM"

                if most_downtime_window_start > 12:
                    most_downtime_window_start -= 12
                    most_downtime_meridiem = "PM"

                if most_downtime_window_end > 12:
                    most_downtime_window_end -= 12
                    most_downtime_meridiem = "PM"

                most_downtime_window = f"{most_downtime_window_start}-{most_downtime_window_end}"

            else:
                most_downtime_window = None
                most_downtime_meridiem = None

            activity_object = get_activity_metrics(productionDataManager_obj,mine_id, from_date, to_date, section_list,set(shift_list),total_feet_mined)
            inspection_downtime = {"feets":activity_object.get("inspection_downtime",0)}
            planned_downtime = {"feets":activity_object.get("planned_downtime",0)}
            unplanned_downtime = {"feets":activity_object.get("unplanned_downtime",0)}
            on_time = {"feets":activity_object.get("on_time",0)}
            average_goal = activity_object.get("average_goal",0)
            last_report = activity_object.get("last_report",None)

            last_report = (
                (convert_dt_to_tz(last_report, mine_timezone)).replace(
                    tzinfo=None
                )
                if last_report
                else None
            )

            logger.info(f"activity object : {activity_object}")

            activity_metrics = {
                "lastReport": last_report,
                "tzCode": tz_code,
                "tzAbbreviation": tz_abbreviation,
                "inspectionDowntime": inspection_downtime,
                "plannedDowntime": planned_downtime,
                "unplannedDowntime": unplanned_downtime,
                "onTime": on_time,
            }

            result = ReportMineSchema(feetMinedGraphData= feet_mined_graph_data,totalDowntime=total_downtime_mins, mostDowntime = most_downtime_window,
                                    mostDowntimeMeridiem=most_downtime_meridiem,  totalDowntimeUnit= "hrs", totalFeetMined=total_feet_mined,
                                    feetMinedPerHour=round(feet_mined_per_hour,2), feetMinedPerHourUnit = "ft", totalFeetMinedUnit = "ft", sectionwiseGraphData= sectionwiseGraphData, 
                                    averageGoal= average_goal,activityMetrics=activity_metrics)

            return result, None

        except Exception as e:
            logger.error(f"Error in get_report_mine_data: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.API_ERROR, f"Error {e} in get_report_mine_data")





    def get_report_shift_data(self, mine_id,selected_date) -> tuple[ReportShiftSchema, APIError]:
        """_summary_ : Data metrics related to shifts in sections

        Args:
            mine_id (_int_): id of the mine
            selected_date (_datetime.date_): date 

        Returns:
            tuple[ReportShiftSchema, APIError]: Data metrics related to shifts in sections
        """
        try:
            LocationDataManager_obj = LocationDataManager(self.session)
            mine_check = check_mine_exists(LocationDataManager_obj, mine_id)
            logger.info(f"mine_id: {mine_id}")
            if not mine_check:
                logger.error(f"Error in get_sections_report: Mine check failed: {not mine_check}")
                return None, APIError(404, APIErrorType.MINE_NOT_FOUND, "Mine not found")

            productionDataManager_obj = ProductionDataManager(self.session)
            sections, shifts  = get_sections(productionDataManager_obj,mine_id)
            # Uncomment following line if Maintenance shift needed included
            # shifts = get_shifts_include_maintenance(productionDataManager_obj,mine_id)
            
            # logger.info(f"Shift: {shifts}")
            # logger.info(f"section: {sections}")
            res = []
            shifts_to_display = []
            for section in sections:
                for shift in shifts:
                    section_id = section.get("sectionId")
                    shift_id = shift.get("shiftId")
                    is_active = shift.get("is_active")
                    metrics, graph_data = get_report_shift_metrics(productionDataManager_obj,mine_id, section_id, shift_id,selected_date)
                    if len(graph_data) > 0 :
                        graph_data.insert(0,{"hour":shift.get("shiftHours")[0],"feetMined":0})

                    goal = calculate_shift_goal(productionDataManager_obj,selected_date,section_id, shift_id)
                    mined = metrics.get("mined")
                    if mined:
                        feet_mined_per_hour = metrics.get("mined") / shift.get("shiftDuration")
                        feet_mined_per_hour = round(feet_mined_per_hour,2)
                    else:
                        feet_mined_per_hour = None

                    total_downtime = metrics.get("total_down_time")
                    if total_downtime :
                        total_downtime_mins = total_downtime.hour * 60 + total_downtime.minute
                    else:
                        total_downtime_mins = None

                    res_dict = {"mined":metrics.get("mined"),"feetMinedPerHour":feet_mined_per_hour,"SectionName":section.get("sectionName"),
                                "shiftStartTime":shift.get("startTime"),"cuts": metrics.get("total_cuts"),
                                "downTimeEvents":metrics.get("total_down_events"), "totalDowntime":total_downtime_mins,
                                "onSection": metrics.get("on_section"),"sectionId":section_id,"shiftId":shift_id,
                                "shiftName":shift.get("shiftName"), "shiftGraphData":graph_data,"shiftHours":shift.get("shiftHours"), "goal":goal,"shiftType":shift.get("shift_type")}
                    
                    if is_active:
                        res.append(res_dict)
                        if shift not in shifts_to_display:
                            shifts_to_display.append(shift)
                        logger.debug("Active shift data: %s", res_dict)
                    elif not is_active  and check_data_present_in_shift(res_dict):
                        logger.debug("Inactive shift data: %s", res_dict)
                        res.append(res_dict)
                        if shift not in shifts_to_display:
                            shifts_to_display.append(shift)

            result = ReportShiftSchema (sections= sections, shifts = shifts_to_display, shiftsMetrics = res)

            return result, None

        except Exception as e:
            logger.error(f"Error in get_report_shift_data: {e}", exc_info=True)
            return None, APIError(500, APIErrorType.API_ERROR, f"Error {e} in get_report_shift_data")