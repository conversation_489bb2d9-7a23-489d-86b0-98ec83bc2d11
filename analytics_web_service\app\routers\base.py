from fastapi import APIRouter, Depends, Path
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session


from backend.session import create_session
# from middleware import Api<PERSON>eyMiddleware
from const import (
    API_BASE_URL, BASE_TAGS
)
from fastapi import Security
from services.base import GetVersion
from app_loggers.loggers_config import get_logger
logger = get_logger(__name__)
from middleware import validate_api_key


router = APIRouter(dependencies=[Security(validate_api_key)],tags= BASE_TAGS)


@router.get("/version")
async def get_repo_version(session: Session = Depends(create_session))-> str :
    """Get Version."""
    result, api_error = GetVersion(session).get_repo_version()
    if api_error is not None:
        return JSONResponse(status_code=api_error.status, content=api_error.to_dict())
    return result